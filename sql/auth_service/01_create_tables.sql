-- 认证服务数据库初始化脚本
-- Game Microservices Auth Service Database Schema
-- 版本: 1.0.0
-- 作者: 29108
-- 日期: 2025/7/21

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS game_microservices 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE game_microservices;

-- ==================== 用户基础信息表 ====================
CREATE TABLE IF NOT EXISTS users (
    user_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    nickname VARCHAR(100) NOT NULL COMMENT '昵称',
    avatar_url VARCHAR(500) DEFAULT '' COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '用户状态：0-未激活，1-活跃，2-暂停，3-封禁，4-已删除',
    online_status TINYINT DEFAULT 0 COMMENT '在线状态：0-离线，1-在线，2-离开，3-忙碌，4-隐身',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) DEFAULT '' COMMENT '最后登录IP',
    login_attempts INT DEFAULT 0 COMMENT '登录尝试次数',
    locked_until TIMESTAMP NULL COMMENT '锁定到期时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_online_status (online_status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_login_at (last_login_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- ==================== 用户角色表 ====================
CREATE TABLE IF NOT EXISTS user_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授予时间',
    granted_by BIGINT DEFAULT 0 COMMENT '授予者用户ID',
    
    UNIQUE KEY uk_user_role (user_id, role_name),
    INDEX idx_user_id (user_id),
    INDEX idx_role_name (role_name),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

-- ==================== 用户元数据表 ====================
CREATE TABLE IF NOT EXISTS user_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    meta_key VARCHAR(100) NOT NULL COMMENT '元数据键',
    meta_value TEXT COMMENT '元数据值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_meta (user_id, meta_key),
    INDEX idx_user_id (user_id),
    INDEX idx_meta_key (meta_key),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户元数据表';

-- ==================== 游戏用户数据表 ====================
CREATE TABLE IF NOT EXISTS game_user_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    game_type TINYINT NOT NULL COMMENT '游戏类型：1-贪吃蛇，2-俄罗斯方块，3-象棋，4-扑克，5-RPG，6-MOBA，7-FPS，8-策略',
    level INT DEFAULT 1 COMMENT '游戏等级',
    experience BIGINT DEFAULT 0 COMMENT '经验值',
    coins BIGINT DEFAULT 0 COMMENT '游戏币',
    gems BIGINT DEFAULT 0 COMMENT '宝石数量',
    total_games INT DEFAULT 0 COMMENT '总游戏次数',
    wins INT DEFAULT 0 COMMENT '胜利次数',
    losses INT DEFAULT 0 COMMENT '失败次数',
    draws INT DEFAULT 0 COMMENT '平局次数',
    total_playtime_seconds BIGINT DEFAULT 0 COMMENT '总游戏时间（秒）',
    best_score BIGINT DEFAULT 0 COMMENT '最高分数',
    last_played_at TIMESTAMP NULL COMMENT '最后游戏时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_game (user_id, game_type),
    INDEX idx_user_id (user_id),
    INDEX idx_game_type (game_type),
    INDEX idx_level (level),
    INDEX idx_experience (experience),
    INDEX idx_last_played_at (last_played_at),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏用户数据表';

-- ==================== 游戏用户成就表 ====================
CREATE TABLE IF NOT EXISTS game_user_achievements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    game_type TINYINT NOT NULL COMMENT '游戏类型',
    achievement_name VARCHAR(100) NOT NULL COMMENT '成就名称',
    achieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    
    UNIQUE KEY uk_user_achievement (user_id, game_type, achievement_name),
    INDEX idx_user_id (user_id),
    INDEX idx_game_type (game_type),
    INDEX idx_achievement_name (achievement_name),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏用户成就表';

-- ==================== 游戏用户自定义数据表 ====================
CREATE TABLE IF NOT EXISTS game_user_custom_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    game_type TINYINT NOT NULL COMMENT '游戏类型',
    data_key VARCHAR(100) NOT NULL COMMENT '数据键',
    data_value TEXT COMMENT '数据值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_game_data (user_id, game_type, data_key),
    INDEX idx_user_id (user_id),
    INDEX idx_game_type (game_type),
    INDEX idx_data_key (data_key),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏用户自定义数据表';

-- ==================== 游戏服务器信息表 ====================
CREATE TABLE IF NOT EXISTS game_servers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    server_id VARCHAR(100) NOT NULL UNIQUE COMMENT '服务器唯一标识',
    server_name VARCHAR(200) NOT NULL COMMENT '服务器名称',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口号',
    game_type TINYINT NOT NULL COMMENT '支持的游戏类型',
    region VARCHAR(50) DEFAULT '' COMMENT '服务器区域',
    current_players INT DEFAULT 0 COMMENT '当前玩家数',
    max_players INT DEFAULT 100 COMMENT '最大玩家数',
    cpu_usage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'CPU使用率（0-100）',
    memory_usage DECIMAL(5,2) DEFAULT 0.00 COMMENT '内存使用率（0-100）',
    is_healthy BOOLEAN DEFAULT TRUE COMMENT '健康状态',
    is_accepting_players BOOLEAN DEFAULT TRUE COMMENT '是否接受新玩家',
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后心跳时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_server_id (server_id),
    INDEX idx_game_type (game_type),
    INDEX idx_region (region),
    INDEX idx_is_healthy (is_healthy),
    INDEX idx_is_accepting_players (is_accepting_players),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏服务器信息表';

-- ==================== 游戏服务器元数据表 ====================
CREATE TABLE IF NOT EXISTS game_server_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    server_id VARCHAR(100) NOT NULL COMMENT '服务器ID',
    meta_key VARCHAR(100) NOT NULL COMMENT '元数据键',
    meta_value TEXT COMMENT '元数据值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_server_meta (server_id, meta_key),
    INDEX idx_server_id (server_id),
    INDEX idx_meta_key (meta_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏服务器元数据表';

-- ==================== 用户会话表（用于持久化重要会话信息） ====================
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    session_id VARCHAR(100) NOT NULL UNIQUE COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    device_type VARCHAR(50) DEFAULT '' COMMENT '设备类型',
    device_id VARCHAR(200) DEFAULT '' COMMENT '设备唯一标识',
    client_ip VARCHAR(45) DEFAULT '' COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '用户代理字符串',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_device_id (device_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active),
    INDEX idx_last_activity_at (last_activity_at),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- ==================== 安全事件日志表 ====================
CREATE TABLE IF NOT EXISTS security_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT DEFAULT 0 COMMENT '用户ID（0表示系统事件）',
    event_type VARCHAR(100) NOT NULL COMMENT '事件类型',
    description TEXT COMMENT '事件描述',
    client_ip VARCHAR(45) DEFAULT '' COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理字符串',
    additional_data JSON COMMENT '附加数据（JSON格式）',
    severity TINYINT DEFAULT 1 COMMENT '严重程度：1-信息，2-警告，3-错误，4-严重',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at),
    INDEX idx_client_ip (client_ip)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安全事件日志表';

-- ==================== 设备信息表 ====================
CREATE TABLE IF NOT EXISTS user_devices (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    device_id VARCHAR(200) NOT NULL COMMENT '设备唯一标识',
    device_name VARCHAR(200) DEFAULT '' COMMENT '设备名称',
    device_type VARCHAR(50) DEFAULT '' COMMENT '设备类型',
    os_name VARCHAR(100) DEFAULT '' COMMENT '操作系统名称',
    os_version VARCHAR(100) DEFAULT '' COMMENT '操作系统版本',
    browser_name VARCHAR(100) DEFAULT '' COMMENT '浏览器名称',
    browser_version VARCHAR(100) DEFAULT '' COMMENT '浏览器版本',
    screen_resolution VARCHAR(50) DEFAULT '' COMMENT '屏幕分辨率',
    timezone VARCHAR(100) DEFAULT '' COMMENT '时区',
    is_trusted BOOLEAN DEFAULT FALSE COMMENT '是否受信任设备',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次见到时间',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后见到时间',
    
    UNIQUE KEY uk_user_device (user_id, device_id),
    INDEX idx_user_id (user_id),
    INDEX idx_device_id (device_id),
    INDEX idx_device_type (device_type),
    INDEX idx_is_trusted (is_trusted),
    INDEX idx_last_seen (last_seen),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备信息表';
