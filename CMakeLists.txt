cmake_minimum_required(VERSION 3.10)
project(game_microservices)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)


# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pthread")


# ==================== 平台检测 ====================
if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    message(STATUS "Target platform: Windows")
    set(PLATFORM_WINDOWS TRUE)
elseif(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    message(STATUS "Target platform: Linux")
    set(PLATFORM_LINUX TRUE)
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
    message(STATUS "Target platform: macOS")
    set(PLATFORM_MACOS TRUE)
else()
    message(STATUS "Target platform: ${CMAKE_SYSTEM_NAME}")
    set(PLATFORM_LINUX TRUE)  # 默认假设是类Unix系统
endif()

# ==================== 编译选项配置 ====================
if(PLATFORM_WINDOWS)
    # Windows特定设置
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    add_definitions(-DWIN32_LEAN_AND_MEAN -DNOMINMAX -DPLATFORM_WINDOWS)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-unused-parameter")
else()
    # Linux/Unix特定设置
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pthread")
    add_definitions(-DPLATFORM_LINUX)
endif()


# 第三方库
find_package(OpenSSL REQUIRED)
find_package(CURL REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread)
find_package(Protobuf REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(nlohmann_json REQUIRED)

# 添加子目录
add_subdirectory(src/common)
add_subdirectory(src/core_services)
add_subdirectory(src/game_services)

# ==================== 库变量定义 ====================
# 为测试提供库变量
set(CONFIG_LIB temp_config_lib)
set(LOGGER_LIB temp_logger_lib)
set(THREAD_POOL_LIB temp_thread_pool_lib)
set(TASK_SCHEDULER_LIB temp_task_scheduler_lib)
set(DATABASE_LIB database_lib)  # 数据库库
set(NETWORK_LIB temp_network_lib)

# 单元测试
add_subdirectory(tests)

# 安装目标
install(DIRECTORY ${CMAKE_BINARY_DIR}/bin/
        DESTINATION bin
        USE_SOURCE_PERMISSIONS)
