/**
 * @file service_discovery.h
 * @brief API网关服务发现功能
 * @details 提供服务注册、发现、健康检查等功能
 * 
 * <AUTHOR>
 * @date 2025/7/25
 */

#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <chrono>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include "core_services/api_gateway/load_balancer.h"
#include "common/http/http_client.h"

namespace core_services {
namespace api_gateway {

    /**
     * @brief 服务发现管理器
     * @details 负责服务注册、发现、健康检查和自动故障转移
     */
    class ServiceDiscovery {
    public:
        /**
         * @brief 服务信息结构体
         */
        struct ServiceInfo {
            std::string service_name;                    ///< 服务名称
            std::string service_version;                 ///< 服务版本
            std::string host;                           ///< 服务主机
            int port;                                   ///< 服务端口
            std::string health_check_endpoint;          ///< 健康检查端点
            std::vector<std::string> endpoints;         ///< 服务端点列表
            std::chrono::system_clock::time_point last_heartbeat; ///< 最后心跳时间
            bool healthy = true;                        ///< 健康状态
            int weight = 1;                            ///< 负载均衡权重
            std::unordered_map<std::string, std::string> metadata; ///< 元数据
        };

        /**
         * @brief 配置结构体
         */
        struct Config {
            bool enable_service_discovery = true;       ///< 是否启用服务发现
            std::chrono::seconds health_check_interval{30}; ///< 健康检查间隔
            std::chrono::seconds service_timeout{60};   ///< 服务超时时间
            int health_check_timeout_ms = 2000;        ///< 健康检查超时时间（减少到2秒）
            int max_retry_count = 3;                   ///< 最大重试次数
            bool auto_deregister_unhealthy = true;     ///< 自动注销不健康服务
        };

        /**
         * @brief 构造函数
         * @param config 配置
         * @param load_balancer 负载均衡器
         */
        explicit ServiceDiscovery(const Config& config, 
                                std::shared_ptr<LoadBalancer> load_balancer);

        /**
         * @brief 析构函数
         */
        ~ServiceDiscovery();

        /**
         * @brief 启动服务发现
         */
        void start();

        /**
         * @brief 停止服务发现
         */
        void stop();

        /**
         * @brief 注册服务
         * @param service_info 服务信息
         * @return 注册成功返回true
         */
        bool registerService(const ServiceInfo& service_info);

        /**
         * @brief 注销服务
         * @param service_name 服务名称
         * @param host 主机地址
         * @param port 端口
         * @return 注销成功返回true
         */
        bool deregisterService(const std::string& service_name, 
                             const std::string& host, int port);

        /**
         * @brief 更新服务心跳
         * @param service_name 服务名称
         * @param host 主机地址
         * @param port 端口
         * @return 更新成功返回true
         */
        bool updateHeartbeat(const std::string& service_name, 
                           const std::string& host, int port);

        /**
         * @brief 获取服务列表
         * @param service_name 服务名称
         * @return 服务实例列表
         */
        std::vector<ServiceInfo> getServices(const std::string& service_name) const;

        /**
         * @brief 获取健康的服务列表
         * @param service_name 服务名称
         * @return 健康的服务实例列表
         */
        std::vector<ServiceInfo> getHealthyServices(const std::string& service_name) const;

        /**
         * @brief 检查服务是否存在
         * @param service_name 服务名称
         * @return 存在返回true
         */
        bool hasService(const std::string& service_name) const;

        /**
         * @brief 获取服务统计信息
         * @return 统计信息映射
         */
        std::unordered_map<std::string, size_t> getServiceStats() const;

    private:
        Config config_;                                 ///< 配置
        std::shared_ptr<LoadBalancer> load_balancer_;   ///< 负载均衡器
        
        // 服务注册表：service_name -> list of ServiceInfo
        std::unordered_map<std::string, std::vector<ServiceInfo>> services_;
        mutable std::mutex services_mutex_;             ///< 服务列表互斥锁

        // 健康检查相关
        std::unique_ptr<std::thread> health_check_thread_; ///< 健康检查线程
        std::atomic<bool> running_{false};              ///< 运行状态
        std::unique_ptr<common::http::HttpClient> http_client_; ///< HTTP客户端

        /**
         * @brief 健康检查循环
         */
        void healthCheckLoop();

        /**
         * @brief 检查单个服务实例的健康状态
         * @param service_info 服务信息
         * @return 健康返回true
         */
        bool checkServiceHealth(ServiceInfo& service_info);

        /**
         * @brief 更新负载均衡器中的服务实例
         * @param service_name 服务名称
         */
        void updateLoadBalancerInstances(const std::string& service_name);

        /**
         * @brief 清理过期服务
         */
        void cleanupExpiredServices();

        /**
         * @brief 生成服务实例唯一标识
         * @param host 主机地址
         * @param port 端口
         * @return 唯一标识字符串
         */
        std::string generateInstanceId(const std::string& host, int port) const;

        /**
         * @brief 获取健康的服务列表（内部方法，不加锁）
         * @param service_name 服务名称
         * @return 健康的服务实例列表
         */
        std::vector<ServiceInfo> getHealthyServicesInternal(const std::string& service_name) const;
    };

} // namespace api_gateway
} // namespace core_services
