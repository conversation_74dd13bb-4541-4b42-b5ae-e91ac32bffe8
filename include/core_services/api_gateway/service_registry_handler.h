/**
 * @file service_registry_handler.h
 * @brief API网关服务注册处理器
 * @details 处理服务注册、注销、心跳等HTTP请求
 * 
 * <AUTHOR>
 * @date 2025/7/25
 */

#pragma once

#include <memory>
#include <string>
#include "core_services/api_gateway/service_discovery.h"
#include "common/http/http_server.h"
#include "common/http/http_request.h"
#include "common/http/http_response.h"
#include "common/http/http_handler.h"
#include <nlohmann/json.hpp>

namespace core_services {
namespace api_gateway {

    /**
     * @brief 服务注册处理器
     * @details 提供HTTP端点来处理服务注册、注销、心跳等请求
     */
    class ServiceRegistryHandler {
    public:
        /**
         * @brief 构造函数
         * @param service_discovery 服务发现管理器
         */
        explicit ServiceRegistryHandler(std::shared_ptr<ServiceDiscovery> service_discovery);

        /**
         * @brief 析构函数
         */
        ~ServiceRegistryHandler() = default;

        /**
         * @brief 注册HTTP路由到服务器
         * @param http_server HTTP服务器
         */
        void registerRoutes(common::http::HttpServer* http_server);

    private:
        std::shared_ptr<ServiceDiscovery> service_discovery_; ///< 服务发现管理器

        /**
         * @brief 处理服务注册请求
         * @param req HTTP请求
         * @param res HTTP响应
         */
        void handleServiceRegister(const common::http::HttpRequest& req, 
                                 common::http::HttpResponse& res);

        /**
         * @brief 处理服务注销请求
         * @param req HTTP请求
         * @param res HTTP响应
         */
        void handleServiceDeregister(const common::http::HttpRequest& req, 
                                   common::http::HttpResponse& res);

        /**
         * @brief 处理服务心跳请求
         * @param req HTTP请求
         * @param res HTTP响应
         */
        void handleServiceHeartbeat(const common::http::HttpRequest& req, 
                                  common::http::HttpResponse& res);

        /**
         * @brief 处理服务列表查询请求
         * @param req HTTP请求
         * @param res HTTP响应
         */
        void handleServiceList(const common::http::HttpRequest& req, 
                             common::http::HttpResponse& res);

        /**
         * @brief 处理服务统计信息请求
         * @param req HTTP请求
         * @param res HTTP响应
         */
        void handleServiceStats(const common::http::HttpRequest& req, 
                              common::http::HttpResponse& res);

        /**
         * @brief 构建成功响应
         * @param message 消息内容
         * @param data 数据内容（可选）
         * @return JSON响应
         */
        nlohmann::json buildSuccessResponse(const std::string& message, 
                                          const nlohmann::json& data = nlohmann::json::object());

        /**
         * @brief 构建错误响应
         * @param error_code 错误代码
         * @param error_message 错误消息
         * @return JSON响应
         */
        nlohmann::json buildErrorResponse(const std::string& error_code, 
                                        const std::string& error_message);

        /**
         * @brief 验证服务注册请求
         * @param request_json 请求JSON
         * @return 验证成功返回true
         */
        bool validateServiceRegisterRequest(const nlohmann::json& request_json);

        /**
         * @brief 从JSON构建ServiceInfo
         * @param json JSON对象
         * @return ServiceInfo对象
         */
        ServiceDiscovery::ServiceInfo buildServiceInfoFromJson(const nlohmann::json& json);

        /**
         * @brief 将ServiceInfo转换为JSON
         * @param service_info ServiceInfo对象
         * @return JSON对象
         */
        nlohmann::json serviceInfoToJson(const ServiceDiscovery::ServiceInfo& service_info);

        /**
         * @brief 设置CORS头
         * @param res HTTP响应
         */
        void setCorsHeaders(common::http::HttpResponse& res);

        /**
         * @brief 设置JSON响应头
         * @param res HTTP响应
         */
        void setJsonHeaders(common::http::HttpResponse& res);
    };

} // namespace api_gateway
} // namespace core_services
