//
// Created by 29108 on 2025/7/15.
//

#ifndef API_GATEWAY_H
#define API_GATEWAY_H

#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <functional>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <set>
#include <vector>
#include <ctime>
#include <sys/stat.h>
#include <dirent.h>
#include <unistd.h>
#include <cstdint>

#include <climits>


#include "common/http/http_server.h"
#include "common/config/config_manager.h"
#include "common/thread_pool/thread_pool.h"

#include "circuit_breaker.h"
#include "load_balancer.h"
#include "rate_limiter.h"
#include "service_route.h"
#include "service_discovery.h"
#include "service_registry_handler.h"


namespace core_services {
    namespace api_gateway {

        /**
         * @brief API网关主类 - 统一入口，路由转发，认证鉴权，限流熔断
         * @details 作为微服务架构的统一入口点，提供完整的网关功能
         *
         * 核心功能：
         * - HTTP请求路由和转发
         * - 负载均衡和服务发现
         * - 限流和熔断保护
         * - 认证和授权
         * - 请求/响应转换
         * - 监控和日志记录
         */
        class ApiGateway {
        public:
            /**
             * @brief API网关配置结构体
             */
            struct Config {
                common::http::HttpServerConfig server_config;    ///< HTTP服务器配置
                std::vector<ServiceRoute> routes;                  ///< 路由配置列表
                RateLimiter::Config rate_limit_config;             ///< 限流配置
                CircuitBreaker::Config circuit_breaker_config;     ///< 熔断器配置
                LoadBalancer::Strategy load_balance_strategy;      ///< 负载均衡策略

                // 认证配置
                bool enable_auth = true;                           ///< 是否启用认证
                std::string jwt_secret = "default_secret";         ///< JWT密钥
                std::chrono::seconds jwt_expiry{3600};             ///< JWT过期时间
                std::vector<std::string> public_paths;             ///< 公开路径（无需认证）

                // 监控配置
                bool enable_metrics = true;                        ///< 是否启用指标收集
                bool enable_tracing = true;                        ///< 是否启用链路追踪
                std::string metrics_path = "/metrics";             ///< 指标暴露路径

                // 安全配置
                bool enable_cors = true;                           ///< 是否启用CORS
                std::vector<std::string> allowed_origins;          ///< 允许的来源
                std::vector<std::string> allowed_methods;          ///< 允许的HTTP方法
                std::vector<std::string> allowed_headers;          ///< 允许的请求头

                // 服务发现配置
                ServiceDiscovery::Config service_discovery_config; ///< 服务发现配置

                /**
                 * @brief 从ConfigManager加载配置
                 */
                static Config fromConfigManager();

                /**
                 * @brief 验证配置有效性
                 */
                bool validate() const;

            private:
                /**
                 * @brief 解析逗号分隔的字符串列表
                 * @param str 逗号分隔的字符串
                 * @return 解析后的字符串向量
                 */
                static std::vector<std::string> parseStringList(const std::string& str);
            };

            /**
             * @brief 构造函数
             * @param config API网关配置
             */
            explicit ApiGateway(const Config& config);

            /**
             * @brief 析构函数
             */
            ~ApiGateway();

            // ==================== 生命周期管理 ====================

            /**
             * @brief 启动API网关服务
             */
            void start();

            /**
             * @brief 停止API网关服务
             */
            void stop();

            /**
             * @brief 检查服务是否运行中
             * @return true表示运行中
             */
            bool isRunning() const;

            // ==================== 路由管理 ====================

            /**
             * @brief 添加服务路由
             * @param route 路由配置
             */
            void addServiceRoute(const ServiceRoute& route);

            /**
             * @brief 移除服务路由
             * @param service_name 服务名称
             */
            void removeServiceRoute(const std::string& service_name);

            /**
             * @brief 更新服务路由
             * @param route 新的路由配置
             */
            void updateServiceRoute(const ServiceRoute& route);

            /**
             * @brief 获取所有路由
             * @return 路由列表
             */
            std::vector<ServiceRoute> getAllRoutes() const;

            // ==================== 服务实例管理 ====================

            /**
             * @brief 注册服务实例
             * @param service_name 服务名称
             * @param instance 服务实例信息
             */
            void registerServiceInstance(const std::string& service_name, const LoadBalancer::ServiceInstance& instance);

            /**
             * @brief 验证服务注册与路由配置的一致性
             * @param service_name 服务名称
             * @param instance 服务实例
             */
            void validateServiceRegistration(const std::string& service_name, const LoadBalancer::ServiceInstance& instance);

            /**
             * @brief 注销服务实例
             * @param service_name 服务名称
             * @param instance_id 实例ID
             */
            void unregisterServiceInstance(const std::string& service_name, const std::string& instance_id);

            /**
             * @brief 更新服务实例健康状态
             * @param service_name 服务名称
             * @param instance_id 实例ID
             * @param healthy 健康状态
             */
            void updateInstanceHealth(const std::string& service_name, const std::string& instance_id, bool healthy);

            // ==================== 统计和监控 ====================

            /**
             * @brief 网关统计信息结构体
             */
            struct Statistics {
                uint64_t total_requests = 0;                      ///< 总请求数
                uint64_t successful_requests = 0;                 ///< 成功请求数
                uint64_t failed_requests = 0;                     ///< 失败请求数
                uint64_t error_requests = 0;                      ///< 错误请求数
                uint64_t not_found_requests = 0;                  ///< 404请求数
                uint64_t rate_limited_requests = 0;               ///< 被限流的请求数
                uint64_t rate_limit_rejections = 0;               ///< 限流拒绝数
                uint64_t circuit_breaker_open_requests = 0;       ///< 熔断器开启时的请求数
                uint64_t circuit_breaker_rejections = 0;          ///< 熔断器拒绝数
                uint64_t no_instance_errors = 0;                  ///< 无可用实例错误数
                uint64_t total_response_time = 0;                 ///< 总响应时间（毫秒）
                double average_response_time = 0.0;               ///< 平均响应时间
                std::chrono::system_clock::time_point start_time; ///< 启动时间

                /**
                 * @brief 计算成功率
                 */
                double getSuccessRate() const {
                    return total_requests > 0 ?
                        static_cast<double>(successful_requests) / total_requests : 0.0;
                }

                /**
                 * @brief 计算平均响应时间
                 */
                double getAverageResponseTime() const {
                    return total_requests > 0 ?
                        static_cast<double>(total_response_time) / total_requests : 0.0;
                }
            };

            /**
             * @brief 获取统计信息
             * @return 统计信息
             */
            Statistics getStatistics() const;

            /**
             * @brief 重置统计信息
             */
            void resetStatistics();

            // ==================== 配置管理 ====================

            /**
             * @brief 更新配置
             * @param new_config 新配置
             */
            void updateConfig(const Config& new_config);

            /**
             * @brief 获取当前配置
             * @return 当前配置
             */
            const Config& getConfig() const { return config_; }

        private:
            Config config_;                                        ///< 网关配置
            std::atomic<bool> running_{false};                    ///< 运行状态

            // 核心组件
            std::unique_ptr<common::http::HttpServer> http_server_;     ///< HTTP服务器
            std::unique_ptr<RouteManager> route_manager_;               ///< 路由管理器
            std::shared_ptr<LoadBalancer> load_balancer_;               ///< 负载均衡器
            std::unique_ptr<RateLimiter> rate_limiter_;                 ///< 限流器
            std::unique_ptr<CircuitBreaker> circuit_breaker_;           ///< 熔断器
            std::shared_ptr<ServiceDiscovery> service_discovery_;       ///< 服务发现管理器
            std::unique_ptr<ServiceRegistryHandler> registry_handler_;  ///< 服务注册处理器

            // 统计信息
            Statistics stats_;
            mutable std::shared_mutex stats_mutex_;

            // 线程池（用于异步处理）
            std::shared_ptr<common::thread_pool::ThreadPool> thread_pool_;

            /**
             * @brief 处理HTTP请求的主要逻辑
             * @param request HTTP请求
             * @param response HTTP响应
             */
            void handleRequest(const common::http::HttpRequest& request, common::http::HttpResponse& response);

            /**
             * @brief 转发请求到目标服务
             * @param route 路由配置
             * @param instance 服务实例
             * @param request 原始请求
             * @param response 响应对象
             */
            void forwardRequest(const ServiceRoute& route, const LoadBalancer::ServiceInstance& instance,
                              const common::http::HttpRequest& request,
                              common::http::HttpResponse& response);

            /**
             * @brief 认证检查
             * @param request HTTP请求
             * @param response HTTP响应
             * @return true表示认证通过
             */
            bool authenticateRequest(const common::http::HttpRequest& request, common::http::HttpResponse& response);

            /**
             * @brief 检查限流
             * @param request HTTP请求
             * @param response HTTP响应
             * @return true表示通过限流检查
             */
            bool checkRateLimit(const common::http::HttpRequest& request, common::http::HttpResponse& response);

            /**
             * @brief 应用CORS头部
             * @param request HTTP请求
             * @param response HTTP响应
             */
            void applyCorsHeaders(const common::http::HttpRequest& request, common::http::HttpResponse& response);

            /**
             * @brief 处理健康检查请求
             * @param request HTTP请求
             * @param response HTTP响应
             */
            void handleHealthCheck(const common::http::HttpRequest& request, common::http::HttpResponse& response);

            /**
             * @brief 处理指标请求
             * @param request HTTP请求
             * @param response HTTP响应
             */
            void handleMetrics(const common::http::HttpRequest& request, common::http::HttpResponse& response);

            /**
             * @brief 检查来源是否被允许
             * @param origin 请求来源
             * @return true表示允许
             */
            bool isOriginAllowed(const std::string& origin) const;

            /**
             * @brief 验证JWT token
             * @param token JWT token
             * @return true表示有效
             */
            bool validateJwtToken(const std::string& token) const;

            /**
             * @brief 更新统计信息
             * @param success 是否成功
             * @param response_time 响应时间
             * @param rate_limited 是否被限流
             * @param circuit_breaker_open 熔断器是否开启
             */
            void updateStatistics(bool success, std::chrono::milliseconds response_time,
                                bool rate_limited = false, bool circuit_breaker_open = false);

            /**
             * @brief 初始化HTTP服务器路由
             */
            void setupHttpRoutes();

            /**
             * @brief 初始化中间件
             */
            void setupMiddleware();

            /**
             * @brief 设置CORS中间件
             */
            void setupCorsMiddleware();

            /**
             * @brief 设置认证中间件
             */
            void setupAuthMiddleware();

            /**
             * @brief 设置限流中间件
             */
            void setupRateLimitMiddleware();

            /**
             * @brief 设置日志中间件
             */
            void setupLoggingMiddleware();

            /**
             * @brief 记录请求完成日志
             * @param request HTTP请求
             * @param response HTTP响应
             */
            void logRequestCompletion(const common::http::HttpRequest& request, const common::http::HttpResponse& response);
        };


    }
}


#endif //API_GATEWAY_H
