//
// Created by 29108 on 2025/7/15.
//

#ifndef SERVICE_ROUTE_H
#define SERVICE_ROUTE_H


#include <algorithm>
#include <map>
#include <memory>
#include <shared_mutex>
#include <string>
#include <vector>
#include <set>
#include <sstream>
#include <cctype>
#include <utility>
#include "common/config/config_manager.h"

namespace core_services {
    namespace api_gateway {
        /**
         * @brief 服务路由配置结构体
         * @details 定义API网关的路由规则和目标服务信息
         */
        struct ServiceRoute {
            std::string service_name;                              ///< 服务名称
            std::string path_pattern;                              ///< 路径匹配模式（支持通配符）
            std::vector<std::string> methods;                      ///< 支持的HTTP方法
            std::string target_host;                               ///< 目标服务主机
            int target_port;                                       ///< 目标服务端口
            std::string target_path_prefix;                        ///< 目标路径前缀
            bool strip_path_prefix = true;                         ///< 是否去除路径前缀
            int timeout_ms = 5000;                                 ///< 请求超时时间（毫秒）
            int retry_count = 3;                                   ///< 重试次数
            bool enable_circuit_breaker = true;                    ///< 是否启用熔断器
            bool enable_rate_limiting = true;                      ///< 是否启用限流
            std::map<std::string, std::string> headers_to_add;     ///< 需要添加的请求头
            std::vector<std::string> headers_to_remove;            ///< 需要移除的请求头

            // 路由匹配配置
            enum class MatchType {
                EXACT,      ///< 精确匹配
                PREFIX,     ///< 前缀匹配
                REGEX       ///< 正则表达式匹配
            };
            MatchType match_type = MatchType::PREFIX;               ///< 匹配类型
            int priority = 0;                                       ///< 路由优先级（数值越大优先级越高）
            bool enabled = true;                                    ///< 路由是否启用
            std::string route_id;                                   ///< 路由唯一标识

            // 健康检查配置
            bool health_check_enabled = true;                       ///< 是否启用健康检查
            std::string health_check_path = "/health";              ///< 健康检查路径
            int health_check_interval_ms = 30000;                   ///< 健康检查间隔（毫秒）

            // 负载均衡配置
            std::string load_balancer_strategy = "round_robin";     ///< 负载均衡策略

            /**
             * @brief 默认构造函数
             */
            ServiceRoute() = default;

            /**
             * @brief 构造函数
             * @param name 服务名称
             * @param pattern 路径模式
             * @param host 目标主机
             * @param port 目标端口
             */
            ServiceRoute(const std::string& name, const std::string& pattern,
                        const std::string& host, int port)
                : service_name(name), path_pattern(pattern), target_host(host), target_port(port) {
                route_id = name + "_" + pattern;
                // 设置默认支持的HTTP方法
                if (methods.empty()) {
                    methods = {"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"};
                }
            }

            /**
             * @brief 验证路由配置有效性
             * @return true表示配置有效
             */
            bool validate() const {
                return !service_name.empty() &&
                       !path_pattern.empty() &&
                       !target_host.empty() &&
                       target_port > 0 && target_port < 65536 &&
                       timeout_ms > 0 &&
                       retry_count >= 0 &&
                       health_check_interval_ms > 0;
            }

            /**
             * @brief 检查路径是否匹配
             * @param path 请求路径
             * @return true表示匹配
             */
            bool matchPath(const std::string& path) const;

            /**
             * @brief 检查HTTP方法是否支持
             * @param method HTTP方法
             * @return true表示支持
             */
            bool supportMethod(const std::string& method) const {
                if (methods.empty()) return true; // 空表示支持所有方法
                return std::find(methods.begin(), methods.end(), method) != methods.end();
            }

            /**
             * @brief 重写请求路径
             * @param original_path 原始路径
             * @return 重写后的路径
             */
            std::string rewritePath(const std::string& original_path) const;

            /**
             * @brief 生成目标URL
             * @param original_path 原始路径
             * @return 完整的目标URL
             */
            std::string generateTargetUrl(const std::string& original_path) const;

            /**
             * @brief 将匹配类型转换为字符串
             * @param type 匹配类型
             * @return 类型字符串
             */
            static std::string matchTypeToString(MatchType type) {
                switch (type) {
                    case MatchType::EXACT: return "exact";
                    case MatchType::PREFIX: return "prefix";
                    case MatchType::REGEX: return "regex";
                    default: return "unknown";
                }
            }

            /**
             * @brief 从字符串解析匹配类型
             * @param type_str 类型字符串
             * @return 匹配类型
             */
            static MatchType stringToMatchType(const std::string& type_str) {
                if (type_str == "exact") return MatchType::EXACT;
                if (type_str == "prefix") return MatchType::PREFIX;
                if (type_str == "regex") return MatchType::REGEX;
                return MatchType::PREFIX; // 默认前缀匹配
            }

            /**
             * @brief 比较路由优先级（用于排序）
             * @param other 另一个路由
             * @return true表示当前路由优先级更高
             */
            bool operator<(const ServiceRoute& other) const {
                return priority > other.priority; // 优先级高的排在前面
            }
        };

        /**
         * @brief 路由配置管理器
         */
        class RouteManager {
        public:
            /**
             * @brief 构造函数
             */
            RouteManager() = default;

            /**
             * @brief 析构函数
             */
            ~RouteManager() = default;

            /**
             * @brief 添加路由规则
             * @param route 路由配置
             */
            void addRoute(const ServiceRoute& route);

            /**
             * @brief 移除路由规则
             * @param service_name 服务名称
             */
            void removeRoute(const std::string& service_name);

            /**
             * @brief 更新路由规则
             * @param route 新的路由配置
             */
            void updateRoute(const ServiceRoute& route);

            /**
             * @brief 查找匹配的路由
             * @param method HTTP方法
             * @param path 请求路径
             * @return 匹配的路由配置，如果没有匹配则返回nullptr
             */
            std::shared_ptr<ServiceRoute> findRoute(const std::string& method, const std::string& path) const;

            /**
             * @brief 获取所有路由
             * @return 所有路由配置
             */
            std::vector<ServiceRoute> getAllRoutes() const;

            /**
             * @brief 清空所有路由
             */
            void clear();

            /**
             * @brief 根据路由ID查找路由
             * @param route_id 路由ID
             * @return 路由配置，如果没有找到则返回nullptr
             */
            std::shared_ptr<ServiceRoute> findRouteById(const std::string& route_id) const;

            /**
             * @brief 根据服务名称获取路由
             * @param service_name 服务名称
             * @return 匹配的路由列表
             */
            std::vector<ServiceRoute> getRoutesByService(const std::string& service_name) const;

            /**
             * @brief 启用/禁用路由
             * @param route_id 路由ID
             * @param enabled 是否启用
             * @return 是否操作成功
             */
            bool setRouteEnabled(const std::string& route_id, bool enabled);

            /**
             * @brief 获取路由数量
             * @return 路由总数
             */
            size_t getRouteCount() const;

            /**
             * @brief 获取启用的路由数量
             * @return 启用的路由数量
             */
            size_t getEnabledRouteCount() const;

            /**
             * @brief 验证所有路由配置
             * @return 无效路由的ID列表
             */
            std::vector<std::string> validateAllRoutes() const;

            /**
             * @brief 从配置文件加载路由
             * @param config_file 配置文件路径
             * @return 是否加载成功
             */
            bool loadFromConfig(const std::string& config_file);

            /**
             * @brief 保存路由到配置文件
             * @param config_file 配置文件路径
             * @return 是否保存成功
             */
            bool saveToConfig(const std::string& config_file) const;

        private:
            std::vector<ServiceRoute> routes_;                     ///< 路由列表
            mutable std::shared_mutex routes_mutex_;               ///< 路由访问锁

            /**
             * @brief 对路由进行排序（按优先级）
             */
            void sortRoutes();

            /**
             * @brief 检查路由ID是否已存在
             * @param route_id 路由ID
             * @return 是否存在
             */
            bool routeIdExists(const std::string& route_id) const;

            /**
            * @brief 获取文件扩展名
            * @param filename 文件名
            * @return 文件扩展名（包含点号）
            */
            std::string getFileExtension(const std::string& filename) const;

            /**
             * @brief 保存路由到JSON配置文件
             * @param config_file 配置文件路径
             * @return 是否保存成功
             */
            bool saveToJsonConfig(const std::string& config_file) const;

            /**
             * @brief 保存路由到YAML配置文件
             * @param config_file 配置文件路径
             * @return 是否保存成功
             */
            bool saveToYamlConfig(const std::string& config_file) const;

            /**
             * @brief 保存路由到ENV配置文件
             * @param config_file 配置文件路径
             * @return 是否保存成功
             */
            bool saveToEnvConfig(const std::string& config_file) const;


            /**
             * @brief 转义JSON字符串
             * @param str 原始字符串
             * @return 转义后的字符串
             */
            std::string escapeJsonString(const std::string& str) const;

            /**
             * @brief 获取当前时间戳字符串
             * @return 时间戳字符串
             */
            std::string getCurrentTimestamp() const;

            // ==================== 新增的配置解析相关方法 ====================

            /**
             * @brief 解析配置键，提取路由名称和属性
             * @param config_key 配置键，如"routes.user_route.path"
             * @return 返回{路由名称, 属性名}的pair
             */
            std::pair<std::string, std::string> parseConfigKey(const std::string& config_key);

            /**
             * @brief 设置路由属性
             * @param route 要设置的路由对象
             * @param attribute 属性名
             * @param value 属性值
             */
            void setRouteAttribute(ServiceRoute& route, const std::string& attribute, const std::string& value);

            /**
             * @brief 解析HTTP方法字符串为vector
             * @param methods_str 逗号分隔的HTTP方法字符串，如"GET,POST,PUT"
             * @return HTTP方法向量
             */
            std::vector<std::string> parseHttpMethodsVector(const std::string& methods_str);

            /**
             * @brief 解析字符串向量（逗号分隔）
             * @param str 逗号分隔的字符串，如"header1,header2,header3"
             * @return 字符串向量
             */
            std::vector<std::string> parseStringVector(const std::string& str);

            /**
             * @brief 解析匹配类型字符串
             * @param match_type_str 匹配类型字符串，如"exact"、"prefix"、"regex"
             * @return 匹配类型枚举
             */
            ServiceRoute::MatchType parseMatchType(const std::string& match_type_str);

            /**
             * @brief 验证路由配置的有效性
             * @param route 要验证的路由对象
             * @return 验证通过返回true，否则返回false
             */
            bool validateRoute(const ServiceRoute& route);
        };



    }
}




#endif //SERVICE_ROUTE_H
