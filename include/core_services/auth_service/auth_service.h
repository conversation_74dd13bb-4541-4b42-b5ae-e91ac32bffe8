/**
 * @file auth_service.h
 * @brief 认证服务主类 - 游戏微服务架构的认证服务核心
 * <AUTHOR>
 * @date 2025/7/21
 * @version 1.0
 *
 * 功能特性:
 * - 用户注册和登录认证
 * - 游戏登录和服务器分配
 * - JWT令牌管理
 * - 会话管理
 * - 密码安全管理
 * - 多设备支持
 * - 实时状态同步
 *
 * 架构特性:
 * - 与API Gateway协作的服务发现
 * - 基于Kafka的事件驱动架构
 * - 智能负载均衡支持
 * - 高可用性和容错设计
 * - 微服务间通信优化
 *
 * 与现有模块集成:
 * - 复用common模块的所有基础设施
 * - 依赖API Gateway的服务发现和路由
 * - 集成Kafka进行事件发布
 * - 使用MySQL和Redis进行数据存储
 * - 使用ThreadPool进行异步处理
 */

#ifndef AUTH_SERVICE_H
#define AUTH_SERVICE_H

// 解决DEBUG宏冲突
#ifdef DEBUG
#undef DEBUG
#endif

#include "user_models.h"
#include "jwt_manager.h"
#include "password_manager.h"
#include "session_manager.h"
#include "user_repository.h"
#include "database_initializer.h"
#include "common/http/http_server.h"
#include "common/http/http_client.h"
#include "common/database/mysql_pool.h"
#include "common/database/redis_pool.h"
#include "common/kafka/kafka_producer.h"
#include "common/kafka/kafka_consumer.h"
#include "common/thread_pool/thread_pool.h"
#include "common/config/config_manager.h"
#include "common/logger/logger.h"
#include "common/scheduler/task_scheduler.h"
#include <string>
#include <memory>
#include <vector>
#include <optional>
#include <future>
#include <atomic>
#include <thread>
#include <chrono>
#include <nlohmann/json.hpp>

namespace core_services {
    namespace auth_service {



        /**
         * @brief 认证服务主类
         * @details 游戏微服务架构的认证服务核心实现
         */
        class AuthService {
        public:
            /**
             * @brief 服务配置结构体
             */
            struct Config {
                std::string service_name = "auth_service";           ///< 服务名称
                std::string service_version = "1.0.0";              ///< 服务版本
                std::string host = "0.0.0.0";                       ///< 监听主机
                int port = 8081;                                     ///< 监听端口
                int worker_threads = 8;                              ///< 工作线程数
                bool enable_https = false;                          ///< 是否启用HTTPS
                std::string ssl_cert_file;                          ///< SSL证书文件
                std::string ssl_key_file;                           ///< SSL私钥文件

                // 数据库配置
                std::string mysql_host = "localhost";               ///< MySQL主机
                int mysql_port = 3306;                               ///< MySQL端口
                std::string mysql_database = "game_microservices";  ///< MySQL数据库名
                std::string mysql_username = "root";                ///< MySQL用户名
                std::string mysql_password = "";                    ///< MySQL密码
                int mysql_pool_size = 10;                           ///< MySQL连接池大小

                // Redis配置
                std::string redis_host = "localhost";               ///< Redis主机
                int redis_port = 6379;                               ///< Redis端口
                std::string redis_password = "";                    ///< Redis密码
                int redis_database = 0;                             ///< Redis数据库
                int redis_pool_size = 10;                           ///< Redis连接池大小

                // Kafka配置
                std::string kafka_brokers = "localhost:9092";       ///< Kafka代理地址
                std::string kafka_topic_prefix = "game_microservices"; ///< Kafka主题前缀
                bool enable_kafka = true;                           ///< 是否启用Kafka

                // 服务发现配置
                bool enable_service_discovery = true;               ///< 是否启用服务发现
                std::string api_gateway_host = "localhost";         ///< API Gateway主机
                int api_gateway_port = 8080;                        ///< API Gateway端口
                std::chrono::seconds heartbeat_interval{30};        ///< 心跳间隔

                // 性能配置
                int max_concurrent_requests = 1000;                 ///< 最大并发请求数
                std::chrono::seconds request_timeout{30};           ///< 请求超时时间
                bool enable_request_logging = true;                 ///< 是否启用请求日志
                bool enable_metrics = true;                         ///< 是否启用指标收集

                /**
                 * @brief 从配置管理器加载配置
                 * @return 认证服务配置对象
                 */
                static Config fromConfigManager();

                /**
                 * @brief 验证配置有效性
                 * @return 验证结果，成功返回空字符串
                 */
                std::string validate() const;
            };

            /**
             * @brief 构造函数
             * @param config 服务配置
             */
            explicit AuthService(const Config& config);

            /**
             * @brief 析构函数
             */
            ~AuthService();

            // ==================== 服务生命周期 ====================

            /**
             * @brief 初始化服务
             * @return 初始化成功返回true
             */
            bool initialize();

            /**
             * @brief 启动服务
             * @return 启动成功返回true
             */
            bool start();

            /**
             * @brief 停止服务
             * @return 停止成功返回true
             */
            bool stop();

            /**
             * @brief 重启服务
             * @return 重启成功返回true
             */
            bool restart();

            /**
             * @brief 检查服务是否运行中
             * @return 如果服务运行中返回true
             */
            bool isRunning() const { return running_; }

            // ==================== 核心认证功能 ====================

            /**
             * @brief 用户注册
             * @param request 注册请求
             * @return 认证结果
             */
            AuthResult registerUser(const AuthRequest& request);

            /**
             * @brief 用户登录
             * @param request 登录请求
             * @return 认证结果
             */
            AuthResult loginUser(const AuthRequest& request);

            /**
             * @brief 用户登出
             * @param access_token 访问令牌
             * @return 登出成功返回true
             */
            bool logoutUser(const std::string& access_token);

            /**
             * @brief 刷新访问令牌
             * @param refresh_token 刷新令牌
             * @return 认证结果
             */
            AuthResult refreshToken(const std::string& refresh_token);

            /**
             * @brief 验证访问令牌
             * @param access_token 访问令牌
             * @param client_ip 客户端IP
             * @return 令牌验证结果
             */
            TokenValidationResult validateToken(const std::string& access_token, const std::string& client_ip = "");

            // ==================== 游戏相关功能 ====================

            /**
             * @brief 游戏登录
             * @param request 游戏登录请求
             * @return 游戏登录结果
             */
            GameLoginResult gameLogin(const GameLoginRequest& request);

            /**
             * @brief 游戏登出
             * @param game_session_token 游戏会话令牌
             * @return 登出成功返回true
             */
            bool gameLogout(const std::string& game_session_token);

            /**
             * @brief 获取用户游戏数据
             * @param user_id 用户ID
             * @param game_type 游戏类型
             * @return 游戏用户数据，未找到返回nullopt
             */
            std::optional<GameUserData> getUserGameData(int64_t user_id, GameType game_type);

            /**
             * @brief 更新游戏结果
             * @param user_id 用户ID
             * @param game_type 游戏类型
             * @param won 是否胜利
             * @param score 得分
             * @param duration_seconds 游戏时长
             * @return 更新成功返回true
             */
            bool updateGameResult(int64_t user_id, GameType game_type, bool won, int64_t score, int64_t duration_seconds);

            /**
             * @brief 获取游戏服务器列表
             * @param game_type 游戏类型
             * @param region 区域（可选）
             * @return 游戏服务器信息列表
             */
            std::vector<GameServerInfo> getGameServers(GameType game_type, const std::string& region = "");

            /**
             * @brief 选择最佳游戏服务器
             * @param game_type 游戏类型
             * @param user_location 用户位置（可选）
             * @return 最佳游戏服务器信息，未找到返回nullopt
             */
            std::optional<GameServerInfo> selectBestGameServer(GameType game_type, const std::string& user_location = "");

            // ==================== 用户管理功能 ====================

            /**
             * @brief 获取用户信息
             * @param user_id 用户ID
             * @return 用户信息，未找到返回nullopt
             */
            std::optional<CoreUserInfo> getUserInfo(int64_t user_id);

            /**
             * @brief 更新用户信息
             * @param user_info 用户信息
             * @return 更新成功返回true
             */
            bool updateUserInfo(const CoreUserInfo& user_info);

            /**
             * @brief 修改密码
             * @param user_id 用户ID
             * @param old_password 旧密码
             * @param new_password 新密码
             * @return 修改成功返回true
             */
            bool changePassword(int64_t user_id, const std::string& old_password, const std::string& new_password);

            /**
             * @brief 重置密码
             * @param email 邮箱地址
             * @return 重置成功返回true
             */
            bool resetPassword(const std::string& email);

            /**
             * @brief 获取用户会话列表
             * @param user_id 用户ID
             * @return 会话信息列表
             */
            std::vector<UserSession> getUserSessions(int64_t user_id);

            /**
             * @brief 终止用户会话
             * @param user_id 用户ID
             * @param session_id 会话ID
             * @return 终止成功返回true
             */
            bool terminateUserSession(int64_t user_id, const std::string& session_id);

            // ==================== 异步操作 ====================

            /**
             * @brief 异步用户注册
             * @param request 注册请求
             * @return Future对象，包含认证结果
             */
            std::future<AuthResult> registerUserAsync(const AuthRequest& request);

            /**
             * @brief 异步用户登录
             * @param request 登录请求
             * @return Future对象，包含认证结果
             */
            std::future<AuthResult> loginUserAsync(const AuthRequest& request);

            /**
             * @brief 异步游戏登录
             * @param request 游戏登录请求
             * @return Future对象，包含游戏登录结果
             */
            std::future<GameLoginResult> gameLoginAsync(const GameLoginRequest& request);

            // ==================== 事件和通知 ====================

            /**
             * @brief 发布用户事件
             * @param event_type 事件类型
             * @param user_id 用户ID
             * @param event_data 事件数据
             * @return 发布成功返回true
             */
            bool publishUserEvent(const std::string& event_type, int64_t user_id, const nlohmann::json& event_data);

            /**
             * @brief 发布游戏事件
             * @param event_type 事件类型
             * @param user_id 用户ID
             * @param game_type 游戏类型
             * @param event_data 事件数据
             * @return 发布成功返回true
             */
            bool publishGameEvent(const std::string& event_type, int64_t user_id, GameType game_type, const nlohmann::json& event_data);

            /**
             * @brief 处理游戏服务器状态更新
             * @param server_info 服务器信息
             * @return 处理成功返回true
             */
            bool handleGameServerStatusUpdate(const GameServerInfo& server_info);

            // ==================== 管理和监控 ====================

            /**
             * @brief 获取服务状态
             * @return 服务状态信息
             */
            nlohmann::json getServiceStatus() const;

            /**
             * @brief 获取服务统计信息
             * @return 统计信息
             */
            nlohmann::json getServiceStatistics() const;

            /**
             * @brief 获取健康检查信息
             * @return 健康检查结果
             */
            nlohmann::json getHealthCheck() const;

            /**
             * @brief 重置统计信息
             */
            void resetStatistics();

            /**
             * @brief 获取在线用户数量
             * @return 在线用户数量
             */
            int64_t getOnlineUserCount() const;

            /**
             * @brief 获取活跃会话数量
             * @return 活跃会话数量
             */
            int64_t getActiveSessionCount() const;

            /**
             * @brief 强制清理用户会话（管理员操作）
             * @param user_id 用户ID
             * @param reason 清理原因
             * @return 清理的会话数量
             */
            int forceCleanupUserSessions(int64_t user_id, const std::string& reason);

        private:
            Config config_;                                                     ///< 服务配置
            std::atomic<bool> running_{false};                                 ///< 服务运行状态
            std::atomic<bool> initialized_{false};                             ///< 服务初始化状态

            // 核心组件
            std::unique_ptr<common::http::HttpServer> http_server_;             ///< HTTP服务器
            std::thread http_server_thread_;                                    ///< HTTP服务器线程
            std::thread connection_pool_monitor_thread_;                        ///< 连接池监控线程
            std::shared_ptr<common::database::MySQLPool> mysql_pool_;          ///< MySQL连接池
            std::shared_ptr<common::database::RedisPool> redis_pool_;          ///< Redis连接池
            std::shared_ptr<common::messaging::KafkaProducer> kafka_producer_;     ///< Kafka生产者
            std::shared_ptr<common::messaging::KafkaConsumer> kafka_consumer_;     ///< Kafka消费者
            std::shared_ptr<common::thread_pool::ThreadPool> thread_pool_;     ///< 线程池
            std::shared_ptr<common::scheduler::TaskScheduler> scheduler_;          ///< 调度器

            // 业务组件
            std::unique_ptr<JwtManager> jwt_manager_;                          ///< JWT管理器
            std::unique_ptr<PasswordManager> password_manager_;               ///< 密码管理器
            std::unique_ptr<SessionManager> session_manager_;                 ///< 会话管理器
            std::unique_ptr<UserRepository> user_repository_;                 ///< 用户数据访问层
            std::unique_ptr<DatabaseInitializer> database_initializer_;       ///< 数据库初始化器

            // 游戏服务器缓存
            std::unordered_map<GameType, std::vector<GameServerInfo>> game_servers_cache_; ///< 游戏服务器缓存
            std::chrono::system_clock::time_point last_server_update_;         ///< 最后服务器更新时间
            mutable std::shared_mutex game_servers_mutex_;                     ///< 游戏服务器互斥锁

            // 统计信息
            mutable std::atomic<uint64_t> total_requests_{0};                  ///< 总请求数
            mutable std::atomic<uint64_t> successful_requests_{0};             ///< 成功请求数
            mutable std::atomic<uint64_t> failed_requests_{0};                 ///< 失败请求数
            mutable std::atomic<uint64_t> registrations_{0};                   ///< 注册数量
            mutable std::atomic<uint64_t> logins_{0};                          ///< 登录数量
            mutable std::atomic<uint64_t> game_logins_{0};                     ///< 游戏登录数量

            // ==================== 内部方法 ====================

            /**
             * @brief 初始化数据库连接池
             * @return 初始化成功返回true
             */
            bool initializeDatabasePools();

            /**
             * @brief 初始化数据库表结构
             * @return 初始化成功返回true
             */
            bool initializeDatabaseSchema();

            /**
             * @brief 初始化Kafka组件
             * @return 初始化成功返回true
             */
            bool initializeKafka();

            /**
             * @brief 初始化HTTP服务器
             * @return 初始化成功返回true
             */
            bool initializeHttpServer();

            /**
             * @brief 初始化业务组件
             * @return 初始化成功返回true
             */
            bool initializeBusinessComponents();

            /**
             * @brief 注册HTTP路由
             */
            void registerHttpRoutes();

            /**
             * @brief 注册服务到API Gateway
             * @return 注册成功返回true
             */
            bool registerToApiGateway();

            /**
             * @brief 启动心跳任务
             */
            void startHeartbeatTask();

            /**
             * @brief 启动定时任务
             */
            void startScheduledTasks();

            /**
             * @brief 启动连接池监控
             */
            void startConnectionPoolMonitoring();

            /**
             * @brief 检查端口可用性
             * @param host 主机地址
             * @param port 端口号
             * @return 端口可用返回true
             */
            bool checkPortAvailability(const std::string& host, int port);

            /**
             * @brief 处理Kafka消息
             * @param topic 主题
             * @param message 消息
             */
            void handleKafkaMessage(const std::string& topic, const std::string& message);

            /**
             * @brief 处理游戏服务器注册事件
             * @param server_info 服务器信息
             */
            void handleGameServerRegistered(const GameServerInfo& server_info);

            /**
             * @brief 处理游戏服务器注销事件
             * @param server_id 服务器ID
             */
            void handleGameServerUnregistered(const std::string& server_id);

            /**
             * @brief 处理用户封禁事件
             * @param user_id 用户ID
             */
            void handleUserBanned(int64_t user_id);

            /**
             * @brief 处理用户解封事件
             * @param user_id 用户ID
             */
            void handleUserUnbanned(int64_t user_id);

            /**
             * @brief 处理强制登出事件
             * @param user_id 用户ID
             */
            void handleForceLogout(int64_t user_id);

            /**
             * @brief 处理清除缓存事件
             * @param cache_type 缓存类型
             */
            void handleClearCache(const std::string& cache_type);

            /**
             * @brief 更新游戏服务器缓存
             * @return 更新成功返回true
             */
            bool updateGameServersCache();

            /**
             * @brief 验证请求权限
             * @param access_token 访问令牌
             * @param required_roles 需要的角色
             * @return 验证通过返回true
             */
            bool validateRequestPermission(const std::string& access_token, const std::vector<std::string>& required_roles);

            /**
             * @brief 记录请求日志
             * @param endpoint 端点
             * @param method 方法
             * @param client_ip 客户端IP
             * @param user_id 用户ID
             * @param response_code 响应代码
             * @param response_time 响应时间
             */
            void logRequest(const std::string& endpoint, const std::string& method, const std::string& client_ip,
                          int64_t user_id, int response_code, std::chrono::milliseconds response_time);

            /**
             * @brief 构建错误响应
             * @param error_code 错误代码
             * @param error_message 错误消息
             * @return 错误响应JSON
             */
            nlohmann::json buildErrorResponse(const std::string& error_code, const std::string& error_message);

            /**
             * @brief 构建成功响应
             * @param data 响应数据
             * @return 成功响应JSON
             */
            nlohmann::json buildSuccessResponse(const nlohmann::json& data);
        };

    } // namespace auth_service
} // namespace core_services

#endif //AUTH_SERVICE_H
