# API网关路由配置示例
# 支持YAML和JSON格式

# 路由配置
routes:
  # 用户服务路由
  user_service_route:
    path: "/api/users/*"
    service: "user-service"
    target_host: "user-service.internal"
    target_port: 8080
    target_path_prefix: "/api/v1"
    strip_path_prefix: true
    methods: "GET,POST,PUT,DELETE"
    timeout: 30                    # 超时时间（秒，会转换为毫秒）
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 100
    enabled: true
    route_id: "user-service-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 30      # 健康检查间隔（秒，会转换为毫秒）
    load_balancer_strategy: "round_robin"
    headers_to_add:
      X-Service-Name: "user-service"
      X-Gateway-Version: "1.0.0"
    headers_to_remove: "X-Internal-Token,X-Debug-Info"

  # 订单服务路由
  order_service_route:
    path: "/api/orders/*"
    service: "order-service"
    target_host: "order-service.internal"
    target_port: 8081
    target_path_prefix: "/api/v1"
    strip_path_prefix: true
    methods: "GET,POST,PUT,DELETE,PATCH"
    timeout_ms: 45000              # 直接指定毫秒
    retry_count: 2
    enable_circuit_breaker: true
    enable_rate_limiting: false
    match_type: "prefix"
    priority: 90
    enabled: true
    route_id: "order-service-api"
    health_check_enabled: true
    health_check_path: "/actuator/health"
    health_check_interval_ms: 60000 # 直接指定毫秒
    load_balancer_strategy: "weighted_round_robin"
    headers_to_add:
      X-Service-Name: "order-service"
      X-Request-Timeout: "45000"

  # 支付服务路由
  payment_service_route:
    path: "/api/payments/*"
    service: "payment-service"
    target_host: "payment-service.internal"
    target_port: 8082
    target_path_prefix: "/api/v1"
    strip_path_prefix: false
    methods: "POST,GET"
    timeout: 60                    # 支付服务需要更长超时时间
    retry_count: 1                 # 支付操作重试次数要少
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "prefix"
    priority: 200                  # 支付服务优先级最高
    enabled: true
    route_id: "payment-service-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 15      # 支付服务健康检查更频繁
    load_balancer_strategy: "least_connections"
    headers_to_add:
      X-Service-Name: "payment-service"
      X-Security-Level: "high"
    headers_to_remove: "X-Debug-Info"

  # 静态文件服务路由
  static_files_route:
    path: "/static/*"
    service: "static-file-service"
    target_host: "nginx.internal"
    target_port: 80
    target_path_prefix: "/static"
    strip_path_prefix: false
    methods: "GET,HEAD"
    timeout: 10                    # 静态文件服务超时时间短
    retry_count: 0                 # 静态文件不重试
    enable_circuit_breaker: false
    enable_rate_limiting: false
    match_type: "prefix"
    priority: 10                   # 静态文件优先级最低
    enabled: true
    route_id: "static-files"
    health_check_enabled: false    # 静态文件服务不需要健康检查
    load_balancer_strategy: "round_robin"

  # 精确匹配路由示例
  health_check_route:
    path: "/health"
    service: "health-service"
    target_host: "localhost"
    target_port: 8090
    target_path_prefix: ""
    strip_path_prefix: false
    methods: "GET"
    timeout: 5
    retry_count: 0
    enable_circuit_breaker: false
    enable_rate_limiting: false
    match_type: "exact"            # 精确匹配
    priority: 1000                 # 健康检查优先级最高
    enabled: true
    route_id: "health-check"
    health_check_enabled: false
    load_balancer_strategy: "round_robin"

  # 正则表达式匹配路由示例
  api_version_route:
    path: "/api/v[0-9]+/.*"
    service: "versioned-api-service"
    target_host: "api-service.internal"
    target_port: 8083
    target_path_prefix: ""
    strip_path_prefix: false
    methods: "GET,POST,PUT,DELETE,PATCH,HEAD,OPTIONS"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    match_type: "regex"            # 正则表达式匹配
    priority: 50
    enabled: true
    route_id: "versioned-api"
    health_check_enabled: true
    health_check_path: "/health"
    health_check_interval: 30
    load_balancer_strategy: "round_robin"
    headers_to_add:
      X-API-Version: "dynamic"

# 热重载配置
  hot_reload:
    enabled: true
    check_interval: 30             # 检查配置文件变更的间隔（秒）

# 全局配置
global:
  default_timeout: 30
  default_retry_count: 3
  default_health_check_interval: 60
  enable_access_log: true
  enable_metrics: true
