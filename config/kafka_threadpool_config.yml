# Kafka模块线程池集成配置示例
# 此配置文件展示了如何配置Kafka生产者和消费者的线程池集成功能

kafka:
  # ==================== Kafka生产者配置 ====================
  producer:
    # 基础连接配置
    brokers: "kafka:9092"               # Kafka服务器地址
    client_id: "game-service-producer"  # 客户端ID
    
    # 可靠性配置
    acks: 1                             # 确认级别 (0, 1, -1)
    retries: 3                          # 重试次数
    
    # 性能配置
    batch_size: 16384                   # 批处理大小
    linger_ms: 5                        # 消息缓冲时间
    buffer_memory: 33554432             # 缓冲区大小
    compression_type: "snappy"          # 压缩算法
    
    # 超时配置
    request_timeout_ms: 30000           # 请求超时时间
    delivery_timeout_ms: 120000         # 投递超时时间
    
    # 线程池集成配置
    thread_pool:
      enable_async_operations: true     # 是否启用异步操作
      core_size: 4                      # 核心线程数
      max_size: 16                      # 最大线程数
      keep_alive_ms: 60000             # 线程保活时间
      queue_capacity: 1000             # 任务队列容量
      enable_monitoring: true          # 是否启用监控
      name_prefix: "kafka-producer-"   # 线程名前缀

  # ==================== Kafka消费者配置 ====================
  consumer:
    # 基础连接配置
    brokers: "kafka:9092"               # Kafka服务器地址
    group_id: "game-service-group"      # 消费者组ID
    client_id: "game-service-consumer"  # 客户端ID
    
    # 消费配置
    session_timeout_ms: 30000           # 会话超时时间
    max_poll_interval_ms: 300000        # 最大轮询间隔
    offset_reset: "earliest"            # 偏移量重置策略
    enable_auto_commit: false           # 是否自动提交偏移量
    auto_commit_interval_ms: 5000       # 自动提交间隔
    
    # 线程池集成配置
    thread_pool:
      enable_async_operations: true     # 是否启用异步操作
      core_size: 4                      # 核心线程数
      max_size: 16                      # 最大线程数
      keep_alive_ms: 60000             # 线程保活时间
      queue_capacity: 1000             # 任务队列容量
      enable_monitoring: true          # 是否启用监控
      name_prefix: "kafka-consumer-"   # 线程名前缀

# ==================== 日志配置 ====================
logging:
  level: "INFO"                        # 日志级别
  enable_console: true                 # 是否启用控制台输出
  enable_file: true                    # 是否启用文件输出
  log_file_path: "logs/kafka.log"      # 日志文件路径
  enable_async: true                   # 是否启用异步日志

# ==================== 应用配置 ====================
application:
  name: "kafka-threadpool-demo"        # 应用名称
  version: "1.0.0"                     # 应用版本
  environment: "development"           # 运行环境
