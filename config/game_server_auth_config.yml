# 游戏服务器认证授权服务配置文件
# config/game_server_auth_config.yml

auth_service:
  # HTTP服务器配置
  server:
    host: "0.0.0.0"
    port: 8081
    worker_threads: 8
    max_connections: 10000
    keep_alive_timeout: 60
    request_timeout: 30
    enable_compression: true
    
  # JWT配置
  jwt:
    secret_key: "your_super_secret_jwt_key_for_game_servers_2025"
    issuer: "game-auth-service"
    audience: "game-servers"
    access_token_expire_seconds: 3600      # 1小时
    refresh_token_expire_seconds: 604800   # 7天
    enable_token_blacklist: true
    blacklist_cleanup_interval_minutes: 60
    
  # 数据库配置
  database:
    mysql:
      host: "localhost"
      port: 3306
      user: "game_auth_user"
      password: "secure_password_123"
      database: "game_auth_service_db"
      charset: "utf8mb4"
      pool_size: 20
      max_idle_time: 300
      connection_timeout: 10
      
    redis:
      host: "localhost"
      port: 6379
      password: ""
      database: 0
      pool_size: 10
      connection_timeout: 5
      command_timeout: 3
      
  # 会话管理配置
  session:
    session_timeout_minutes: 60           # 游戏会话1小时
    max_sessions_per_user: 3              # 每用户最多3个会话
    enable_session_sharing: false         # 不允许会话共享
    cleanup_interval_minutes: 10          # 10分钟清理一次
    
  # 游戏服务器专用配置
  game_server:
    # 服务器发现配置
    server_discovery:
      enable: true
      update_interval_seconds: 30
      health_check_interval_seconds: 30
      server_timeout_seconds: 120
      max_server_load: 0.85
      preferred_regions: ["asia-east", "asia-south", "us-west", "eu-west"]
      enable_auto_scaling: false
      enable_cross_server: true
    
    # 房间管理配置
    room_management:
      max_rooms_per_server: 200
      max_players_per_room: 20
      room_idle_timeout_minutes: 30
      auto_match_timeout_seconds: 60
      enable_private_rooms: true
      enable_password_rooms: true
      enable_spectator_mode: true
    
    # 游戏匹配配置
    matchmaking:
      enable: true
      skill_based_matching: true
      max_skill_difference: 300
      max_wait_time_seconds: 120
      prefer_same_region: true
      enable_cross_region: true
      enable_bot_filling: false
      min_players_for_match: 2
    
    # VIP系统配置
    vip_system:
      enable: true
      auto_renewal: true
      grace_period_days: 3
      max_vip_level: 4
      bonus_multipliers:
        experience: 1.5
        coins: 2.0
        energy_recovery: 2.0
        drop_rate: 1.3
      exclusive_features:
        - "priority_queue"
        - "exclusive_rooms"
        - "custom_avatar"
        - "name_color"
        - "special_effects"
        - "premium_support"
    
    # 反作弊配置
    anti_cheat:
      enable: true
      trust_score_threshold: 50
      max_reports_per_day: 10
      auto_ban_threshold: 5
      device_fingerprint_check: true
      suspicious_behavior_detection: true
      speed_hack_detection: true
      memory_modification_detection: true
      auto_punishment: true
    
    # 社交系统配置
    social_system:
      max_friends: 200
      max_guild_members: 100
      enable_friend_recommendations: true
      enable_guild_wars: true
      friend_request_timeout_days: 7
      enable_voice_chat: true
      enable_text_chat: true
      chat_filter_enabled: true
    
    # 游戏经济配置
    game_economy:
      daily_coin_limit: 50000
      energy_recovery_rate: 1              # 每分钟恢复1点体力
      max_energy: 100
      energy_cost_per_game: 1
      coin_to_premium_rate: 100            # 100金币=1钻石
      vip_discount_rate: 0.8
      daily_bonus_enabled: true
      weekly_bonus_enabled: true
    
    # 排行榜配置
    leaderboard:
      enable: true
      update_interval_minutes: 5
      season_duration_days: 30
      max_entries: 10000
      reset_on_new_season: true
      enable_global_ranking: true
      enable_regional_ranking: true
      enable_guild_ranking: true
    
    # 成就系统配置
    achievement_system:
      enable: true
      daily_achievements: true
      weekly_achievements: true
      seasonal_achievements: true
      lifetime_achievements: true
      achievement_rewards: true
      notification_enabled: true
  
  # 认证策略配置
  auth_policy:
    max_login_attempts: 5
    lockout_duration_minutes: 30
    enable_multi_device_login: true
    max_concurrent_sessions: 3
    enable_password_complexity: true
    min_password_length: 8
    password_expire_days: 90
    enable_two_factor_auth: false
    
  # 安全配置
  security:
    enable_rate_limiting: true
    login_rate_limit_per_minute: 10
    api_rate_limit_per_minute: 100
    enable_ip_whitelist: false
    ip_whitelist: []
    enable_ddos_protection: true
    max_requests_per_ip: 1000
    
  # 第三方登录配置
  oauth:
    enable_google: false
    google_client_id: ""
    google_client_secret: ""
    
    enable_github: false
    github_client_id: ""
    github_client_secret: ""
    
    enable_wechat: true
    wechat_app_id: "your_wechat_app_id"
    wechat_app_secret: "your_wechat_app_secret"
    
    # 游戏平台登录
    enable_steam: true
    steam_app_id: "your_steam_app_id"
    steam_api_key: "your_steam_api_key"
    
    enable_qq: true
    qq_app_id: "your_qq_app_id"
    qq_app_key: "your_qq_app_key"
    
  # 监控配置
  monitoring:
    enable_audit_log: true
    enable_metrics: true
    metrics_path: "/metrics"
    enable_health_check: true
    health_check_path: "/health"
    enable_performance_monitoring: true
    log_level: "INFO"
    
  # 通知配置
  notification:
    enable_email: true
    email_smtp_host: "smtp.example.com"
    email_smtp_port: 587
    email_username: "<EMAIL>"
    email_password: "email_password"
    
    enable_sms: false
    sms_provider: "aliyun"
    sms_access_key: ""
    sms_secret_key: ""
    
    enable_push: true
    push_provider: "firebase"
    firebase_server_key: "your_firebase_server_key"
    
  # 缓存配置
  cache:
    user_cache_ttl: 1800                  # 用户信息缓存30分钟
    session_cache_ttl: 3600               # 会话缓存1小时
    server_cache_ttl: 300                 # 服务器信息缓存5分钟
    leaderboard_cache_ttl: 300            # 排行榜缓存5分钟
    
  # 日志配置
  logging:
    level: "INFO"
    file_path: "/var/log/game-auth-service/auth.log"
    max_file_size: "100MB"
    max_files: 10
    enable_console: true
    enable_file: true
    enable_json_format: true
    
  # 备份配置
  backup:
    enable_auto_backup: true
    backup_interval_hours: 6
    backup_retention_days: 30
    backup_path: "/var/backups/game-auth-service"
    
# 环境特定配置
development:
  auth_service:
    database:
      mysql:
        host: "localhost"
        database: "game_auth_dev_db"
      redis:
        database: 1
    logging:
      level: "DEBUG"
      enable_console: true
      
production:
  auth_service:
    server:
      worker_threads: 16
    database:
      mysql:
        host: "prod-mysql-cluster.internal"
        pool_size: 50
      redis:
        host: "prod-redis-cluster.internal"
        pool_size: 20
    security:
      enable_ip_whitelist: true
      ip_whitelist: ["10.0.0.0/8", "**********/12", "***********/16"]
    logging:
      level: "WARN"
      enable_console: false
