# ==================== 游戏微服务系统环境变量配置文件 ====================
# 适用于 game_microservices 项目的环境变量配置
# 支持所有现有模块：database, kafka, logger, network, scheduler, thread_pool, config
# 
# 使用方法：
# 1. 直接设置环境变量：export MYSQL_HOST=localhost
# 2. 使用 .env 文件：在项目根目录创建 .env 文件并复制这些变量
# 3. Docker Compose：在 docker-compose.yml 中使用 env_file 指令
# 4. Kubernetes：创建 ConfigMap 和 Secret

# ==================== 系统基本配置 ====================
SYSTEM_VERSION=1.0.0
SYSTEM_ENVIRONMENT=development
SYSTEM_DEBUG_MODE=true

# ==================== 数据库配置 (MySQL) ====================
# 对应 common/database/mysql_pool.h 模块
# 基本连接配置
MYSQL_HOST=dev-mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=test_db

# MySQL连接池配置
MYSQL_POOL_SIZE=10
MYSQL_MAX_POOL_SIZE=50
MYSQL_MIN_POOL_SIZE=5
MYSQL_CONNECTION_TIMEOUT=30
MYSQL_READ_TIMEOUT=60
MYSQL_WRITE_TIMEOUT=60
MYSQL_IDLE_TIMEOUT=300

# MySQL高级配置
MYSQL_CHARSET=utf8mb4
MYSQL_AUTOCOMMIT=true
MYSQL_ISOLATION_LEVEL=REPEATABLE-READ
MYSQL_SSL_MODE=DISABLED
MYSQL_MAX_ALLOWED_PACKET=16777216

# ==================== Redis配置 ====================
# 对应 common/database/redis_pool.h 模块
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DATABASE=0

# Redis连接池配置
REDIS_POOL_SIZE=8
REDIS_MAX_POOL_SIZE=20
REDIS_CONNECTION_TIMEOUT=5
REDIS_READ_TIMEOUT=3
REDIS_WRITE_TIMEOUT=3

# Redis高级配置
REDIS_MAX_REDIRECTIONS=3
REDIS_KEY_PREFIX=game:
REDIS_ENABLE_CLUSTER=false

# ==================== 网络配置 ====================
# 对应 common/network/ 模块 (inet_address, socket, channel, epoll, event_loop)
NETWORK_BIND_ADDRESS=0.0.0.0
NETWORK_LISTEN_PORT=8080
NETWORK_BACKLOG=1024

# 网络线程配置
NETWORK_WORKER_THREADS=4
NETWORK_IO_THREADS=2

# TCP配置 - 对应 Socket 类
NETWORK_TCP_NODELAY=true
NETWORK_SO_REUSEADDR=true
NETWORK_SO_KEEPALIVE=true
NETWORK_KEEPALIVE_IDLE=7200
NETWORK_KEEPALIVE_INTERVAL=75
NETWORK_KEEPALIVE_COUNT=9

# 网络缓冲区配置
NETWORK_SEND_BUFFER_SIZE=65536
NETWORK_RECV_BUFFER_SIZE=65536

# 网络超时配置
NETWORK_CONNECTION_TIMEOUT=30
NETWORK_READ_TIMEOUT=60
NETWORK_WRITE_TIMEOUT=60
NETWORK_IDLE_TIMEOUT=300

# 网络连接限制
NETWORK_MAX_CONNECTIONS=1000
NETWORK_MAX_CONNECTIONS_PER_IP=10

# Epoll配置 - 对应 Epoll 类
NETWORK_ENABLE_EPOLL=true
NETWORK_EPOLL_TIMEOUT=1000
NETWORK_MAX_EVENTS=1024

# ==================== 日志配置 ====================
# 对应 common/logger/logger.h 模块
# 全局日志配置
LOG_LEVEL=INFO

# 控制台日志配置
LOG_CONSOLE_ENABLED=true
LOG_CONSOLE_COLORED=true
LOG_CONSOLE_LEVEL=DEBUG
LOG_CONSOLE_PATTERN=[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] [%s:%#] %v

# 文件日志配置
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/game_service.log
LOG_FILE_LEVEL=INFO
LOG_FILE_MAX_SIZE=104857600
LOG_FILE_MAX_FILES=10
LOG_FILE_ROTATION_POLICY=size
LOG_FILE_ROTATION_TIME=00:00
LOG_FILE_COMPRESSION=true

# 网络日志配置
LOG_NETWORK_ENABLED=false
LOG_NETWORK_PROTOCOL=tcp
LOG_NETWORK_HOST=log-server
LOG_NETWORK_PORT=514
LOG_NETWORK_LEVEL=ERROR
LOG_NETWORK_FORMAT=json
LOG_NETWORK_TIMEOUT=5000

# 异步日志配置
LOG_ASYNC_ENABLED=true
LOG_ASYNC_QUEUE_SIZE=8192
LOG_ASYNC_THREAD_COUNT=2
LOG_ASYNC_OVERFLOW_POLICY=block
LOG_ASYNC_FLUSH_INTERVAL=1000

# ==================== Kafka配置 ====================
# 对应 common/kafka/ 模块 (kafka_producer, kafka_consumer)

# Kafka生产者配置 - 对应 KafkaProducer 类
KAFKA_PRODUCER_BROKERS=kafka:9092
KAFKA_PRODUCER_CLIENT_ID=game-service-producer
KAFKA_PRODUCER_ACKS=all
KAFKA_PRODUCER_RETRIES=3
KAFKA_PRODUCER_RETRY_BACKOFF_MS=100
KAFKA_PRODUCER_BATCH_SIZE=16384
KAFKA_PRODUCER_LINGER_MS=5
KAFKA_PRODUCER_BUFFER_MEMORY=33554432
KAFKA_PRODUCER_COMPRESSION_TYPE=gzip
KAFKA_PRODUCER_ENABLE_IDEMPOTENCE=true
KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS=5
KAFKA_PRODUCER_REQUEST_TIMEOUT_MS=30000
KAFKA_PRODUCER_DELIVERY_TIMEOUT_MS=120000

# Kafka消费者配置 - 对应 KafkaConsumer 类
KAFKA_CONSUMER_BROKERS=kafka:9092
KAFKA_CONSUMER_GROUP_ID=game-service-group
KAFKA_CONSUMER_CLIENT_ID=game-service-consumer
KAFKA_CONSUMER_AUTO_OFFSET_RESET=earliest
KAFKA_CONSUMER_ENABLE_AUTO_COMMIT=false
KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL_MS=5000
KAFKA_CONSUMER_MAX_POLL_RECORDS=500
KAFKA_CONSUMER_MAX_POLL_INTERVAL_MS=300000
KAFKA_CONSUMER_SESSION_TIMEOUT_MS=30000
KAFKA_CONSUMER_HEARTBEAT_INTERVAL_MS=3000
KAFKA_CONSUMER_FETCH_MIN_BYTES=1024
KAFKA_CONSUMER_FETCH_MAX_WAIT_MS=500
KAFKA_CONSUMER_MAX_PARTITION_FETCH_BYTES=1048576
KAFKA_CONSUMER_ISOLATION_LEVEL=read_committed

# Kafka主题配置
KAFKA_CONSUMER_TOPICS=game.events,user.actions,system.notifications

# ==================== 任务调度器配置 ====================
# 对应 common/scheduler/task_scheduler.h 模块
SCHEDULER_WORKER_THREADS=4
SCHEDULER_MAX_PENDING_TASKS=1000
SCHEDULER_TASK_QUEUE_SIZE=500
SCHEDULER_TICK_INTERVAL_MS=100

# 调度器功能开关
SCHEDULER_ENABLE_METRICS=true
SCHEDULER_ENABLE_TASK_TIMEOUT=true
SCHEDULER_ENABLE_PRIORITY_QUEUE=true
SCHEDULER_ENABLE_TASK_RETRY=true

# 调度器超时和重试配置
SCHEDULER_DEFAULT_TASK_TIMEOUT_MS=10000
SCHEDULER_MAX_RETRY_ATTEMPTS=3
SCHEDULER_RETRY_DELAY_MS=1000
SCHEDULER_RETRY_BACKOFF_MULTIPLIER=2.0
SCHEDULER_MAX_RETRY_DELAY_MS=30000

# 调度器优先级配置
SCHEDULER_HIGH_PRIORITY_THRESHOLD=100
SCHEDULER_NORMAL_PRIORITY_THRESHOLD=50
SCHEDULER_LOW_PRIORITY_THRESHOLD=10

# 调度器监控配置
SCHEDULER_METRICS_INTERVAL_MS=5000
SCHEDULER_ENABLE_TASK_HISTORY=true
SCHEDULER_MAX_HISTORY_SIZE=1000

# ==================== 线程池配置 ====================
# 对应 common/thread_pool/thread_pool.h 模块
THREAD_POOL_CORE_SIZE=4
THREAD_POOL_MAX_SIZE=16
THREAD_POOL_KEEP_ALIVE_MS=60000
THREAD_POOL_QUEUE_CAPACITY=1000

# 线程池高级配置
THREAD_POOL_ALLOW_CORE_TIMEOUT=true
THREAD_POOL_PRESTART_CORE_THREADS=true
THREAD_POOL_REJECTION_POLICY=caller_runs
THREAD_POOL_THREAD_PRIORITY=0
THREAD_POOL_NAME_PREFIX=game-worker-

# 线程池监控配置
THREAD_POOL_ENABLE_MONITORING=true
THREAD_POOL_MONITORING_INTERVAL_MS=5000
THREAD_POOL_ENABLE_THREAD_DUMP=false

# ==================== 服务器配置 ====================
# HTTP服务器配置
SERVER_HTTP_BIND_ADDRESS=0.0.0.0
SERVER_HTTP_PORT=8080
SERVER_HTTP_MAX_CONNECTIONS=1000
SERVER_HTTP_KEEP_ALIVE_TIMEOUT=60
SERVER_HTTP_REQUEST_TIMEOUT=30
SERVER_HTTP_MAX_REQUEST_SIZE=10485760

# WebSocket服务器配置
SERVER_WEBSOCKET_PORT=8081
SERVER_WEBSOCKET_MAX_CONNECTIONS=2000
SERVER_WEBSOCKET_PING_INTERVAL=30
SERVER_WEBSOCKET_PONG_TIMEOUT=10
SERVER_WEBSOCKET_MAX_MESSAGE_SIZE=1048576

# SSL/TLS配置
SERVER_SSL_ENABLED=false
SERVER_SSL_CERT_FILE=certs/server.crt
SERVER_SSL_KEY_FILE=certs/server.key
SERVER_SSL_CA_FILE=certs/ca.crt
SERVER_SSL_PROTOCOLS=TLSv1.2,TLSv1.3

# ==================== 性能监控配置 ====================
MONITORING_ENABLED=true
MONITORING_PORT=8082
MONITORING_PATH=/metrics

# 健康检查配置
MONITORING_HEALTH_CHECK_ENABLED=true
MONITORING_HEALTH_CHECK_PATH=/health
MONITORING_HEALTH_CHECK_INTERVAL=30
MONITORING_HEALTH_CHECK_TIMEOUT=5

# 性能指标配置
MONITORING_METRICS_ENABLE_SYSTEM=true
MONITORING_METRICS_ENABLE_APPLICATION=true
MONITORING_METRICS_ENABLE_CUSTOM=true
MONITORING_METRICS_COLLECTION_INTERVAL=10

# ==================== 游戏业务配置 ====================
GAME_MAX_PLAYERS_PER_ROOM=50
GAME_ROOM_IDLE_TIMEOUT=1800
GAME_PLAYER_SESSION_TIMEOUT=3600

# 游戏机制配置
GAME_MECHANICS_ENABLE_PVP=true
GAME_MECHANICS_ENABLE_PVE=true
GAME_MECHANICS_MAX_LEVEL=100
GAME_MECHANICS_EXPERIENCE_MULTIPLIER=1.0

# 经济系统配置
GAME_ECONOMY_STARTING_GOLD=1000
GAME_ECONOMY_MAX_GOLD=999999999
GAME_ECONOMY_DAILY_BONUS=100

# ==================== 开发和调试配置 ====================
# 开发环境特定配置
DEV_ENABLE_DEBUG_LOGS=true
DEV_ENABLE_PROFILING=false
DEV_ENABLE_MEMORY_TRACKING=false
DEV_MOCK_EXTERNAL_SERVICES=false

# 测试配置
TEST_DATABASE_NAME=game_microservices_test
TEST_REDIS_DATABASE=15
TEST_KAFKA_TOPIC_PREFIX=test_
TEST_LOG_LEVEL=DEBUG

# ==================== 安全配置 ====================
# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ISSUER=game-microservices
JWT_EXPIRATION_TIME=3600

# API密钥配置
API_KEY_HEADER_NAME=X-API-Key
API_KEY_RATE_LIMIT=1000

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-API-Key
CORS_MAX_AGE=3600

# ==================== 部署配置 ====================
# Docker配置
DOCKER_CONTAINER_NAME=game-microservices
DOCKER_NETWORK_NAME=game-network
DOCKER_VOLUME_NAME=game-data

# Kubernetes配置
K8S_NAMESPACE=game-microservices
K8S_SERVICE_NAME=game-service
K8S_DEPLOYMENT_NAME=game-deployment
K8S_REPLICA_COUNT=3

# 云服务配置
CLOUD_PROVIDER=aws
CLOUD_REGION=us-west-2
CLOUD_AVAILABILITY_ZONE=us-west-2a
