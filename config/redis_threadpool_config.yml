# Redis连接池线程池集成配置示例
# 此配置文件展示了如何配置Redis连接池的线程池集成功能

database:
  redis:
    # ==================== 基础Redis连接配置 ====================
    host: "redis"                       # Redis服务器地址
    port: 6379                          # Redis服务器端口
    password: "123456"                  # Redis密码
    database: 0                         # Redis数据库编号（0-15）
    
    # ==================== Redis连接池配置 ====================
    pool:
      initial_size: 5                   # 初始连接数
      max_size: 20                      # 最大连接数
      min_size: 2                       # 最小连接数
      idle_timeout: 300                 # 空闲连接超时时间（秒）
      health_check_interval: 60         # 健康检查间隔（秒）
    
    # ==================== 线程池集成配置 ====================
    thread_pool:
      enable_async_operations: true     # 是否启用异步操作
      core_size: 4                      # 核心线程数
      max_size: 16                      # 最大线程数
      keep_alive_ms: 60000             # 线程保活时间（毫秒）
      queue_capacity: 1000             # 任务队列容量
      enable_monitoring: true          # 是否启用线程池监控

# ==================== 日志配置 ====================
logging:
  level: "INFO"                        # 日志级别
  enable_console: true                 # 是否启用控制台输出
  enable_file: true                    # 是否启用文件输出
  log_file_path: "logs/redis_pool.log" # 日志文件路径
  enable_async: true                   # 是否启用异步日志

# ==================== 应用配置 ====================
application:
  name: "redis-threadpool-demo"        # 应用名称
  version: "1.0.0"                     # 应用版本
  environment: "development"           # 运行环境
