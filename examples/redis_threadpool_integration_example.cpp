/**
 * @file redis_threadpool_integration_example.cpp
 * @brief Redis连接池线程池集成使用示例
 * <AUTHOR> Assistant
 * @date 2025/7/10
 */

#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <future>

#include "common/database/redis_pool.h"
#include "common/config/config_manager.h"
#include "common/logger/logger.h"

using namespace common::database;
using namespace common::config;

class RedisThreadPoolDemo {
public:
    void run() {
        std::cout << "=== Redis连接池线程池集成示例 ===" << std::endl;
        
        try {
            initializeConfig();
            createRedisPool();
            demonstrateBasicOperations();
            demonstrateAsyncOperations();
            demonstrateMonitoring();
            gracefulShutdown();
        } catch (const std::exception& e) {
            std::cerr << "示例运行异常: " << e.what() << std::endl;
        }
    }

private:
    std::unique_ptr<RedisPool> redis_pool_;
    
    void initializeConfig() {
        std::cout << "\n1. 初始化配置管理器..." << std::endl;
        auto& config = ConfigManager::getInstance();
        config.set("database.redis.host", "redis");
        config.set("database.redis.port", "6379");
        config.set("database.redis.password", "123456");
        config.set("database.redis.thread_pool.enable_async_operations", "true");
        config.set("database.redis.thread_pool.core_size", "4");
        std::cout << "   配置设置成功" << std::endl;
    }
    
    void createRedisPool() {
        std::cout << "\n2. 创建Redis连接池..." << std::endl;
        redis_pool_ = std::make_unique<RedisPool>();
        redis_pool_->start();
        redis_pool_->enableConfigHotReload();
        std::cout << "   Redis连接池创建成功" << std::endl;
    }
    
    void demonstrateBasicOperations() {
        std::cout << "\n3. 演示基础操作..." << std::endl;
        auto conn = redis_pool_->getConnection();
        if (conn) {
            bool success = conn->set("demo:key1", "Hello Redis ThreadPool!");
            std::cout << "   SET操作: " << (success ? "成功" : "失败") << std::endl;
            std::string value = conn->get("demo:key1");
            std::cout << "   GET操作: " << value << std::endl;
            redis_pool_->returnConnection(conn);
        }
    }
    
    void demonstrateAsyncOperations() {
        std::cout << "\n4. 演示异步操作..." << std::endl;
        if (!redis_pool_->isAsyncOperationsEnabled()) {
            std::cout << "   异步操作未启用" << std::endl;
            return;
        }
        
        auto future_connections = redis_pool_->createConnectionsAsync(3);
        redis_pool_->performAsyncHealthCheck();
        redis_pool_->cleanupExpiredConnectionsAsync();
        redis_pool_->warmupPoolAsync(10);
        
        try {
            auto connections = future_connections.get();
            std::cout << "   异步创建连接完成: " << connections.size() << " 个" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "   异步操作异常: " << e.what() << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }
    
    void demonstrateMonitoring() {
        std::cout << "\n5. 演示性能监控..." << std::endl;
        std::cout << "   总连接数: " << redis_pool_->getTotalConnections() << std::endl;
        std::cout << "   活跃连接数: " << redis_pool_->getActiveConnections() << std::endl;
        std::cout << "   空闲连接数: " << redis_pool_->getIdleConnections() << std::endl;
        
        if (redis_pool_->isAsyncOperationsEnabled()) {
            std::cout << "\n   线程池状态:" << std::endl;
            std::cout << redis_pool_->getThreadPoolStatus() << std::endl;
        }
    }
    
    void gracefulShutdown() {
        std::cout << "\n6. 优雅关闭..." << std::endl;
        if (redis_pool_) {
            redis_pool_->stop();
            std::cout << "   Redis连接池已关闭" << std::endl;
        }
    }
};

int main() {
    try {
        RedisThreadPoolDemo demo;
        demo.run();
        std::cout << "\n=== 示例运行完成 ===" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return 1;
    }
}
