/**
 * @file main.cpp
 * @brief 认证服务启动入口 - 游戏微服务架构的认证服务主程序
 * <AUTHOR>
 * @date 2025/7/21
 * @version 1.0
 *
 * 功能特性:
 * - 服务启动和初始化
 * - 配置文件加载
 * - 信号处理
 * - 优雅关闭
 * - 错误处理和恢复
 *
 * 启动流程:
 * 1. 加载配置文件
 * 2. 初始化日志系统
 * 3. 创建认证服务实例
 * 4. 初始化服务组件
 * 5. 启动HTTP服务器
 * 6. 注册到API Gateway
 * 7. 等待信号处理
 */

#include "core_services/auth_service/auth_service.h"
#include "common/config/config_manager.h"
#include "common/logger/logger.h"
#include <iostream>
#include <csignal>
#include <memory>
#include <thread>
#include <chrono>

using namespace core_services::auth_service;

// 全局服务实例指针
std::unique_ptr<AuthService> g_auth_service = nullptr;

/**
 * @brief 信号处理函数
 * @param signal 信号值
 */
void signalHandler(int signal) {
    const char* signal_name = "UNKNOWN";
    switch (signal) {
        case SIGINT: signal_name = "SIGINT"; break;
        case SIGTERM: signal_name = "SIGTERM"; break;
        case SIGHUP: signal_name = "SIGHUP"; break;
        case SIGABRT: signal_name = "SIGABRT"; break;
        case SIGSEGV: signal_name = "SIGSEGV"; break;
        default: break;
    }

    std::cerr << "收到信号: " << signal_name << " (" << signal << ")" << std::endl;

    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_INFO("收到终止信号: " + std::string(signal_name) + "，开始优雅关闭服务...");
            if (g_auth_service) {
                g_auth_service->stop();
            }
            break;
        case SIGHUP:
            LOG_INFO("收到重新加载信号，重启服务...");
            if (g_auth_service) {
                g_auth_service->restart();
            }
            break;
        case SIGABRT:
        case SIGSEGV:
            // 对于严重错误信号，记录信息并立即退出
            std::cerr << "程序异常终止，信号: " << signal_name << std::endl;
            LOG_ERROR("程序异常终止，信号: " + std::string(signal_name));

            // 对于严重错误，不尝试清理，直接退出避免进一步损坏
            // 设置全局指针为nullptr，避免析构时的问题
            if (g_auth_service) {
                g_auth_service.release(); // 释放所有权但不调用析构函数
            }

            // 使用_exit避免调用析构函数和atexit处理器
            _exit(signal == SIGABRT ? 134 : 139);
            break;
        default:
            LOG_WARNING("收到未处理的信号: " + std::to_string(signal));
            break;
    }
}

/**
 * @brief 注册信号处理器
 */
void registerSignalHandlers() {
    std::signal(SIGINT, signalHandler);   // Ctrl+C
    std::signal(SIGTERM, signalHandler);  // 终止信号
    std::signal(SIGHUP, signalHandler);   // 重新加载信号
    std::signal(SIGABRT, signalHandler);  // 异常终止信号
    std::signal(SIGSEGV, signalHandler);  // 段错误信号
    
    // 忽略SIGPIPE信号（避免网络连接断开时程序崩溃）
    std::signal(SIGPIPE, SIG_IGN);
}

/**
 * @brief 打印启动横幅
 */
void printBanner() {
    std::cout << R"(
    ╔══════════════════════════════════════════════════════════════╗
    ║                    游戏微服务认证服务                          ║
    ║                   Game Microservices Auth Service            ║
    ║                                                              ║
    ║  版本: 1.0.0                                                 ║
    ║  作者: 29108                                                 ║
    ║  日期: 2025/7/21                                             ║
    ║                                                              ║
    ║  功能特性:                                                    ║
    ║  • 用户注册和登录认证                                          ║
    ║  • JWT令牌管理                                               ║
    ║  • 游戏登录和服务器分配                                        ║
    ║  • 多设备会话管理                                             ║
    ║  • 实时状态同步                                               ║
    ║  • 与API Gateway协作                                         ║
    ║  • 基于Kafka的事件驱动                                        ║
    ╚══════════════════════════════════════════════════════════════╝
)" << std::endl;
}

/**
 * @brief 加载配置文件
 * @param config_file 配置文件路径
 * @return 加载成功返回true
 */
bool loadConfiguration(const std::string& config_file) {
    try {
        auto& config_manager = common::config::ConfigManager::getInstance();
        
        // 加载配置文件
        if (!config_manager.loadFromFile(config_file)) {
            std::cerr << "错误: 无法加载配置文件: " << config_file << std::endl;
            return false;
        }
        
        LOG_INFO("配置文件加载成功: " + config_file);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "错误: 加载配置文件时发生异常: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief 初始化日志系统
 * @return 初始化成功返回true
 */
bool initializeLogging() {
    try {
        auto& config_manager = common::config::ConfigManager::getInstance();
        
        // 获取日志配置
        std::string log_level = config_manager.get<std::string>("logging.level", "INFO");
        std::string log_file = config_manager.get<std::string>("logging.file", "logs/auth_service.log");
        bool enable_console = config_manager.get<bool>("logging.console", true);
        
        // 初始化日志系统
        common::logger::Logger::getInstance().initializeFromConfig();
        
        LOG_INFO("日志系统初始化成功");
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "错误: 初始化日志系统时发生异常: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief 创建和初始化认证服务
 * @return 服务实例指针，失败返回nullptr
 */
std::unique_ptr<AuthService> createAuthService() {
    try {
        // 从配置管理器加载服务配置
        auto config = AuthService::Config::fromConfigManager();
        
        // 验证配置
        std::string validation_error = config.validate();
        if (!validation_error.empty()) {
            LOG_ERROR("服务配置验证失败: " + validation_error);
            return nullptr;
        }
        
        // 创建服务实例
        auto service = std::make_unique<AuthService>(config);
        
        // 初始化服务
        if (!service->initialize()) {
            LOG_ERROR("认证服务初始化失败");
            return nullptr;
        }
        
        LOG_INFO("认证服务创建和初始化成功");
        return service;
    } catch (const std::exception& e) {
        LOG_ERROR("创建认证服务时发生异常: " + std::string(e.what()));
        return nullptr;
    }
}

/**
 * @brief 等待服务运行
 * @param service 服务实例
 */
void waitForService(AuthService* service) {
    LOG_INFO("认证服务已启动，等待请求处理...");
    LOG_INFO("按 Ctrl+C 优雅关闭服务");
    
    // 定期检查服务状态
    while (service && service->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 可以在这里添加定期健康检查
        // 例如：检查数据库连接、Redis连接等
    }
    
    LOG_INFO("服务已停止运行");
}

/**
 * @brief 主函数
 * @param argc 参数数量
 * @param argv 参数数组
 * @return 程序退出代码
 */
int main(int argc, char* argv[]) {
    // 打印启动横幅
    printBanner();
    
    try {
        // 解析命令行参数
        std::string config_file = "../../../../config/auth_service_config.yml";
        if (argc > 1) {
            config_file = argv[1];
        }
        
        std::cout << "使用配置文件: " << config_file << std::endl;
        
        // 加载配置文件
        if (!loadConfiguration(config_file)) {
            return EXIT_FAILURE;
        }
        
        // 初始化日志系统
        if (!initializeLogging()) {
            return EXIT_FAILURE;
        }
        
        // 注册信号处理器
        registerSignalHandlers();
        
        LOG_INFO("=== 认证服务启动开始 ===");
        
        // 创建和初始化认证服务
        g_auth_service = createAuthService();
        if (!g_auth_service) {
            LOG_ERROR("认证服务创建失败");
            return EXIT_FAILURE;
        }
        
        // 启动服务
        if (!g_auth_service->start()) {
            LOG_ERROR("认证服务启动失败");
            return EXIT_FAILURE;
        }
        
        LOG_INFO("=== 认证服务启动完成 ===");

        // 等待服务运行
        waitForService(g_auth_service.get());

        LOG_INFO("=== 认证服务关闭开始 ===");

        // 先停止服务，再清理资源
        if (g_auth_service) {
            try {
                LOG_INFO("正在停止认证服务...");
                bool stop_success = g_auth_service->stop();
                if (stop_success) {
                    LOG_INFO("认证服务已停止");
                } else {
                    LOG_WARNING("认证服务停止时返回失败状态，但继续清理资源");
                }

                LOG_INFO("正在清理认证服务资源...");
                g_auth_service.reset();
                LOG_INFO("认证服务资源已清理");
            } catch (const std::exception& e) {
                LOG_ERROR("停止认证服务时发生异常: " + std::string(e.what()));
                std::cerr << "停止认证服务时发生异常: " << e.what() << std::endl;
                // 不要返回错误，继续清理
            } catch (...) {
                LOG_ERROR("停止认证服务时发生未知异常");
                std::cerr << "停止认证服务时发生未知异常" << std::endl;
                // 不要返回错误，继续清理
            }
        }

        LOG_INFO("=== 认证服务关闭完成 ===");

        // 强制刷新日志
        try {
            common::logger::Logger::getInstance().flushAll();
        } catch (...) {
            // 忽略日志刷新异常
        }

        return EXIT_SUCCESS;
        
    } catch (const std::exception& e) {
        std::cerr << "程序运行时发生异常: " << e.what() << std::endl;
        LOG_ERROR("程序运行时发生异常: " + std::string(e.what()));
        
        // 清理资源
        if (g_auth_service) {
            g_auth_service->stop();
            g_auth_service.reset();
        }
        
        return EXIT_FAILURE;
    } catch (...) {
        std::cerr << "程序运行时发生未知异常" << std::endl;
        LOG_ERROR("程序运行时发生未知异常");
        
        // 清理资源
        if (g_auth_service) {
            g_auth_service->stop();
            g_auth_service.reset();
        }
        
        return EXIT_FAILURE;
    }
}
