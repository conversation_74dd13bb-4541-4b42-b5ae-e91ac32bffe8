# 认证服务CMake构建文件
# Game Microservices Auth Service CMakeLists.txt
# 版本: 1.0.0
# 作者: 29108
# 日期: 2025/7/21

cmake_minimum_required(VERSION 3.16)

# 项目信息
project(auth_service
    VERSION 1.0.0
    DESCRIPTION "Game Microservices Authentication Service"
    LANGUAGES CXX
)

# C++标准设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0 -Wall -Wextra")
    add_definitions(-DAUTH_SERVICE_DEBUG)
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -DNDEBUG")
endif()

# 包含目录
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 查找依赖包
find_package(PkgConfig REQUIRED)
find_package(Threads REQUIRED)
find_package(OpenSSL REQUIRED)

# 查找nlohmann/json
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    find_path(JSON_INCLUDE_DIR
        NAMES nlohmann/json.hpp
        PATHS /usr/include /usr/local/include
    )
    if(NOT JSON_INCLUDE_DIR)
        message(FATAL_ERROR "nlohmann/json not found. Please install nlohmann-json3-dev")
    endif()
endif()

# 查找MySQL Connector/C++
find_path(MYSQL_CONNECTOR_INCLUDE_DIR
    NAMES mysql_connection.h mysql/mysql.h
    PATHS
        /usr/include/mysql-cppconn-8
        /usr/local/include/mysql-cppconn-8
        /usr/include/mysql
        /usr/include/mysql++
        /usr/local/include/mysql
)

find_library(MYSQL_CONNECTOR_LIB
    NAMES
        mysqlcppconn8
        mysqlcppconn
        mysqlclient
        mysql
    PATHS
        /usr/lib
        /usr/local/lib
        /usr/lib/x86_64-linux-gnu
        /usr/lib64
)

# 查找Redis客户端库
find_path(REDIS_INCLUDE_DIR
    NAMES hiredis/hiredis.h
    PATHS /usr/include /usr/local/include
)

find_library(REDIS_LIB
    NAMES hiredis
    PATHS /usr/lib /usr/local/lib
)

# 查找Kafka客户端库
find_library(RDKAFKA_LIB
    NAMES rdkafka
    PATHS /usr/lib /usr/local/lib
)

# 查找bcrypt库
find_library(BCRYPT_LIB
    NAMES bcrypt
    PATHS /usr/lib /usr/local/lib
)

# 源文件列表
set(AUTH_SERVICE_SOURCES
    # 数据模型
    user_models.cpp

    # 核心组件
    jwt_manager.cpp
    password_manager.cpp
    session_manager.cpp
    user_repository.cpp
    database_initializer.cpp

    # 主服务类
    auth_service.cpp

    # 启动入口
    main.cpp
)

# 头文件列表
set(AUTH_SERVICE_HEADERS
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/user_models.h
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/jwt_manager.h
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/password_manager.h
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/session_manager.h
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/user_repository.h
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/database_initializer.h
    ${CMAKE_SOURCE_DIR}/include/core_services/auth_service/auth_service.h
)

# Common模块库（根据实际的库名称）
set(COMMON_LIBS
    common_config_lib
    database_lib
    http_module
    network_lib
    messaging_lib
    common_logger_lib
    task_scheduler_lib
    thread_pool_lib
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${AUTH_SERVICE_SOURCES} ${AUTH_SERVICE_HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    # Common模块
    ${COMMON_LIBS}

    # 系统库
    Threads::Threads

    # 加密库
    OpenSSL::SSL
    OpenSSL::Crypto

    # 其他库
    dl
    rt
)

# 条件链接可选依赖
if(MYSQL_CONNECTOR_LIB)
    target_link_libraries(${PROJECT_NAME} ${MYSQL_CONNECTOR_LIB})
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_MYSQL)
endif()

if(REDIS_LIB)
    target_link_libraries(${PROJECT_NAME} ${REDIS_LIB})
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_REDIS)
endif()

if(RDKAFKA_LIB)
    target_link_libraries(${PROJECT_NAME} ${RDKAFKA_LIB})
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_KAFKA)
endif()

if(BCRYPT_LIB)
    target_link_libraries(${PROJECT_NAME} ${BCRYPT_LIB})
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_BCRYPT)
endif()

# JSON库
if(nlohmann_json_FOUND)
    target_link_libraries(${PROJECT_NAME} nlohmann_json::nlohmann_json)
    target_compile_definitions(${PROJECT_NAME} PRIVATE HAVE_NLOHMANN_JSON)
endif()

# 包含目录
if(MYSQL_CONNECTOR_INCLUDE_DIR)
    target_include_directories(${PROJECT_NAME} PRIVATE ${MYSQL_CONNECTOR_INCLUDE_DIR})
endif()

if(REDIS_INCLUDE_DIR)
    target_include_directories(${PROJECT_NAME} PRIVATE ${REDIS_INCLUDE_DIR})
endif()

if(JSON_INCLUDE_DIR)
    target_include_directories(${PROJECT_NAME} PRIVATE ${JSON_INCLUDE_DIR})
endif()

if(OPENSSL_INCLUDE_DIR)
    target_include_directories(${PROJECT_NAME} PRIVATE ${OPENSSL_INCLUDE_DIR})
endif()

# 编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    AUTH_SERVICE_VERSION="${PROJECT_VERSION}"
    AUTH_SERVICE_NAME="${PROJECT_NAME}"
)

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    COMPONENT auth_service
)

# 安装配置文件
install(FILES
    ${CMAKE_SOURCE_DIR}/config/auth_service_config.yml
    DESTINATION etc/game-microservices
    COMPONENT auth_service
)

# 调试信息
message(STATUS "=== Auth Service Build Configuration ===")
message(STATUS "Project: ${PROJECT_NAME} v${PROJECT_VERSION}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "MySQL Connector Include: ${MYSQL_CONNECTOR_INCLUDE_DIR}")
message(STATUS "MySQL Connector Library: ${MYSQL_CONNECTOR_LIB}")
message(STATUS "Redis Include: ${REDIS_INCLUDE_DIR}")
message(STATUS "Redis Library: ${REDIS_LIB}")
message(STATUS "JSON Include: ${JSON_INCLUDE_DIR}")
message(STATUS "RdKafka Library: ${RDKAFKA_LIB}")
message(STATUS "BCrypt Library: ${BCRYPT_LIB}")
message(STATUS "==========================================")

# 检查可选依赖并给出建议
message(STATUS "=== 依赖检查结果 ===")

if(MYSQL_CONNECTOR_LIB)
    message(STATUS "✓ MySQL Connector/C++: ${MYSQL_CONNECTOR_LIB}")
else()
    message(STATUS "✗ MySQL Connector/C++ not found")
    message(STATUS "  Install: sudo apt-get install libmysqlcppconn-dev")
    message(STATUS "  Note: Database features will be disabled")
endif()

if(REDIS_LIB)
    message(STATUS "✓ Redis (hiredis): ${REDIS_LIB}")
else()
    message(STATUS "✗ Redis client library not found")
    message(STATUS "  Install: sudo apt-get install libhiredis-dev")
    message(STATUS "  Note: Caching features will be disabled")
endif()

if(nlohmann_json_FOUND OR JSON_INCLUDE_DIR)
    message(STATUS "✓ nlohmann/json: Available")
else()
    message(STATUS "✗ nlohmann/json not found")
    message(STATUS "  Install: sudo apt-get install nlohmann-json3-dev")
    message(STATUS "  Note: JSON processing may not work")
endif()

if(RDKAFKA_LIB)
    message(STATUS "✓ librdkafka: ${RDKAFKA_LIB}")
else()
    message(STATUS "✗ librdkafka not found")
    message(STATUS "  Install: sudo apt-get install librdkafka-dev")
    message(STATUS "  Note: Kafka features will be disabled")
endif()

if(BCRYPT_LIB)
    message(STATUS "✓ BCrypt: ${BCRYPT_LIB}")
else()
    message(STATUS "✗ BCrypt library not found")
    message(STATUS "  Note: Using OpenSSL for password hashing")
endif()

message(STATUS "========================")