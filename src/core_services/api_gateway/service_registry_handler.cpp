/**
 * @file service_registry_handler.cpp
 * @brief API网关服务注册处理器实现
 * @details 处理服务注册、注销、心跳等HTTP请求的具体实现
 * 
 * <AUTHOR>
 * @date 2025/7/25
 */

#include "core_services/api_gateway/service_registry_handler.h"
#include "common/logger/logger.h"
#include <sstream>

namespace core_services {
namespace api_gateway {

    ServiceRegistryHandler::ServiceRegistryHandler(std::shared_ptr<ServiceDiscovery> service_discovery)
        : service_discovery_(service_discovery) {
        LOG_INFO("服务注册处理器初始化完成");
    }

    void ServiceRegistryHandler::registerRoutes(common::http::HttpServer* http_server) {
        if (!http_server) {
            LOG_ERROR("HTTP服务器为空，无法注册路由");
            return;
        }

        // 服务注册端点
        http_server->post("/api/v1/services/register",
            [this](const common::http::HttpRequest& req, common::http::HttpResponse& res) {
                this->handleServiceRegister(req, res);
            });

        // 服务注销端点
        http_server->del("/api/v1/services/deregister",
            [this](const common::http::HttpRequest& req, common::http::HttpResponse& res) {
                this->handleServiceDeregister(req, res);
            });

        // 服务心跳端点
        http_server->post("/api/v1/services/heartbeat",
            [this](const common::http::HttpRequest& req, common::http::HttpResponse& res) {
                this->handleServiceHeartbeat(req, res);
            });

        // 服务列表查询端点
        http_server->get("/api/v1/services",
            [this](const common::http::HttpRequest& req, common::http::HttpResponse& res) {
                this->handleServiceList(req, res);
            });

        // 服务统计信息端点
        http_server->get("/api/v1/services/stats",
            [this](const common::http::HttpRequest& req, common::http::HttpResponse& res) {
                this->handleServiceStats(req, res);
            });

        // OPTIONS请求处理（CORS预检）
        http_server->options("/api/v1/services/*",
            [this](const common::http::HttpRequest& req, common::http::HttpResponse& res) {
                setCorsHeaders(res);
                res.setStatus(200);
                res.setBody("");
            });

        LOG_INFO("服务注册路由注册完成");
    }

    void ServiceRegistryHandler::handleServiceRegister(const common::http::HttpRequest& req, 
                                                     common::http::HttpResponse& res) {
        try {
            setCorsHeaders(res);
            setJsonHeaders(res);

            // 解析请求体
            std::string request_body = req.getBody();
            if (request_body.empty()) {
                auto error_response = buildErrorResponse("EMPTY_REQUEST_BODY", "请求体不能为空");
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            nlohmann::json request_json;
            try {
                request_json = nlohmann::json::parse(request_body);
            } catch (const nlohmann::json::parse_error& e) {
                auto error_response = buildErrorResponse("INVALID_JSON", "无效的JSON格式: " + std::string(e.what()));
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            // 验证请求格式
            if (!validateServiceRegisterRequest(request_json)) {
                auto error_response = buildErrorResponse("INVALID_REQUEST", "请求格式无效");
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            // 构建服务信息
            auto service_info = buildServiceInfoFromJson(request_json);

            // 注册服务
            if (service_discovery_->registerService(service_info)) {
                auto success_response = buildSuccessResponse("服务注册成功", {
                    {"service_name", service_info.service_name},
                    {"host", service_info.host},
                    {"port", service_info.port}
                });
                res.setStatus(200);
                res.setBody(success_response.dump());
                
                LOG_INFO("服务注册成功: " + service_info.service_name + 
                        " (" + service_info.host + ":" + std::to_string(service_info.port) + ")");
            } else {
                auto error_response = buildErrorResponse("REGISTRATION_FAILED", "服务注册失败");
                res.setStatus(500);
                res.setBody(error_response.dump());
            }

        } catch (const std::exception& e) {
            LOG_ERROR("处理服务注册请求异常: " + std::string(e.what()));
            auto error_response = buildErrorResponse("INTERNAL_ERROR", "内部服务器错误");
            res.setStatus(500);
            res.setBody(error_response.dump());
        }
    }

    void ServiceRegistryHandler::handleServiceDeregister(const common::http::HttpRequest& req, 
                                                       common::http::HttpResponse& res) {
        try {
            setCorsHeaders(res);
            setJsonHeaders(res);

            // 解析请求体
            std::string request_body = req.getBody();
            if (request_body.empty()) {
                auto error_response = buildErrorResponse("EMPTY_REQUEST_BODY", "请求体不能为空");
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            nlohmann::json request_json;
            try {
                request_json = nlohmann::json::parse(request_body);
            } catch (const nlohmann::json::parse_error& e) {
                auto error_response = buildErrorResponse("INVALID_JSON", "无效的JSON格式: " + std::string(e.what()));
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            // 验证必需字段
            if (!request_json.contains("service_name") || !request_json.contains("host") || !request_json.contains("port")) {
                auto error_response = buildErrorResponse("MISSING_FIELDS", "缺少必需字段: service_name, host, port");
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            std::string service_name = request_json["service_name"];
            std::string host = request_json["host"];
            int port = request_json["port"];

            // 注销服务
            if (service_discovery_->deregisterService(service_name, host, port)) {
                auto success_response = buildSuccessResponse("服务注销成功", {
                    {"service_name", service_name},
                    {"host", host},
                    {"port", port}
                });
                res.setStatus(200);
                res.setBody(success_response.dump());
                
                LOG_INFO("服务注销成功: " + service_name + " (" + host + ":" + std::to_string(port) + ")");
            } else {
                auto error_response = buildErrorResponse("DEREGISTRATION_FAILED", "服务注销失败或服务不存在");
                res.setStatus(404);
                res.setBody(error_response.dump());
            }

        } catch (const std::exception& e) {
            LOG_ERROR("处理服务注销请求异常: " + std::string(e.what()));
            auto error_response = buildErrorResponse("INTERNAL_ERROR", "内部服务器错误");
            res.setStatus(500);
            res.setBody(error_response.dump());
        }
    }

    void ServiceRegistryHandler::handleServiceHeartbeat(const common::http::HttpRequest& req, 
                                                      common::http::HttpResponse& res) {
        try {
            setCorsHeaders(res);
            setJsonHeaders(res);

            // 解析请求体
            std::string request_body = req.getBody();
            if (request_body.empty()) {
                auto error_response = buildErrorResponse("EMPTY_REQUEST_BODY", "请求体不能为空");
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            nlohmann::json request_json;
            try {
                request_json = nlohmann::json::parse(request_body);
            } catch (const nlohmann::json::parse_error& e) {
                auto error_response = buildErrorResponse("INVALID_JSON", "无效的JSON格式: " + std::string(e.what()));
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            // 验证必需字段
            if (!request_json.contains("service_name") || !request_json.contains("host") || !request_json.contains("port")) {
                auto error_response = buildErrorResponse("MISSING_FIELDS", "缺少必需字段: service_name, host, port");
                res.setStatus(400);
                res.setBody(error_response.dump());
                return;
            }

            std::string service_name = request_json["service_name"];
            std::string host = request_json["host"];
            int port = request_json["port"];

            // 更新心跳
            if (service_discovery_->updateHeartbeat(service_name, host, port)) {
                auto success_response = buildSuccessResponse("心跳更新成功");
                res.setStatus(200);
                res.setBody(success_response.dump());
            } else {
                auto error_response = buildErrorResponse("HEARTBEAT_FAILED", "心跳更新失败或服务不存在");
                res.setStatus(404);
                res.setBody(error_response.dump());
            }

        } catch (const std::exception& e) {
            LOG_ERROR("处理服务心跳请求异常: " + std::string(e.what()));
            auto error_response = buildErrorResponse("INTERNAL_ERROR", "内部服务器错误");
            res.setStatus(500);
            res.setBody(error_response.dump());
        }
    }

    void ServiceRegistryHandler::handleServiceList(const common::http::HttpRequest& req, 
                                                 common::http::HttpResponse& res) {
        try {
            setCorsHeaders(res);
            setJsonHeaders(res);

            // 获取查询参数
            std::string service_name = req.getQueryParam("service");
            bool healthy_only = req.getQueryParam("healthy") == "true";

            nlohmann::json services_json = nlohmann::json::array();

            if (!service_name.empty()) {
                // 查询特定服务
                auto services = healthy_only ? 
                    service_discovery_->getHealthyServices(service_name) :
                    service_discovery_->getServices(service_name);

                for (const auto& service : services) {
                    services_json.push_back(serviceInfoToJson(service));
                }
            } else {
                // 查询所有服务（需要扩展ServiceDiscovery接口）
                auto stats = service_discovery_->getServiceStats();
                nlohmann::json stats_json;
                for (const auto& [key, value] : stats) {
                    stats_json[key] = value;
                }
                services_json = stats_json;
            }

            auto success_response = buildSuccessResponse("查询成功", {
                {"services", services_json},
                {"count", services_json.size()}
            });
            res.setStatus(200);
            res.setBody(success_response.dump());

        } catch (const std::exception& e) {
            LOG_ERROR("处理服务列表请求异常: " + std::string(e.what()));
            auto error_response = buildErrorResponse("INTERNAL_ERROR", "内部服务器错误");
            res.setStatus(500);
            res.setBody(error_response.dump());
        }
    }

    void ServiceRegistryHandler::handleServiceStats(const common::http::HttpRequest& req, 
                                                  common::http::HttpResponse& res) {
        try {
            setCorsHeaders(res);
            setJsonHeaders(res);

            auto stats = service_discovery_->getServiceStats();
            nlohmann::json stats_json;
            for (const auto& [key, value] : stats) {
                stats_json[key] = value;
            }

            auto success_response = buildSuccessResponse("统计信息获取成功", stats_json);
            res.setStatus(200);
            res.setBody(success_response.dump());

        } catch (const std::exception& e) {
            LOG_ERROR("处理服务统计请求异常: " + std::string(e.what()));
            auto error_response = buildErrorResponse("INTERNAL_ERROR", "内部服务器错误");
            res.setStatus(500);
            res.setBody(error_response.dump());
        }
    }

    nlohmann::json ServiceRegistryHandler::buildSuccessResponse(const std::string& message,
                                                              const nlohmann::json& data) {
        nlohmann::json response;
        response["success"] = true;
        response["message"] = message;
        response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        if (!data.empty()) {
            response["data"] = data;
        }

        return response;
    }

    nlohmann::json ServiceRegistryHandler::buildErrorResponse(const std::string& error_code,
                                                            const std::string& error_message) {
        nlohmann::json response;
        response["success"] = false;
        response["error_code"] = error_code;
        response["error_message"] = error_message;
        response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        return response;
    }

    bool ServiceRegistryHandler::validateServiceRegisterRequest(const nlohmann::json& request_json) {
        // 检查必需字段
        if (!request_json.contains("service_name") || !request_json.contains("host") ||
            !request_json.contains("port")) {
            return false;
        }

        // 检查字段类型
        if (!request_json["service_name"].is_string() ||
            !request_json["host"].is_string() ||
            !request_json["port"].is_number_integer()) {
            return false;
        }

        // 检查端口范围
        int port = request_json["port"];
        if (port <= 0 || port > 65535) {
            return false;
        }

        return true;
    }

    ServiceDiscovery::ServiceInfo ServiceRegistryHandler::buildServiceInfoFromJson(const nlohmann::json& json) {
        ServiceDiscovery::ServiceInfo service_info;

        service_info.service_name = json["service_name"];
        service_info.host = json["host"];
        service_info.port = json["port"];

        // 可选字段
        if (json.contains("service_version")) {
            service_info.service_version = json["service_version"];
        }

        if (json.contains("health_check_endpoint")) {
            service_info.health_check_endpoint = json["health_check_endpoint"];
        }

        if (json.contains("weight")) {
            service_info.weight = json["weight"];
        }

        if (json.contains("endpoints") && json["endpoints"].is_array()) {
            for (const auto& endpoint : json["endpoints"]) {
                if (endpoint.is_string()) {
                    service_info.endpoints.push_back(endpoint);
                }
            }
        }

        if (json.contains("metadata") && json["metadata"].is_object()) {
            for (auto it = json["metadata"].begin(); it != json["metadata"].end(); ++it) {
                if (it.value().is_string()) {
                    service_info.metadata[it.key()] = it.value();
                }
            }
        }

        return service_info;
    }

    nlohmann::json ServiceRegistryHandler::serviceInfoToJson(const ServiceDiscovery::ServiceInfo& service_info) {
        nlohmann::json json;

        json["service_name"] = service_info.service_name;
        json["service_version"] = service_info.service_version;
        json["host"] = service_info.host;
        json["port"] = service_info.port;
        json["health_check_endpoint"] = service_info.health_check_endpoint;
        json["healthy"] = service_info.healthy;
        json["weight"] = service_info.weight;

        json["endpoints"] = nlohmann::json::array();
        for (const auto& endpoint : service_info.endpoints) {
            json["endpoints"].push_back(endpoint);
        }

        json["metadata"] = nlohmann::json::object();
        for (const auto& [key, value] : service_info.metadata) {
            json["metadata"][key] = value;
        }

        json["last_heartbeat"] = std::chrono::duration_cast<std::chrono::seconds>(
            service_info.last_heartbeat.time_since_epoch()).count();

        return json;
    }

    void ServiceRegistryHandler::setCorsHeaders(common::http::HttpResponse& res) {
        res.setHeader("Access-Control-Allow-Origin", "*");
        res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
        res.setHeader("Access-Control-Max-Age", "86400");
    }

    void ServiceRegistryHandler::setJsonHeaders(common::http::HttpResponse& res) {
        res.setHeader("Content-Type", "application/json; charset=utf-8");
    }

} // namespace api_gateway
} // namespace core_services
