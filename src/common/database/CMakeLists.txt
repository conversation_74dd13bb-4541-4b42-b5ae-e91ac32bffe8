cmake_minimum_required(VERSION 3.10)

# 数据库连接池库
add_library(database_lib
        mysql_pool.cpp
        redis_pool.cpp
)

# 设置C++标准
target_compile_features(database_lib PUBLIC cxx_std_17)

# 添加头文件搜索路径
target_include_directories(database_lib PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

# 查找MySQL库
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(MYSQL QUIET mysqlclient)
endif()

if(NOT MYSQL_FOUND)
    # 手动查找MySQL
    find_path(MYSQL_INCLUDE_DIR
        NAMES mysql/mysql.h mysql.h
        PATHS
            /usr/include
            /usr/local/include
            /usr/include/mysql
            /usr/local/include/mysql
            /opt/mysql/include
        PATH_SUFFIXES mysql
    )

    find_library(MYSQL_LIBRARY
        NAMES mysqlclient mysql libmysql
        PATHS
            /usr/lib
            /usr/local/lib
            /usr/lib/mysql
            /usr/local/lib/mysql
            /opt/mysql/lib
            /usr/lib/x86_64-linux-gnu
        PATH_SUFFIXES mysql
    )

    if(MYSQL_INCLUDE_DIR AND MYSQL_LIBRARY)
        set(MYSQL_FOUND TRUE)
        set(MYSQL_INCLUDE_DIRS ${MYSQL_INCLUDE_DIR})
        set(MYSQL_LIBRARIES ${MYSQL_LIBRARY})
    endif()
endif()

# 查找并链接线程库
find_package(Threads REQUIRED)

# 链接库
target_link_libraries(database_lib
    PUBLIC
        Threads::Threads
)

if(MYSQL_FOUND)
    target_include_directories(database_lib PRIVATE ${MYSQL_INCLUDE_DIRS})
    target_link_libraries(database_lib PRIVATE ${MYSQL_LIBRARIES})
    target_compile_definitions(database_lib PRIVATE MYSQL_FOUND)
    message(STATUS "Found MySQL: ${MYSQL_LIBRARIES}")
else()
    message(WARNING "MySQL not found. Database functionality will be limited.")
    message(STATUS "To install MySQL development libraries:")
    message(STATUS "  Ubuntu/Debian: sudo apt-get install libmysqlclient-dev")
    message(STATUS "  CentOS/RHEL: sudo yum install mysql-devel")
endif()

# 设置编译选项
target_compile_options(database_lib PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -Wpedantic>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -Wpedantic>
    $<$<CXX_COMPILER_ID:MSVC>:/W4>
)