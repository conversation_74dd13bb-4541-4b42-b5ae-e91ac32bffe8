cmake_minimum_required(VERSION 3.10)

# 查找librdkafka库（如果系统中已安装）
find_library(RDKAFKA_LIBRARY NAMES rdkafka)
find_library(RDKAFKACPP_LIBRARY NAMES rdkafka++)

if(NOT RDKAFKA_LIBRARY OR NOT RDKAFKACPP_LIBRARY)
    message(WARNING "librdkafka libraries not found, will use system default location")
endif()

# Kafka消息队列库
add_library(messaging_lib
        kafka_producer.cpp
        kafka_consumer.cpp
)

# 添加头文件搜索路径
target_include_directories(messaging_lib PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

# 链接依赖库
target_link_libraries(messaging_lib PUBLIC
        common_logger_lib
        ${RDKAFKA_LIBRARY}
        ${RDKAFKACPP_LIBRARY}
)