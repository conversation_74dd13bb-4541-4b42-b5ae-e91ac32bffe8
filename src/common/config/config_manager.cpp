//
// Created by 29108 on 2025/7/6.
//

#include "common/config/config_manager.h"
#include "common/logger/logger.h"
#include <fstream>
#include <algorithm>
#include <cstdlib>
#include <set>
#include <cctype>

namespace common {
    namespace config {
        ConfigManager & ConfigManager::getInstance() {
            static ConfigManager instance;
            return instance;
        }

        bool ConfigManager::loadFromFile(const std::string &file_path) {
            std::string extension = std::filesystem::path(file_path).extension().string();
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

            if (extension == ".yaml" || extension == ".yml") {
                return loadFromYaml(file_path);
            } else if (extension == ".json") {
                return loadFromJson(file_path);
            } else {
                LOG_ERROR("Unsupported config file format: " );
                return false;
            }
        }

        bool ConfigManager::loadFromYaml(const std::string &file_path) {
            try {
                if (!std::filesystem::exists(file_path)) {
                    LOG_ERROR("Config file not found: " + file_path);
                    return false;
                }

                YAML::Node config = YAML::LoadFile(file_path);

                std::unique_lock<std::shared_mutex> lock(config_mutex_);
                loadYamlNode(config, "");


                return true;

            } catch (const YAML::Exception& e) {
                LOG_ERROR("Failed to load YAML config: " + std::string(e.what()));
                return false;
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to load config file: " + std::string(e.what()));
                return false;
            }
        }

        bool ConfigManager::loadFromJson(const std::string &file_path) {
            try {
                if (!std::filesystem::exists(file_path)) {
                    LOG_ERROR("Config file not found: " + file_path);
                    return false;
                }

                std::ifstream file(file_path);
                if (!file.is_open()) {
                    LOG_ERROR("Cannot open config file: " + file_path);
                    return false;
                }

                nlohmann::json config;
                file >> config;

                std::unique_lock<std::shared_mutex> lock(config_mutex_);
                loadJsonNode(config, "");


                return true;

            } catch (const nlohmann::json::exception& e) {
                LOG_ERROR("Failed to load JSON config: " + std::string(e.what()));
                return false;
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to load config file: " + std::string(e.what()));
                return false;
            }
        }

        bool ConfigManager::loadFromEnv() {
            try {
                std::unique_lock<std::shared_mutex> lock(config_mutex_);

                // 加载所有环境变量，转换为配置格式
                // 使用 getenv 和已知的环境变量列表，而不是直接访问 environ
                std::vector<std::string> env_keys = {
                    "PATH", "HOME", "USER", "SHELL", "LANG", "LC_ALL",
                    "SYSTEM_VERSION", "SYSTEM_ENVIRONMENT", "SYSTEM_DEBUG_MODE",
                    "MYSQL_HOST", "MYSQL_PORT", "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DATABASE",
                    "REDIS_HOST", "REDIS_PORT", "REDIS_PASSWORD", "REDIS_DATABASE",
                    "KAFKA_BROKERS", "KAFKA_PRODUCER_BROKERS", "KAFKA_CONSUMER_BROKERS",
                    "KAFKA_GROUP_ID", "KAFKA_CLIENT_ID",
                    "LOG_LEVEL", "LOG_FILE_PATH", "LOG_CONSOLE_ENABLED", "LOG_FILE_ENABLED",
                    "SERVER_HOST", "SERVER_PORT", "THREAD_POOL_SIZE",
                    "NETWORK_BIND_ADDRESS", "NETWORK_LISTEN_PORT"
                };

                for (const auto& env_key : env_keys) {
                    const char* env_value = std::getenv(env_key.c_str());
                    if (env_value != nullptr) {
                        std::string value(env_value);

                        // 将环境变量名转换为配置键格式
                        // 例如: MYSQL_HOST -> mysql_host
                        std::string config_key = envKeyToConfigKey(env_key);
                        if (!config_key.empty()) {
                            configs_[config_key] = value;
                        }
                    }
                }


                return true;

            } catch (const std::exception& e) {
                LOG_ERROR("Failed to load config from environment: " + std::string(e.what()));
                return false;
            }
        }

        bool ConfigManager::has(const std::string &key) const {
            std::shared_lock<std::shared_mutex> lock(config_mutex_);
            return configs_.find(key) != configs_.end();
        }

        void ConfigManager::remove(const std::string &key) {
            std::string old_value;

            {
                std::unique_lock<std::shared_mutex> lock(config_mutex_);
                auto it = configs_.find(key);
                if (it != configs_.end()) {
                    old_value = it->second;
                    configs_.erase(it);
                } else {
                    return; // 键不存在，无需通知
                }
            }

            notifyConfigChange(key, old_value, "");
        }

        void ConfigManager::clear() {
            std::map<std::string, std::string> old_configs;

            {
                std::unique_lock<std::shared_mutex> lock(config_mutex_);
                old_configs = configs_;
                configs_.clear();
            }

            // 通知所有配置被清除
            for (const auto& [key, old_value] : old_configs) {
                notifyConfigChange(key, old_value, "");
            }
        }

        std::map<std::string, std::string> ConfigManager::getAllConfigs() const {
            std::shared_lock<std::shared_mutex> lock(config_mutex_);
            return configs_;
        }

        std::map<std::string, std::string> ConfigManager::getConfigsByPrefix(const std::string &prefix) const {
            std::shared_lock<std::shared_mutex> lock(config_mutex_);
            std::map<std::string, std::string> result;

            for (const auto& [key, value] : configs_) {
                if (key.substr(0, prefix.length()) == prefix) {
                    result[key] = value;
                }
            }

            return result;
        }

        void ConfigManager::addChangeListener(const std::string& key, ChangeListener listener) {
            // 参数验证
            if (!listener) {
                throw std::invalid_argument("ChangeListener cannot be null");
            }

            if (key.empty()) {
                throw std::invalid_argument("Configuration key cannot be empty");
            }

            // 优化：先尝试共享锁查找，减少独占锁的使用时间
            {
                std::shared_lock<std::shared_mutex> shared_lock(listeners_mutex_);
                auto it = listeners_.find(key);
                if (it != listeners_.end()) {
                    // key已存在，升级为独占锁进行修改
                    shared_lock.unlock();
                    std::unique_lock<std::shared_mutex> unique_lock(listeners_mutex_);

                    // 重新查找，因为在锁切换期间可能有变化
                    auto it_unique = listeners_.find(key);
                    if (it_unique != listeners_.end()) {
                        try {
                            it_unique->second.push_back(std::move(listener));
                            LOG_DEBUG("Added change listener for key: " + key);
                            return;
                        } catch (const std::exception& e) {
                            LOG_ERROR("Failed to add change listener for key '" + key + "': " + e.what());
                            throw;
                        }
                    }
                    // 如果key在锁切换期间被删除，继续执行下面的插入逻辑
                }
            }

            // key不存在，需要创建新的vector
            std::unique_lock<std::shared_mutex> unique_lock(listeners_mutex_);
            try {
                // 使用emplace避免不必要的拷贝，并确保异常安全
                auto& listener_vector = listeners_[key];
                listener_vector.push_back(std::move(listener));

                LOG_DEBUG("Added change listener for new key: " + key);
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to add change listener for key '" + key + "': " + e.what());
                throw;
            }
        }

        void ConfigManager::removeChangeListeners(const std::string &key) {
            if (key.empty()) {
                LOG_WARNING("Attempted to remove listeners for empty key");
                return;
            }

            std::unique_lock<std::shared_mutex> lock(listeners_mutex_);

            auto it = listeners_.find(key);
            if (it != listeners_.end()) {
                size_t listener_count = it->second.size();
                listeners_.erase(it);

                LOG_DEBUG("Removed " + std::to_string(listener_count) +
                         " change listeners for key: " + key);
            } else {
                LOG_DEBUG("No listeners found for key: " + key);
            }
        }

        bool ConfigManager::validate() const {
            std::shared_lock<std::shared_mutex> lock(config_mutex_);

            try {
                // 验证所有配置键的格式
                for (const auto& [key, value] : configs_) {
                    if (!isValidConfigKey(key)) {
                        LOG_ERROR("Invalid configuration key format: '" + key + "'");
                        return false;
                    }

                    // 验证配置值不为空（除非明确允许）
                    if (value.empty() && !isEmptyValueAllowed(key)) {
                        LOG_ERROR("Configuration value cannot be empty for key: '" + key + "'");
                        return false;
                    }
                }

                // 验证必需的配置项是否存在
                std::vector<std::string> required_keys = getRequiredConfigKeys();
                for (const auto& required_key : required_keys) {
                    if (configs_.find(required_key) == configs_.end()) {
                        LOG_ERROR("Required configuration key missing: '" + required_key + "'");
                        return false;
                    }
                }

                // 验证配置值的类型和范围
                if (!validateConfigValues()) {
                    return false;
                }

                LOG_DEBUG("Configuration validation passed");
                return true;

            } catch (const std::exception& e) {
                LOG_ERROR("Configuration validation failed with exception: " + std::string(e.what()));
                return false;
            }
        }

        std::vector<std::string> ConfigManager::getValidationErrors() const {
            std::vector<std::string> errors;
            std::shared_lock<std::shared_mutex> lock(config_mutex_);

            try {
                // 检查配置键格式
                for (const auto& [key, value] : configs_) {
                    if (!isValidConfigKey(key)) {
                        errors.push_back("Invalid configuration key format: '" + key + "'");
                    }

                    // 检查空值
                    if (value.empty() && !isEmptyValueAllowed(key)) {
                        errors.push_back("Configuration value cannot be empty for key: '" + key + "'");
                    }
                }

                // 检查必需配置项
                std::vector<std::string> required_keys = getRequiredConfigKeys();
                for (const auto& required_key : required_keys) {
                    if (configs_.find(required_key) == configs_.end()) {
                        errors.push_back("Required configuration key missing: '" + required_key + "'");
                    }
                }

                // 检查配置值的类型和范围
                auto value_errors = getConfigValueValidationErrors();
                errors.insert(errors.end(), value_errors.begin(), value_errors.end());

            } catch (const std::exception& e) {
                errors.push_back("Validation error: " + std::string(e.what()));
            }

            return errors;
        }

        bool ConfigManager::saveToFile(const std::string &file_path) const {
            if (file_path.empty()) {
                LOG_ERROR("File path cannot be empty");
                return false;
            }

            // 根据文件扩展名选择保存格式
            std::string extension = std::filesystem::path(file_path).extension().string();
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

            if (extension == ".yaml" || extension == ".yml") {
                return saveToYaml(file_path);
            } else if (extension == ".json") {
                return saveToJson(file_path);
            } else {
                LOG_ERROR("Unsupported file format: " + extension + ". Supported formats: .yaml, .yml, .json");
                return false;
            }
        }

        bool ConfigManager::saveToYaml(const std::string &file_path) const {
            try {
                // 创建目录（如果不存在）
                std::filesystem::path path(file_path);
                if (path.has_parent_path()) {
                    std::filesystem::create_directories(path.parent_path());
                }

                // 构建YAML结构
                YAML::Node root;

                {
                    std::shared_lock<std::shared_mutex> lock(config_mutex_);

                    for (const auto& [key, value] : configs_) {
                        setYamlValue(root, key, value);
                    }
                }

                // 写入文件
                std::ofstream file(file_path);
                if (!file.is_open()) {
                    LOG_ERROR("Cannot open file for writing: " + file_path);
                    return false;
                }

                file << root;
                file.close();


                return true;

            } catch (const YAML::Exception& e) {
                LOG_ERROR("Failed to save YAML config: " + std::string(e.what()));
                return false;
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to save config file: " + std::string(e.what()));
                return false;
            }
        }

        bool ConfigManager::saveToJson(const std::string &file_path) const {
            try {
                // 创建目录（如果不存在）
                std::filesystem::path path(file_path);
                if (path.has_parent_path()) {
                    std::filesystem::create_directories(path.parent_path());
                }

                // 构建JSON结构
                nlohmann::json root;

                {
                    std::shared_lock<std::shared_mutex> lock(config_mutex_);

                    for (const auto& [key, value] : configs_) {
                        setJsonValue(root, key, value);
                    }
                }

                // 写入文件
                std::ofstream file(file_path);
                if (!file.is_open()) {
                    LOG_ERROR("Cannot open file for writing: " + file_path);
                    return false;
                }

                file << root.dump(4);  // 4空格缩进，美化输出
                file.close();


                return true;

            } catch (const nlohmann::json::exception& e) {
                LOG_ERROR("Failed to save JSON config: " + std::string(e.what()));
                return false;
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to save config file: " + std::string(e.what()));
                return false;
            }
        }

        void ConfigManager::enableHotReload(const std::string &file_path, std::chrono::seconds check_interval) {
            disableHotReload(); // 先停止之前的热重载

            hot_reload_enabled_ = true;
            hot_reload_file_path_ = file_path;
            hot_reload_check_interval_ = check_interval;

            // 记录文件的最后修改时间
            if (std::filesystem::exists(file_path)) {
                last_file_write_time_ = std::filesystem::last_write_time(file_path);
            }

            // 启动热重载线程
            hot_reload_thread_ = std::thread(&ConfigManager::hotReloadWorker, this);


        }

        void ConfigManager::disableHotReload() {
            hot_reload_enabled_ = false;

            if (hot_reload_thread_.joinable()) {
                hot_reload_thread_.join();
            }


        }

        ConfigManager::ConfigManager()
            : hot_reload_enabled_(false),
              last_file_write_time_(std::filesystem::file_time_type{}),
              hot_reload_check_interval_(std::chrono::seconds(5)) {

            // 避免在构造函数中使用LOG宏，防止循环依赖
            // LOG_INFO("ConfigManager initialized" );

            // 初始化默认配置值
            try {
                // 可以在这里设置一些默认的系统配置
                configs_["system.version"] = "1.0.0";
                configs_["system.startup_time"] = std::to_string(
                    std::chrono::duration_cast<std::chrono::seconds>(
                        std::chrono::system_clock::now().time_since_epoch()
                    ).count()
                );

                // 使用标准输出代替LOG宏，避免循环依赖
                // LOG_DEBUG("Default system configurations initialized" );
            } catch (const std::exception& e) {
                // 使用标准错误输出代替LOG宏，避免循环依赖
                std::cerr << "ConfigManager: Failed to initialize default configurations: " << e.what() << std::endl;
            }
        }

        ConfigManager::~ConfigManager() {
            // 避免在析构函数中使用LOG宏，防止循环依赖
            // LOG_INFO("ConfigManager shutting down..." );

            try {
                // 1. 首先禁用热重载，停止监控线程
                if (hot_reload_enabled_.load()) {
                    // LOG_DEBUG("Disabling hot reload during shutdown" );
                    disableHotReload();
                }

                // 2. 清除所有配置变更监听器
                {
                    std::unique_lock<std::shared_mutex> lock(listeners_mutex_);
                    if (!listeners_.empty()) {
                        size_t total_listeners = 0;
                        for (const auto& pair : listeners_) {
                            total_listeners += pair.second.size();
                        }
                        // LOG_DEBUG("Clearing " + std::to_string(total_listeners) +
                        //          " change listeners for " + std::to_string(listeners_.size()) + " keys",
                        //          __FILE__, __LINE__);
                        listeners_.clear();
                    }
                }

                // 3. 清空所有配置数据
                {
                    std::unique_lock<std::shared_mutex> lock(config_mutex_);
                    if (!configs_.empty()) {
                        // LOG_DEBUG("Clearing " + std::to_string(configs_.size()) + " configuration entries" );
                        configs_.clear();
                    }
                }

                // 4. 重置热重载相关状态
                hot_reload_file_path_.clear();
                last_file_write_time_ = std::filesystem::file_time_type{};
                hot_reload_check_interval_ = std::chrono::seconds(5);

                // LOG_INFO("ConfigManager shutdown completed successfully" );

            } catch (const std::exception& e) {
                // 析构函数中不应该抛出异常，只记录错误到标准错误输出
                std::cerr << "ConfigManager: Error during shutdown: " << e.what() << std::endl;
            } catch (...) {
                // 捕获所有其他异常
                std::cerr << "ConfigManager: Unknown error during shutdown" << std::endl;
            }
        }

        void ConfigManager::notifyConfigChange(const std::string &key, const std::string &old_value,
            const std::string &new_value) {
            // 复制监听器列表，避免在回调执行期间持有锁
            std::vector<ChangeListener> listeners_to_notify;

            {
                std::shared_lock<std::shared_mutex> lock(listeners_mutex_);
                auto it = listeners_.find(key);
                if (it != listeners_.end() && !it->second.empty()) {
                    // 复制监听器列表，避免在回调期间持有锁
                    listeners_to_notify = it->second;
                }
            }

            // 在锁外执行回调，避免死锁和提高并发性能
            if (!listeners_to_notify.empty()) {
                LOG_DEBUG("Notifying " + std::to_string(listeners_to_notify.size()) +
                         " listeners for config change: " + key);

                for (const auto& listener : listeners_to_notify) {
                    try {
                        // 检查listener是否仍然有效
                        if (listener) {
                            listener(key, old_value, new_value);
                        }
                    } catch (const std::exception& e) {
                        LOG_ERROR("Config change listener failed for key '" + key + "': " +
                                 e.what());
                        // 继续执行其他监听器，不因一个失败而中断
                    } catch (...) {
                        LOG_ERROR("Config change listener failed for key '" + key +
                                 "' with unknown exception");
                    }
                }
            }
        }

        void ConfigManager::hotReloadWorker() {


            while (hot_reload_enabled_.load()) {
                try {
                    // 检查文件是否存在，使用error_code避免异常
                    std::error_code ec;
                    bool file_exists = std::filesystem::exists(hot_reload_file_path_, ec);

                    if (ec) {
                        LOG_WARNING("Error checking file existence: " + ec.message());
                        std::this_thread::sleep_for(hot_reload_check_interval_);
                        continue;
                    }

                    if (!file_exists) {
                        LOG_WARNING("Config file no longer exists: " + hot_reload_file_path_);
                        std::this_thread::sleep_for(hot_reload_check_interval_);
                        continue;
                    }

                    // 获取文件修改时间
                    auto current_write_time = std::filesystem::last_write_time(hot_reload_file_path_, ec);
                    if (ec) {
                        LOG_WARNING("Error getting file modification time: " + ec.message());
                        std::this_thread::sleep_for(hot_reload_check_interval_);
                        continue;
                    }

                    // 检查文件是否有变化
                    if (current_write_time != last_file_write_time_) {


                        // 保存旧配置用于比较
                        std::map<std::string, std::string> old_configs;
                        {
                            std::shared_lock<std::shared_mutex> lock(config_mutex_);
                            old_configs = configs_;
                        }

                        // 重新加载配置
                        bool reload_success = false;
                        try {
                            reload_success = loadFromFile(hot_reload_file_path_);
                        } catch (const std::exception& e) {
                            LOG_ERROR("Failed to reload config file: " + std::string(e.what()));
                            // 继续使用旧配置，不更新时间戳
                            std::this_thread::sleep_for(hot_reload_check_interval_);
                            continue;
                        }

                        if (reload_success) {
                            last_file_write_time_ = current_write_time;


                            // 比较配置变化并通知监听器（在锁外执行通知）
                            std::map<std::string, std::string> current_configs;
                            {
                                std::shared_lock<std::shared_mutex> lock(config_mutex_);
                                current_configs = configs_;
                            }

                            // 检查新增和修改的配置
                            for (const auto& [key, new_value] : current_configs) {
                                auto old_it = old_configs.find(key);
                                if (old_it == old_configs.end()) {
                                    // 新增的配置
                                    notifyConfigChange(key, "", new_value);
                                } else if (old_it->second != new_value) {
                                    // 修改的配置
                                    notifyConfigChange(key, old_it->second, new_value);
                                }
                            }

                            // 检查删除的配置
                            for (const auto& [key, old_value] : old_configs) {
                                if (current_configs.find(key) == current_configs.end()) {
                                    notifyConfigChange(key, old_value, "");
                                }
                            }
                        } else {
                            LOG_ERROR("Failed to reload config file, keeping old configuration");
                        }
                    }

                } catch (const std::filesystem::filesystem_error& e) {
                    LOG_ERROR("Filesystem error in hot reload worker: " + std::string(e.what()));
                } catch (const std::exception& e) {
                    LOG_ERROR("Hot reload worker error: " + std::string(e.what()));
                } catch (...) {
                    LOG_ERROR("Unknown error in hot reload worker");
                }

                // 等待下次检查
                std::this_thread::sleep_for(hot_reload_check_interval_);
            }


        }

        std::filesystem::file_time_type ConfigManager::getFileModificationTime(const std::string &file_path) {
            std::error_code ec;
            auto ftime = std::filesystem::last_write_time(file_path, ec);
            if (ec) {
                LOG_WARNING("Error getting file modification time for " + file_path + ": " + ec.message());
                return std::filesystem::file_time_type{};
            }
            return ftime;
        }

        void ConfigManager::loadYamlNode(const YAML::Node &node, const std::string &prefix) {
            if (node.IsScalar()) {
                configs_[prefix] = node.as<std::string>();
            } else if (node.IsMap()) {
                for (const auto& item : node) {
                    std::string key = item.first.as<std::string>();
                    std::string full_key = prefix.empty() ? key : prefix + "." + key;
                    loadYamlNode(item.second, full_key);
                }
            } else if (node.IsSequence()) {
                // 处理数组类型
                std::ostringstream oss;
                for (size_t i = 0; i < node.size(); ++i) {
                    if (i > 0) oss << ",";
                    oss << node[i].as<std::string>();
                }
                configs_[prefix] = oss.str();
            }
        }

        void ConfigManager::loadJsonNode(const nlohmann::json &node, const std::string &prefix) {
            if (node.is_string()) {
                configs_[prefix] = node.get<std::string>();
            } else if (node.is_number()) {
                configs_[prefix] = std::to_string(node.get<double>());
            } else if (node.is_boolean()) {
                configs_[prefix] = node.get<bool>() ? "true" : "false";
            } else if (node.is_object()) {
                for (const auto& [key, value] : node.items()) {
                    std::string full_key = prefix.empty() ? key : prefix + "." + key;
                    loadJsonNode(value, full_key);
                }
            } else if (node.is_array()) {
                std::ostringstream oss;
                for (size_t i = 0; i < node.size(); ++i) {
                    if (i > 0) oss << ",";
                    if (node[i].is_string()) {
                        oss << node[i].get<std::string>();
                    } else {
                        oss << node[i].dump();
                    }
                }
                configs_[prefix] = oss.str();
            }
        }

        std::string ConfigManager::envKeyToConfigKey(const std::string &env_key) const {
            // 将环境变量名转换为配置键
            // 例如: MYSQL_HOST -> database.mysql.host
            //      REDIS_PORT -> database.redis.port
            //      HTTP_SERVER_PORT -> server.http.port

            std::string key = env_key;
            std::transform(key.begin(), key.end(), key.begin(), ::tolower);

            // 定义环境变量到配置键的映射规则
            static const std::map<std::string, std::string> env_mappings = {
                // 系统相关
                {"system_version", "system_version"},
                {"system_environment", "system_environment"},
                {"system_debug_mode", "system_debug_mode"},

                // 数据库相关
                {"mysql_host", "mysql_host"},
                {"mysql_port", "mysql_port"},
                {"mysql_user", "mysql_user"},
                {"mysql_password", "mysql_password"},
                {"mysql_database", "mysql_database"},
                {"redis_host", "redis_host"},
                {"redis_port", "redis_port"},
                {"redis_password", "redis_password"},
                {"redis_database", "redis_database"},

                // 服务器相关
                {"server_host", "server_host"},
                {"server_port", "server_port"},
                {"http_server_port", "server.http.port"},
                {"http_server_host", "server.http.bind_address"},
                {"tcp_server_port", "server.tcp.port"},
                {"websocket_port", "server.websocket.port"},

                // 认证相关
                {"jwt_secret_key", "auth.jwt.secret_key"},
                {"jwt_issuer", "auth.jwt.issuer"},

                // 日志相关
                {"log_level", "log_level"},
                {"log_file_path", "log_file_path"},
                {"log_console_enabled", "log_console_enabled"},
                {"log_file_enabled", "log_file_enabled"},

                // Kafka相关
                {"kafka_brokers", "kafka_producer_brokers"},
                {"kafka_producer_brokers", "kafka_producer_brokers"},
                {"kafka_consumer_brokers", "kafka_consumer_brokers"},
                {"kafka_group_id", "kafka_consumer_group_id"},
                {"kafka_client_id", "kafka_producer_client_id"},

                // 网络相关
                {"network_bind_address", "network_bind_address"},
                {"network_listen_port", "network_listen_port"},

                // 线程池相关
                {"thread_pool_size", "thread_pool_size"},
                {"thread_pool_core_size", "thread_pool_core_size"},
                {"thread_pool_max_size", "thread_pool_max_size"}
            };

            auto it = env_mappings.find(key);
            return it != env_mappings.end() ? it->second : "";
        }

        // 配置重新加载实现
        bool ConfigManager::reload() {
            if (hot_reload_file_path_.empty()) {
                LOG_WARNING("No file path set for reload operation");
                return false;
            }



            // 保存旧配置用于比较
            std::map<std::string, std::string> old_configs;
            {
                std::shared_lock<std::shared_mutex> lock(config_mutex_);
                old_configs = configs_;
            }

            // 重新加载配置
            bool reload_success = false;
            try {
                reload_success = loadFromFile(hot_reload_file_path_);
            } catch (const std::exception& e) {
                LOG_ERROR("Failed to reload config file: " + std::string(e.what()));
                return false;
            }

            if (!reload_success) {
                LOG_ERROR("Failed to reload configuration file");
                return false;
            }

            // 更新文件修改时间
            try {
                std::error_code ec;
                last_file_write_time_ = std::filesystem::last_write_time(hot_reload_file_path_, ec);
                if (ec) {
                    LOG_WARNING("Could not update file modification time: " + ec.message());
                }
            } catch (const std::exception& e) {
                LOG_WARNING("Exception updating file modification time: " + std::string(e.what()));
            }

            // 比较配置变化并通知监听器
            std::map<std::string, std::string> current_configs;
            {
                std::shared_lock<std::shared_mutex> lock(config_mutex_);
                current_configs = configs_;
            }

            // 检查新增和修改的配置
            for (const auto& [key, new_value] : current_configs) {
                auto old_it = old_configs.find(key);
                if (old_it == old_configs.end()) {
                    // 新增的配置
                    notifyConfigChange(key, "", new_value);
                } else if (old_it->second != new_value) {
                    // 修改的配置
                    notifyConfigChange(key, old_it->second, new_value);
                }
            }

            // 检查删除的配置
            for (const auto& [key, old_value] : old_configs) {
                if (current_configs.find(key) == current_configs.end()) {
                    notifyConfigChange(key, old_value, "");
                }
            }


            return true;
        }

        // 配置重置实现
        void ConfigManager::reset() {


            // 保存旧配置用于通知
            std::map<std::string, std::string> old_configs;
            {
                std::unique_lock<std::shared_mutex> lock(config_mutex_);
                old_configs = configs_;
                configs_.clear();

                // 恢复默认配置
                configs_["system.version"] = "1.0.0";
                configs_["system.startup_time"] = std::to_string(
                    std::chrono::duration_cast<std::chrono::seconds>(
                        std::chrono::system_clock::now().time_since_epoch()
                    ).count()
                );
            }

            // 停止热重载
            if (hot_reload_enabled_.load()) {
                disableHotReload();
            }

            // 清除所有监听器
            {
                std::unique_lock<std::shared_mutex> lock(listeners_mutex_);
                listeners_.clear();
            }

            // 重置热重载相关状态
            hot_reload_file_path_.clear();
            last_file_write_time_ = std::filesystem::file_time_type{};
            hot_reload_check_interval_ = std::chrono::seconds{5};

            // 通知所有配置被清除
            for (const auto& [key, old_value] : old_configs) {
                // 注意：这里不能调用notifyConfigChange，因为监听器已被清除
                LOG_DEBUG("Configuration key removed during reset: " + key);
            }


        }

        bool ConfigManager::isValidConfigKey(const std::string &key) const {
            if (key.empty()) {
                return false;
            }

            // 检查是否包含非法字符
            for (char c : key) {
                if (!std::isalnum(c) && c != '.' && c != '_' && c != '-') {
                    return false;
                }
            }

            // 检查是否以点开头或结尾
            if (key.front() == '.' || key.back() == '.') {
                return false;
            }

            // 检查是否有连续的点
            if (key.find("..") != std::string::npos) {
                return false;
            }

            return true;
        }

        std::string ConfigManager::sanitizeConfigValue(const std::string &value) const {
            std::string result = value;

            // 去除前后空白字符
            result.erase(0, result.find_first_not_of(" \t\r\n"));
            result.erase(result.find_last_not_of(" \t\r\n") + 1);

            // 处理引号包围的值
            if (result.length() >= 2) {
                if ((result.front() == '"' && result.back() == '"') ||
                    (result.front() == '\'' && result.back() == '\'')) {
                    result = result.substr(1, result.length() - 2);
                    }
            }

            return result;
        }

        bool ConfigManager::isEmptyValueAllowed(const std::string &key) const {
            // 定义允许为空值的配置键列表
            static const std::set<std::string> empty_allowed_keys = {
                "mysql_password",           // 数据库密码可以为空（无密码连接）
                "redis_password",           // Redis密码可以为空
                "ssl_cert_file",           // SSL证书文件路径可以为空（不使用SSL）
                "ssl_key_file",            // SSL私钥文件路径可以为空
                "kafka_group_id",          // Kafka组ID可以为空（自动生成）
                "kafka_client_id",         // Kafka客户端ID可以为空
                "system.description",      // 系统描述可以为空
                "system.environment",      // 环境标识可以为空
                "log.custom_format",       // 自定义日志格式可以为空
                "server.ssl_cert_file",    // 服务器SSL证书可以为空
                "server.ssl_key_file"      // 服务器SSL私钥可以为空
            };

            // 检查键是否在允许为空的列表中
            bool allowed = empty_allowed_keys.find(key) != empty_allowed_keys.end();

            // 检查是否是可选配置的模式匹配
            if (!allowed) {
                // 以 "optional." 开头的配置都允许为空
                if (key.find("optional.") == 0) {
                    allowed = true;
                }
                // 以 ".description" 结尾的配置允许为空
                else if (key.length() > 12 && key.substr(key.length() - 12) == ".description") {
                    allowed = true;
                }
                // 以 ".comment" 结尾的配置允许为空
                else if (key.length() > 8 && key.substr(key.length() - 8) == ".comment") {
                    allowed = true;
                }
            }

            LOG_DEBUG("Empty value check for key '" + key + "': " + (allowed ? "allowed" : "not allowed"));
            return allowed;
        }

        std::vector<std::string> ConfigManager::getRequiredConfigKeys() const {
            // 定义必需的配置键列表
            static const std::vector<std::string> required_keys = {
                // 数据库相关必需配置
                "mysql_host",
                "mysql_port",
                "mysql_user",
                "mysql_database",
                "mysql_pool_size",
                "mysql_max_pool_size",

                // Redis相关必需配置
                "redis_host",
                "redis_port",
                "redis_database",
                "redis_pool_size",
                "redis_max_pool_size",

                // 服务器相关必需配置
                "server_host",
                "server_port",
                "thread_pool_size",
                "max_connections",
                "keep_alive_timeout",

                // Kafka相关必需配置
                "kafka_brokers",
                "session_timeout_ms",
                "max_poll_interval_ms",
                "offset_reset",
                "enable_auto_commit",

                // 日志相关必需配置
                "log_level",
                "enable_console",
                "enable_file",
                "log_file_path",
                "max_file_size",
                "max_files",
                "enable_async",

                // 系统相关必需配置
                "system.version"
            };

            LOG_DEBUG("Retrieved " + std::to_string(required_keys.size()) + " required configuration keys");
            return required_keys;
        }

        bool ConfigManager::validateConfigValues() const {
            std::shared_lock<std::shared_mutex> lock(config_mutex_);

            try {
                auto required_keys = getRequiredConfigKeys();
                bool all_valid = true;

                // 1. 检查所有必需的配置键是否存在
                for (const auto& key : required_keys) {
                    auto it = configs_.find(key);
                    if (it == configs_.end()) {
                        LOG_ERROR("Required configuration key missing: " + key);
                        all_valid = false;
                        continue;
                    }

                    // 2. 检查值是否为空（对于不允许为空的键）
                    if (it->second.empty() && !isEmptyValueAllowed(key)) {
                        LOG_ERROR("Required configuration key has empty value: " + key);
                        all_valid = false;
                        continue;
                    }
                }

                // 3. 验证特定配置的值格式
                all_valid &= validateSpecificConfigFormats();

                if (all_valid) {
                    LOG_DEBUG("All configuration values validation passed");
                } else {
                    LOG_WARNING("Configuration validation failed - see error logs for details");
                }

                return all_valid;

            } catch (const std::exception& e) {
                LOG_ERROR("Exception during configuration validation: " + std::string(e.what()));
                return false;
            }
        }

        std::vector<std::string> ConfigManager::getConfigValueValidationErrors() const {
            std::shared_lock<std::shared_mutex> lock(config_mutex_);
            std::vector<std::string> errors;

            try {
                auto required_keys = getRequiredConfigKeys();

                // 1. 检查缺失的必需配置
                for (const auto& key : required_keys) {
                    auto it = configs_.find(key);
                    if (it == configs_.end()) {
                        errors.push_back("Missing required configuration: " + key);
                        continue;
                    }

                    // 2. 检查空值
                    if (it->second.empty() && !isEmptyValueAllowed(key)) {
                        errors.push_back("Empty value not allowed for: " + key);
                        continue;
                    }
                }

                // 3. 验证数值类型配置
                validateNumericConfigs(errors);

                // 4. 验证布尔类型配置
                validateBooleanConfigs(errors);

                // 5. 验证文件路径配置
                validateFilePathConfigs(errors);

                // 6. 验证网络配置
                validateNetworkConfigs(errors);

                LOG_DEBUG("Configuration validation found " + std::to_string(errors.size()) + " errors");

            } catch (const std::exception& e) {
                errors.push_back("Exception during validation: " + std::string(e.what()));
                LOG_ERROR("Exception in getConfigValueValidationErrors: " + std::string(e.what()));
            }

            return errors;
        }

        void ConfigManager::setYamlValue(YAML::Node &node, const std::string &key, const std::string &value) const {
            try {
                // 为了保持与loadYamlNode的一致性，我们使用扁平结构
                // 直接使用完整的键名，不创建嵌套结构

                // 尝试智能类型转换
                const std::string& final_key = key;

                // 尝试转换为适当的类型
                if (value == "true" || value == "false") {
                    // 布尔值
                    node[final_key] = (value == "true");
                } else if (value.find_first_not_of("0123456789") == std::string::npos && !value.empty()) {
                    // 整数
                    try {
                        long long int_val = std::stoll(value);
                        node[final_key] = int_val;
                    } catch (...) {
                        node[final_key] = value; // 回退到字符串
                    }
                } else if (value.find_first_not_of("0123456789.") == std::string::npos &&
                          std::count(value.begin(), value.end(), '.') == 1 && !value.empty()) {
                    // 浮点数
                    try {
                        double double_val = std::stod(value);
                        node[final_key] = double_val;
                    } catch (...) {
                        node[final_key] = value; // 回退到字符串
                    }
                } else {
                    // 字符串
                    node[final_key] = value;
                }

                LOG_DEBUG("Set YAML value: " + key + " = " + value);

            } catch (const std::exception& e) {
                LOG_ERROR("Failed to set YAML value for key '" + key + "': " + std::string(e.what()));
                throw;
            }
        }

        void ConfigManager::setJsonValue(nlohmann::json &node, const std::string &key, const std::string &value) const {
            try {
                // 解析键路径（支持嵌套键，如 "database.mysql.host"）
                std::vector<std::string> key_parts;
                std::stringstream ss(key);
                std::string part;

                while (std::getline(ss, part, '.')) {
                    if (!part.empty()) {
                        key_parts.push_back(part);
                    }
                }

                if (key_parts.empty()) {
                    LOG_WARNING("Empty key provided for JSON value setting");
                    return;
                }

                // 导航到正确的节点位置
                nlohmann::json* current_node = &node;
                for (size_t i = 0; i < key_parts.size() - 1; ++i) {
                    if (!current_node->contains(key_parts[i]) || !(*current_node)[key_parts[i]].is_object()) {
                        (*current_node)[key_parts[i]] = nlohmann::json::object();
                    }
                    current_node = &((*current_node)[key_parts[i]]);
                }

                // 设置最终值，尝试智能类型转换
                const std::string& final_key = key_parts.back();

                // 尝试转换为适当的类型
                if (value == "true") {
                    (*current_node)[final_key] = true;
                } else if (value == "false") {
                    (*current_node)[final_key] = false;
                } else if (value == "null") {
                    (*current_node)[final_key] = nullptr;
                } else if (value.find_first_not_of("0123456789") == std::string::npos && !value.empty()) {
                    // 整数
                    try {
                        long long int_val = std::stoll(value);
                        (*current_node)[final_key] = int_val;
                    } catch (...) {
                        (*current_node)[final_key] = value; // 回退到字符串
                    }
                } else if (value.find_first_not_of("0123456789.") == std::string::npos &&
                          std::count(value.begin(), value.end(), '.') == 1 && !value.empty()) {
                    // 浮点数
                    try {
                        double double_val = std::stod(value);
                        (*current_node)[final_key] = double_val;
                    } catch (...) {
                        (*current_node)[final_key] = value; // 回退到字符串
                    }
                } else {
                    // 字符串
                    (*current_node)[final_key] = value;
                }

                LOG_DEBUG("Set JSON value: " + key + " = " + value);

            } catch (const std::exception& e) {
                LOG_ERROR("Failed to set JSON value for key '" + key + "': " + std::string(e.what()));
                throw;
            }
        }

        // ==================== 私有辅助方法实现 ====================

        bool ConfigManager::validateSpecificConfigFormats() const {
            bool all_valid = true;

            // 验证端口号范围
            auto validate_port = [&](const std::string& key) {
                auto it = configs_.find(key);
                if (it != configs_.end()) {
                    try {
                        int port = std::stoi(it->second);
                        if (port < 1 || port > 65535) {
                            LOG_ERROR("Invalid port number for " + key + ": " + it->second + " (must be 1-65535)");
                            return false;
                        }
                    } catch (...) {
                        LOG_ERROR("Invalid port format for " + key + ": " + it->second);
                        return false;
                    }
                }
                return true;
            };

            all_valid &= validate_port("mysql_port");
            all_valid &= validate_port("redis_port");
            all_valid &= validate_port("server_port");

            // 验证日志级别
            auto it = configs_.find("log_level");
            if (it != configs_.end()) {
                std::set<std::string> valid_levels = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};
                if (valid_levels.find(it->second) == valid_levels.end()) {
                    LOG_ERROR("Invalid log level: " + it->second);
                    all_valid = false;
                }
            }

            return all_valid;
        }

        void ConfigManager::validateNumericConfigs(std::vector<std::string>& errors) const {
            // 需要验证的数值配置
            std::map<std::string, std::pair<long long, long long>> numeric_ranges = {
                {"mysql_port", {1, 65535}},
                {"redis_port", {1, 65535}},
                {"server_port", {1, 65535}},
                {"mysql_pool_size", {1, 1000}},
                {"mysql_max_pool_size", {1, 1000}},
                {"redis_pool_size", {1, 1000}},
                {"redis_max_pool_size", {1, 1000}},
                {"thread_pool_size", {1, 1000}},
                {"max_connections", {1, 100000}},
                {"keep_alive_timeout", {1, 3600}},
                {"session_timeout_ms", {1000, 300000}},
                {"max_poll_interval_ms", {1000, 600000}},
                {"max_file_size", {1024, 1073741824}}, // 1KB to 1GB
                {"max_files", {1, 1000}}
            };

            for (const auto& [key, range] : numeric_ranges) {
                auto it = configs_.find(key);
                if (it != configs_.end()) {
                    try {
                        long long value = std::stoll(it->second);
                        if (value < range.first || value > range.second) {
                            errors.push_back("Value out of range for " + key + ": " + it->second +
                                           " (valid range: " + std::to_string(range.first) + "-" + std::to_string(range.second) + ")");
                        }
                    } catch (...) {
                        errors.push_back("Invalid numeric format for " + key + ": " + it->second);
                    }
                }
            }
        }

        void ConfigManager::validateBooleanConfigs(std::vector<std::string>& errors) const {
            // 需要验证的布尔配置
            std::vector<std::string> boolean_keys = {
                "enable_ssl", "enable_console", "enable_file", "enable_async", "enable_auto_commit"
            };

            for (const auto& key : boolean_keys) {
                auto it = configs_.find(key);
                if (it != configs_.end()) {
                    std::string lower_value = it->second;
                    std::transform(lower_value.begin(), lower_value.end(), lower_value.begin(), ::tolower);

                    if (lower_value != "true" && lower_value != "false" &&
                        lower_value != "1" && lower_value != "0" &&
                        lower_value != "yes" && lower_value != "no" &&
                        lower_value != "on" && lower_value != "off") {
                        errors.push_back("Invalid boolean value for " + key + ": " + it->second);
                    }
                }
            }
        }

        void ConfigManager::validateFilePathConfigs(std::vector<std::string>& errors) const {
            // 需要验证的文件路径配置
            std::vector<std::string> file_path_keys = {
                "ssl_cert_file", "ssl_key_file", "log_file_path"
            };

            for (const auto& key : file_path_keys) {
                auto it = configs_.find(key);
                if (it != configs_.end() && !it->second.empty()) {
                    // 检查路径是否包含非法字符
                    const std::string& path = it->second;
                    if (path.find('\0') != std::string::npos) {
                        errors.push_back("File path contains null character for " + key + ": " + path);
                    }

                    // 检查路径长度
                    if (path.length() > 4096) {
                        errors.push_back("File path too long for " + key + " (max 4096 characters)");
                    }
                }
            }
        }

        void ConfigManager::validateNetworkConfigs(std::vector<std::string>& errors) const {
            // 验证主机地址格式
            std::vector<std::string> host_keys = {"mysql_host", "redis_host", "server_host"};

            for (const auto& key : host_keys) {
                auto it = configs_.find(key);
                if (it != configs_.end() && !it->second.empty()) {
                    const std::string& host = it->second;

                    // 基本格式检查
                    if (host.length() > 253) {
                        errors.push_back("Host name too long for " + key + " (max 253 characters)");
                    }

                    // 检查是否包含非法字符
                    if (host.find(' ') != std::string::npos) {
                        errors.push_back("Host name contains spaces for " + key + ": " + host);
                    }
                }
            }

            // 验证Kafka brokers格式
            auto kafka_it = configs_.find("kafka_brokers");
            if (kafka_it != configs_.end() && !kafka_it->second.empty()) {
                const std::string& brokers = kafka_it->second;
                if (brokers.find(':') == std::string::npos) {
                    errors.push_back("Kafka brokers must include port (host:port format): " + brokers);
                }
            }
        }

        // ==================== 配置结构体实现 ====================

        DatabaseConfig DatabaseConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            DatabaseConfig config;

            // 基本连接配置
            config.mysql_host = config_manager.get<std::string>("mysql_host", config.mysql_host);
            config.mysql_port = config_manager.get<int>("mysql_port", config.mysql_port);
            config.mysql_user = config_manager.get<std::string>("mysql_user", config.mysql_user);
            config.mysql_password = config_manager.get<std::string>("mysql_password", config.mysql_password);
            config.mysql_database = config_manager.get<std::string>("mysql_database", config.mysql_database);
            config.mysql_charset = config_manager.get<std::string>("mysql_charset", config.mysql_charset);
            config.mysql_collation = config_manager.get<std::string>("mysql_collation", config.mysql_collation);

            // 连接池配置
            config.mysql_pool_size = config_manager.get<size_t>("mysql_pool_size", config.mysql_pool_size);
            config.mysql_max_pool_size = config_manager.get<size_t>("mysql_max_pool_size", config.mysql_max_pool_size);
            config.mysql_min_pool_size = config_manager.get<size_t>("mysql_min_pool_size", config.mysql_min_pool_size);
            config.mysql_pool_timeout_ms = config_manager.get<int>("mysql_pool_timeout_ms", config.mysql_pool_timeout_ms);
            config.mysql_pool_max_idle_time_ms = config_manager.get<int>("mysql_pool_max_idle_time_ms", config.mysql_pool_max_idle_time_ms);
            config.mysql_pool_test_on_borrow = config_manager.get<bool>("mysql_pool_test_on_borrow", config.mysql_pool_test_on_borrow);
            config.mysql_pool_test_on_return = config_manager.get<bool>("mysql_pool_test_on_return", config.mysql_pool_test_on_return);
            config.mysql_pool_test_while_idle = config_manager.get<bool>("mysql_pool_test_while_idle", config.mysql_pool_test_while_idle);
            config.mysql_pool_validation_interval_ms = config_manager.get<int>("mysql_pool_validation_interval_ms", config.mysql_pool_validation_interval_ms);

            // 连接参数
            config.mysql_connect_timeout_ms = config_manager.get<int>("mysql_connect_timeout_ms", config.mysql_connect_timeout_ms);
            config.mysql_read_timeout_ms = config_manager.get<int>("mysql_read_timeout_ms", config.mysql_read_timeout_ms);
            config.mysql_write_timeout_ms = config_manager.get<int>("mysql_write_timeout_ms", config.mysql_write_timeout_ms);
            config.mysql_auto_reconnect = config_manager.get<bool>("mysql_auto_reconnect", config.mysql_auto_reconnect);
            config.mysql_max_reconnect_attempts = config_manager.get<int>("mysql_max_reconnect_attempts", config.mysql_max_reconnect_attempts);
            config.mysql_reconnect_interval_ms = config_manager.get<int>("mysql_reconnect_interval_ms", config.mysql_reconnect_interval_ms);

            // SSL配置
            config.mysql_use_ssl = config_manager.get<bool>("mysql_use_ssl", config.mysql_use_ssl);
            config.mysql_ssl_ca = config_manager.get<std::string>("mysql_ssl_ca", config.mysql_ssl_ca);
            config.mysql_ssl_cert = config_manager.get<std::string>("mysql_ssl_cert", config.mysql_ssl_cert);
            config.mysql_ssl_key = config_manager.get<std::string>("mysql_ssl_key", config.mysql_ssl_key);
            config.mysql_ssl_cipher = config_manager.get<std::string>("mysql_ssl_cipher", config.mysql_ssl_cipher);
            config.mysql_ssl_verify_server_cert = config_manager.get<bool>("mysql_ssl_verify_server_cert", config.mysql_ssl_verify_server_cert);

            // 性能配置
            config.mysql_max_allowed_packet = config_manager.get<int>("mysql_max_allowed_packet", config.mysql_max_allowed_packet);
            config.mysql_net_buffer_length = config_manager.get<int>("mysql_net_buffer_length", config.mysql_net_buffer_length);
            config.mysql_compress = config_manager.get<bool>("mysql_compress", config.mysql_compress);
            config.mysql_init_command = config_manager.get<std::string>("mysql_init_command", config.mysql_init_command);

            // 事务配置
            config.mysql_isolation_level = config_manager.get<std::string>("mysql_isolation_level", config.mysql_isolation_level);
            config.mysql_autocommit = config_manager.get<bool>("mysql_autocommit", config.mysql_autocommit);
            config.mysql_lock_wait_timeout = config_manager.get<int>("mysql_lock_wait_timeout", config.mysql_lock_wait_timeout);

            return config;
        }

        void DatabaseConfig::validate() const {
            if (mysql_host.empty()) {
                throw std::invalid_argument("MySQL host cannot be empty");
            }
            if (mysql_port < 1 || mysql_port > 65535) {
                throw std::invalid_argument("MySQL port must be between 1 and 65535");
            }
            if (mysql_user.empty()) {
                throw std::invalid_argument("MySQL user cannot be empty");
            }
            if (mysql_database.empty()) {
                throw std::invalid_argument("MySQL database cannot be empty");
            }
            if (mysql_pool_size == 0) {
                throw std::invalid_argument("MySQL pool size must be greater than 0");
            }
            if (mysql_max_pool_size < mysql_pool_size) {
                throw std::invalid_argument("MySQL max pool size must be >= pool size");
            }
        }

        RedisConfig RedisConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            RedisConfig config;

            // 基本连接配置
            config.redis_host = config_manager.get<std::string>("redis_host", config.redis_host);
            config.redis_port = config_manager.get<int>("redis_port", config.redis_port);
            config.redis_password = config_manager.get<std::string>("redis_password", config.redis_password);
            config.redis_database = config_manager.get<int>("redis_database", config.redis_database);

            // 连接池配置
            config.redis_pool_size = config_manager.get<size_t>("redis_pool_size", config.redis_pool_size);
            config.redis_max_pool_size = config_manager.get<size_t>("redis_max_pool_size", config.redis_max_pool_size);
            config.redis_min_pool_size = config_manager.get<size_t>("redis_min_pool_size", config.redis_min_pool_size);
            config.redis_pool_timeout_ms = config_manager.get<int>("redis_pool_timeout_ms", config.redis_pool_timeout_ms);
            config.redis_pool_max_idle_time_ms = config_manager.get<int>("redis_pool_max_idle_time_ms", config.redis_pool_max_idle_time_ms);
            config.redis_pool_test_on_borrow = config_manager.get<bool>("redis_pool_test_on_borrow", config.redis_pool_test_on_borrow);
            config.redis_pool_test_on_return = config_manager.get<bool>("redis_pool_test_on_return", config.redis_pool_test_on_return);

            // 连接参数
            config.redis_connect_timeout_ms = config_manager.get<int>("redis_connect_timeout_ms", config.redis_connect_timeout_ms);
            config.redis_socket_timeout_ms = config_manager.get<int>("redis_socket_timeout_ms", config.redis_socket_timeout_ms);
            config.redis_command_timeout_ms = config_manager.get<int>("redis_command_timeout_ms", config.redis_command_timeout_ms);
            config.redis_tcp_keepalive = config_manager.get<bool>("redis_tcp_keepalive", config.redis_tcp_keepalive);
            config.redis_tcp_keepalive_idle = config_manager.get<int>("redis_tcp_keepalive_idle", config.redis_tcp_keepalive_idle);
            config.redis_tcp_keepalive_interval = config_manager.get<int>("redis_tcp_keepalive_interval", config.redis_tcp_keepalive_interval);
            config.redis_tcp_keepalive_count = config_manager.get<int>("redis_tcp_keepalive_count", config.redis_tcp_keepalive_count);

            // 重试和恢复配置
            config.redis_auto_reconnect = config_manager.get<bool>("redis_auto_reconnect", config.redis_auto_reconnect);
            config.redis_max_reconnect_attempts = config_manager.get<int>("redis_max_reconnect_attempts", config.redis_max_reconnect_attempts);
            config.redis_reconnect_interval_ms = config_manager.get<int>("redis_reconnect_interval_ms", config.redis_reconnect_interval_ms);
            config.redis_retry_attempts = config_manager.get<int>("redis_retry_attempts", config.redis_retry_attempts);
            config.redis_retry_delay_ms = config_manager.get<int>("redis_retry_delay_ms", config.redis_retry_delay_ms);

            // 集群配置
            config.redis_cluster_enabled = config_manager.get<bool>("redis_cluster_enabled", config.redis_cluster_enabled);
            config.redis_cluster_max_redirections = config_manager.get<int>("redis_cluster_max_redirections", config.redis_cluster_max_redirections);
            config.redis_cluster_require_full_coverage = config_manager.get<bool>("redis_cluster_require_full_coverage", config.redis_cluster_require_full_coverage);

            // 哨兵配置
            config.redis_sentinel_enabled = config_manager.get<bool>("redis_sentinel_enabled", config.redis_sentinel_enabled);
            config.redis_sentinel_master_name = config_manager.get<std::string>("redis_sentinel_master_name", config.redis_sentinel_master_name);
            config.redis_sentinel_password = config_manager.get<std::string>("redis_sentinel_password", config.redis_sentinel_password);
            config.redis_sentinel_timeout_ms = config_manager.get<int>("redis_sentinel_timeout_ms", config.redis_sentinel_timeout_ms);

            // SSL/TLS配置
            config.redis_use_ssl = config_manager.get<bool>("redis_use_ssl", config.redis_use_ssl);
            config.redis_ssl_cert_file = config_manager.get<std::string>("redis_ssl_cert_file", config.redis_ssl_cert_file);
            config.redis_ssl_key_file = config_manager.get<std::string>("redis_ssl_key_file", config.redis_ssl_key_file);
            config.redis_ssl_ca_file = config_manager.get<std::string>("redis_ssl_ca_file", config.redis_ssl_ca_file);
            config.redis_ssl_verify_peer = config_manager.get<bool>("redis_ssl_verify_peer", config.redis_ssl_verify_peer);
            config.redis_ssl_ciphers = config_manager.get<std::string>("redis_ssl_ciphers", config.redis_ssl_ciphers);

            // 性能配置
            config.redis_pipeline_size = config_manager.get<int>("redis_pipeline_size", config.redis_pipeline_size);
            config.redis_enable_pipeline = config_manager.get<bool>("redis_enable_pipeline", config.redis_enable_pipeline);
            config.redis_max_memory_policy = config_manager.get<int>("redis_max_memory_policy", config.redis_max_memory_policy);
            config.redis_compress_data = config_manager.get<bool>("redis_compress_data", config.redis_compress_data);

            return config;
        }

        void RedisConfig::validate() const {
            if (redis_host.empty()) {
                throw std::invalid_argument("Redis host cannot be empty");
            }
            if (redis_port < 1 || redis_port > 65535) {
                throw std::invalid_argument("Redis port must be between 1 and 65535");
            }
            if (redis_database < 0 || redis_database > 15) {
                throw std::invalid_argument("Redis database must be between 0 and 15");
            }
            if (redis_pool_size == 0) {
                throw std::invalid_argument("Redis pool size must be greater than 0");
            }
            if (redis_max_pool_size < redis_pool_size) {
                throw std::invalid_argument("Redis max pool size must be >= pool size");
            }
        }

        ServerConfig ServerConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            ServerConfig config;

            config.host = config_manager.get<std::string>("server_host", config.host);
            config.port = config_manager.get<int>("server_port", config.port);
            config.thread_pool_size = config_manager.get<int>("thread_pool_size", config.thread_pool_size);
            config.max_connections = config_manager.get<int>("max_connections", config.max_connections);
            config.keep_alive_timeout = config_manager.get<int>("keep_alive_timeout", config.keep_alive_timeout);
            config.enable_ssl = config_manager.get<bool>("enable_ssl", config.enable_ssl);
            config.ssl_cert_file = config_manager.get<std::string>("ssl_cert_file", config.ssl_cert_file);
            config.ssl_key_file = config_manager.get<std::string>("ssl_key_file", config.ssl_key_file);

            return config;
        }

        void ServerConfig::validate() const {
            if (host.empty()) {
                throw std::invalid_argument("Server host cannot be empty");
            }
            if (port < 1 || port > 65535) {
                throw std::invalid_argument("Server port must be between 1 and 65535");
            }
            if (thread_pool_size < 1) {
                throw std::invalid_argument("Thread pool size must be greater than 0");
            }
            if (max_connections < 1) {
                throw std::invalid_argument("Max connections must be greater than 0");
            }
            if (keep_alive_timeout < 0) {
                throw std::invalid_argument("Keep alive timeout cannot be negative");
            }
            if (enable_ssl) {
                if (ssl_cert_file.empty()) {
                    throw std::invalid_argument("SSL cert file must be specified when SSL is enabled");
                }
                if (ssl_key_file.empty()) {
                    throw std::invalid_argument("SSL key file must be specified when SSL is enabled");
                }
            }
        }

        kafkaProducerConfig kafkaProducerConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            kafkaProducerConfig config;

            config.brokers = config_manager.get<std::string>("kafka_producer_brokers", config.brokers);
            config.client_id = config_manager.get<std::string>("kafka_producer_client_id", config.client_id);
            config.acks = config_manager.get<std::string>("kafka_producer_acks", config.acks);
            config.retries = config_manager.get<int>("kafka_producer_retries", config.retries);
            config.batch_size = config_manager.get<int>("kafka_producer_batch_size", config.batch_size);
            config.linger_ms = config_manager.get<int>("kafka_producer_linger_ms", config.linger_ms);
            config.buffer_memory = config_manager.get<int>("kafka_producer_buffer_memory", config.buffer_memory);
            config.compression_type = config_manager.get<std::string>("kafka_producer_compression_type", config.compression_type);
            config.max_request_size = config_manager.get<int>("kafka_producer_max_request_size", config.max_request_size);
            config.request_timeout_ms = config_manager.get<int>("kafka_producer_request_timeout_ms", config.request_timeout_ms);
            config.delivery_timeout_ms = config_manager.get<int>("kafka_producer_delivery_timeout_ms", config.delivery_timeout_ms);
            config.enable_idempotence = config_manager.get<bool>("kafka_producer_enable_idempotence", config.enable_idempotence);
            config.max_in_flight_requests = config_manager.get<int>("kafka_producer_max_in_flight_requests", config.max_in_flight_requests);

            // 安全配置
            config.security_protocol = config_manager.get<std::string>("kafka_producer_security_protocol", config.security_protocol);
            config.sasl_mechanism = config_manager.get<std::string>("kafka_producer_sasl_mechanism", config.sasl_mechanism);
            config.sasl_username = config_manager.get<std::string>("kafka_producer_sasl_username", config.sasl_username);
            config.sasl_password = config_manager.get<std::string>("kafka_producer_sasl_password", config.sasl_password);
            config.ssl_ca_location = config_manager.get<std::string>("kafka_producer_ssl_ca_location", config.ssl_ca_location);
            config.ssl_certificate_location = config_manager.get<std::string>("kafka_producer_ssl_certificate_location", config.ssl_certificate_location);
            config.ssl_key_location = config_manager.get<std::string>("kafka_producer_ssl_key_location", config.ssl_key_location);
            config.ssl_key_password = config_manager.get<std::string>("kafka_producer_ssl_key_password", config.ssl_key_password);

            return config;
        }

        void kafkaProducerConfig::validate() const {
            if (brokers.empty()) {
                throw std::invalid_argument("Kafka producer brokers cannot be empty");
            }
            if (acks != "0" && acks != "1" && acks != "all") {
                throw std::invalid_argument("Kafka producer acks must be '0', '1', or 'all'");
            }
            if (retries < 0) {
                throw std::invalid_argument("Kafka producer retries cannot be negative");
            }
            if (batch_size < 0) {
                throw std::invalid_argument("Kafka producer batch size cannot be negative");
            }
            if (linger_ms < 0) {
                throw std::invalid_argument("Kafka producer linger ms cannot be negative");
            }
            if (buffer_memory < 1024) {
                throw std::invalid_argument("Kafka producer buffer memory must be at least 1024 bytes");
            }
            std::set<std::string> valid_compression = {"none", "gzip", "snappy", "lz4", "zstd"};
            if (valid_compression.find(compression_type) == valid_compression.end()) {
                throw std::invalid_argument("Invalid Kafka producer compression type: " + compression_type);
            }
            if (max_request_size < 1024) {
                throw std::invalid_argument("Kafka producer max request size must be at least 1024 bytes");
            }
            if (request_timeout_ms < 1000) {
                throw std::invalid_argument("Kafka producer request timeout must be at least 1000 ms");
            }
            if (delivery_timeout_ms < request_timeout_ms) {
                throw std::invalid_argument("Kafka producer delivery timeout must be >= request timeout");
            }
            if (max_in_flight_requests < 1) {
                throw std::invalid_argument("Kafka producer max in flight requests must be at least 1");
            }
        }

        kafkaConsumerConfig kafkaConsumerConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            kafkaConsumerConfig config;

            config.brokers = config_manager.get<std::string>("kafka_consumer_brokers", config.brokers);
            config.group_id = config_manager.get<std::string>("kafka_consumer_group_id", config.group_id);
            config.client_id = config_manager.get<std::string>("kafka_consumer_client_id", config.client_id);
            config.auto_offset_reset = config_manager.get<std::string>("kafka_consumer_auto_offset_reset", config.auto_offset_reset);
            config.enable_auto_commit = config_manager.get<bool>("kafka_consumer_enable_auto_commit", config.enable_auto_commit);
            config.auto_commit_interval_ms = config_manager.get<int>("kafka_consumer_auto_commit_interval_ms", config.auto_commit_interval_ms);
            config.session_timeout_ms = config_manager.get<int>("kafka_consumer_session_timeout_ms", config.session_timeout_ms);
            config.heartbeat_interval_ms = config_manager.get<int>("kafka_consumer_heartbeat_interval_ms", config.heartbeat_interval_ms);
            config.max_poll_interval_ms = config_manager.get<int>("kafka_consumer_max_poll_interval_ms", config.max_poll_interval_ms);
            config.max_poll_records = config_manager.get<int>("kafka_consumer_max_poll_records", config.max_poll_records);
            config.fetch_min_bytes = config_manager.get<int>("kafka_consumer_fetch_min_bytes", config.fetch_min_bytes);
            config.fetch_max_wait_ms = config_manager.get<int>("kafka_consumer_fetch_max_wait_ms", config.fetch_max_wait_ms);
            config.max_partition_fetch_bytes = config_manager.get<int>("kafka_consumer_max_partition_fetch_bytes", config.max_partition_fetch_bytes);
            config.check_crcs = config_manager.get<bool>("kafka_consumer_check_crcs", config.check_crcs);
            config.isolation_level = config_manager.get<std::string>("kafka_consumer_isolation_level", config.isolation_level);

            // 安全配置
            config.security_protocol = config_manager.get<std::string>("kafka_consumer_security_protocol", config.security_protocol);
            config.sasl_mechanism = config_manager.get<std::string>("kafka_consumer_sasl_mechanism", config.sasl_mechanism);
            config.sasl_username = config_manager.get<std::string>("kafka_consumer_sasl_username", config.sasl_username);
            config.sasl_password = config_manager.get<std::string>("kafka_consumer_sasl_password", config.sasl_password);
            config.ssl_ca_location = config_manager.get<std::string>("kafka_consumer_ssl_ca_location", config.ssl_ca_location);
            config.ssl_certificate_location = config_manager.get<std::string>("kafka_consumer_ssl_certificate_location", config.ssl_certificate_location);
            config.ssl_key_location = config_manager.get<std::string>("kafka_consumer_ssl_key_location", config.ssl_key_location);
            config.ssl_key_password = config_manager.get<std::string>("kafka_consumer_ssl_key_password", config.ssl_key_password);

            return config;
        }

        void kafkaConsumerConfig::validate() const {
            if (brokers.empty()) {
                throw std::invalid_argument("Kafka consumer brokers cannot be empty");
            }
            if (auto_offset_reset != "earliest" && auto_offset_reset != "latest" && auto_offset_reset != "none") {
                throw std::invalid_argument("Kafka consumer auto offset reset must be 'earliest', 'latest', or 'none'");
            }
            if (auto_commit_interval_ms < 1000) {
                throw std::invalid_argument("Kafka consumer auto commit interval must be at least 1000 ms");
            }
            if (session_timeout_ms < 6000 || session_timeout_ms > 300000) {
                throw std::invalid_argument("Kafka consumer session timeout must be between 6000 and 300000 ms");
            }
            if (heartbeat_interval_ms >= session_timeout_ms / 3) {
                throw std::invalid_argument("Kafka consumer heartbeat interval must be < session timeout / 3");
            }
            if (max_poll_interval_ms < session_timeout_ms) {
                throw std::invalid_argument("Kafka consumer max poll interval must be >= session timeout");
            }
            if (max_poll_records < 1) {
                throw std::invalid_argument("Kafka consumer max poll records must be at least 1");
            }
            if (fetch_min_bytes < 1) {
                throw std::invalid_argument("Kafka consumer fetch min bytes must be at least 1");
            }
            if (fetch_max_wait_ms < 0) {
                throw std::invalid_argument("Kafka consumer fetch max wait cannot be negative");
            }
            if (max_partition_fetch_bytes < 1024) {
                throw std::invalid_argument("Kafka consumer max partition fetch bytes must be at least 1024");
            }
            if (isolation_level != "read_uncommitted" && isolation_level != "read_committed") {
                throw std::invalid_argument("Kafka consumer isolation level must be 'read_uncommitted' or 'read_committed'");
            }
        }

        KafkaConfig KafkaConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            KafkaConfig config;

            // 加载生产者和消费者配置
            config.producer = kafkaProducerConfig::fromConfigManager();
            config.consumer = kafkaConsumerConfig::fromConfigManager();

            // 向后兼容的通用配置
            config.brokers = config_manager.get<std::string>("kafka_brokers", config.brokers);
            config.group_id = config_manager.get<std::string>("kafka_group_id", config.group_id);
            config.client_id = config_manager.get<std::string>("kafka_client_id", config.client_id);
            config.session_timeout_ms = config_manager.get<int>("session_timeout_ms", config.session_timeout_ms);
            config.max_poll_interval_ms = config_manager.get<int>("max_poll_interval_ms", config.max_poll_interval_ms);
            config.offset_reset = config_manager.get<std::string>("offset_reset", config.offset_reset);
            config.enable_auto_commit = config_manager.get<bool>("enable_auto_commit", config.enable_auto_commit);

            return config;
        }

        void KafkaConfig::validate() const {
            // 验证生产者和消费者配置
            try {
                producer.validate();
            } catch (const std::exception& e) {
                throw std::invalid_argument("Kafka producer config validation failed: " + std::string(e.what()));
            }

            try {
                consumer.validate();
            } catch (const std::exception& e) {
                throw std::invalid_argument("Kafka consumer config validation failed: " + std::string(e.what()));
            }

            // 验证通用配置（向后兼容）
            if (brokers.empty()) {
                throw std::invalid_argument("Kafka brokers cannot be empty");
            }
            if (session_timeout_ms < 1000 || session_timeout_ms > 300000) {
                throw std::invalid_argument("Session timeout must be between 1000 and 300000 ms");
            }
            if (max_poll_interval_ms < 1000 || max_poll_interval_ms > 600000) {
                throw std::invalid_argument("Max poll interval must be between 1000 and 600000 ms");
            }
            if (offset_reset != "earliest" && offset_reset != "latest" && offset_reset != "none") {
                throw std::invalid_argument("Offset reset must be 'earliest', 'latest', or 'none'");
            }
        }



        LogLevel LogConfig::stringToLogLevel(const std::string& level_str) {
            if (level_str == "TRACE") return LogLevel::TRACE;
            if (level_str == "DEBUG") return LogLevel::DEBUG;
            if (level_str == "INFO") return LogLevel::INFO;
            if (level_str == "WARNING") return LogLevel::WARNING;
            if (level_str == "ERROR") return LogLevel::ERROR;
            if (level_str == "FATAL") return LogLevel::FATAL;
            throw std::invalid_argument("Invalid log level: " + level_str);
        }

        std::string LogConfig::logLevelToString(LogLevel level) {
            switch (level) {
                case LogLevel::TRACE: return "TRACE";
                case LogLevel::DEBUG: return "DEBUG";
                case LogLevel::INFO: return "INFO";
                case LogLevel::WARNING: return "WARNING";
                case LogLevel::ERROR: return "ERROR";
                case LogLevel::FATAL: return "FATAL";
                default: return "INFO";
            }
        }

        LogConfig LogConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            LogConfig config;

            // 全局日志级别
            std::string global_level_str = config_manager.get<std::string>("log_level", config.level);
            config.global_level = stringToLogLevel(global_level_str);
            config.level = global_level_str;

            // 控制台日志配置
            config.console.enabled = config_manager.get<bool>("log_console_enabled", config.console.enabled);
            config.console.pattern = config_manager.get<std::string>("log_console_pattern", config.console.pattern);
            config.console.colored = config_manager.get<bool>("log_console_colored", config.console.colored);
            std::string console_level_str = config_manager.get<std::string>("log_console_level", logLevelToString(config.console.min_level));
            config.console.min_level = stringToLogLevel(console_level_str);
            config.console.flush_on_newline = config_manager.get<bool>("log_console_flush_on_newline", config.console.flush_on_newline);

            // 文件日志配置
            config.file.enabled = config_manager.get<bool>("log_file_enabled", config.file.enabled);
            config.file.file_path = config_manager.get<std::string>("log_file_path", config.file.file_path);
            config.file.pattern = config_manager.get<std::string>("log_file_pattern", config.file.pattern);
            config.file.max_file_size = config_manager.get<size_t>("log_file_max_size", config.file.max_file_size);
            config.file.max_files = config_manager.get<int>("log_file_max_files", config.file.max_files);
            std::string file_level_str = config_manager.get<std::string>("log_file_level", logLevelToString(config.file.min_level));
            config.file.min_level = stringToLogLevel(file_level_str);
            config.file.auto_flush = config_manager.get<bool>("log_file_auto_flush", config.file.auto_flush);
            config.file.flush_interval_ms = config_manager.get<int>("log_file_flush_interval_ms", config.file.flush_interval_ms);
            config.file.rotation_policy = config_manager.get<std::string>("log_file_rotation_policy", config.file.rotation_policy);
            config.file.rotation_time = config_manager.get<std::string>("log_file_rotation_time", config.file.rotation_time);

            // 网络日志配置
            config.network.enabled = config_manager.get<bool>("log_network_enabled", config.network.enabled);
            config.network.protocol = config_manager.get<std::string>("log_network_protocol", config.network.protocol);
            config.network.host = config_manager.get<std::string>("log_network_host", config.network.host);
            config.network.port = config_manager.get<int>("log_network_port", config.network.port);
            config.network.facility = config_manager.get<std::string>("log_network_facility", config.network.facility);
            std::string network_level_str = config_manager.get<std::string>("log_network_level", logLevelToString(config.network.min_level));
            config.network.min_level = stringToLogLevel(network_level_str);
            config.network.timeout_ms = config_manager.get<int>("log_network_timeout_ms", config.network.timeout_ms);
            config.network.retry_attempts = config_manager.get<int>("log_network_retry_attempts", config.network.retry_attempts);

            // 异步日志配置
            config.async.enabled = config_manager.get<bool>("log_async_enabled", config.async.enabled);
            config.async.queue_size = config_manager.get<size_t>("log_async_queue_size", config.async.queue_size);
            config.async.thread_count = config_manager.get<int>("log_async_thread_count", config.async.thread_count);
            config.async.flush_interval_ms = config_manager.get<int>("log_async_flush_interval_ms", config.async.flush_interval_ms);
            config.async.overflow_policy = config_manager.get<std::string>("log_async_overflow_policy", config.async.overflow_policy);
            config.async.timeout_ms = config_manager.get<int>("log_async_timeout_ms", config.async.timeout_ms);

            // 向后兼容的简化配置
            config.enable_console = config.console.enabled;
            config.enable_file = config.file.enabled;
            config.file_path = config.file.file_path;
            config.max_file_size = config.file.max_file_size;
            config.max_files = config.file.max_files;
            config.enable_async = config.async.enabled;

            return config;
        }

        void LogConfig::validate() const {
            std::set<std::string> valid_levels = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};
            if (valid_levels.find(level) == valid_levels.end()) {
                throw std::invalid_argument("Invalid log level: " + level);
            }
            if (enable_file && file_path.empty()) {
                throw std::invalid_argument("Log file path cannot be empty when file logging is enabled");
            }
            if (max_file_size < 1024) {
                throw std::invalid_argument("Max file size must be at least 1024 bytes");
            }
            if (max_files < 1) {
                throw std::invalid_argument("Max files must be at least 1");
            }
        }

        NetworkConfig NetworkConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            NetworkConfig config;

            config.bind_address = config_manager.get<std::string>("network_bind_address", config.bind_address);
            config.listen_port = config_manager.get<int>("network_listen_port", config.listen_port);
            config.backlog = config_manager.get<int>("network_backlog", config.backlog);
            config.worker_threads = config_manager.get<int>("network_worker_threads", config.worker_threads);
            config.io_threads = config_manager.get<int>("network_io_threads", config.io_threads);
            config.tcp_nodelay = config_manager.get<bool>("network_tcp_nodelay", config.tcp_nodelay);
            config.so_reuseaddr = config_manager.get<bool>("network_so_reuseaddr", config.so_reuseaddr);
            config.so_keepalive = config_manager.get<bool>("network_so_keepalive", config.so_keepalive);
            config.send_buffer_size = config_manager.get<int>("network_send_buffer_size", config.send_buffer_size);
            config.recv_buffer_size = config_manager.get<int>("network_recv_buffer_size", config.recv_buffer_size);
            config.connection_timeout = config_manager.get<int>("network_connection_timeout", config.connection_timeout);
            config.read_timeout = config_manager.get<int>("network_read_timeout", config.read_timeout);
            config.write_timeout = config_manager.get<int>("network_write_timeout", config.write_timeout);
            config.max_connections = config_manager.get<int>("network_max_connections", config.max_connections);
            config.enable_epoll = config_manager.get<bool>("network_enable_epoll", config.enable_epoll);

            return config;
        }

        void NetworkConfig::validate() const {
            if (bind_address.empty()) {
                throw std::invalid_argument("Network bind address cannot be empty");
            }
            if (listen_port < 1 || listen_port > 65535) {
                throw std::invalid_argument("Network listen port must be between 1 and 65535");
            }
            if (backlog < 1) {
                throw std::invalid_argument("Network backlog must be greater than 0");
            }
            if (worker_threads < 1) {
                throw std::invalid_argument("Network worker threads must be greater than 0");
            }
            if (io_threads < 1) {
                throw std::invalid_argument("Network IO threads must be greater than 0");
            }
            if (send_buffer_size < 1024) {
                throw std::invalid_argument("Send buffer size must be at least 1024 bytes");
            }
            if (recv_buffer_size < 1024) {
                throw std::invalid_argument("Receive buffer size must be at least 1024 bytes");
            }
            if (connection_timeout < 1) {
                throw std::invalid_argument("Connection timeout must be greater than 0");
            }
            if (read_timeout < 1) {
                throw std::invalid_argument("Read timeout must be greater than 0");
            }
            if (write_timeout < 1) {
                throw std::invalid_argument("Write timeout must be greater than 0");
            }
            if (max_connections < 1) {
                throw std::invalid_argument("Max connections must be greater than 0");
            }
        }

        SchedulerConfig SchedulerConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            SchedulerConfig config;

            config.worker_threads = config_manager.get<int>("scheduler_worker_threads", config.worker_threads);
            config.max_pending_tasks = config_manager.get<int>("scheduler_max_pending_tasks", config.max_pending_tasks);
            config.task_queue_size = config_manager.get<int>("scheduler_task_queue_size", config.task_queue_size);
            config.tick_interval_ms = config_manager.get<int>("scheduler_tick_interval_ms", config.tick_interval_ms);
            config.enable_metrics = config_manager.get<bool>("scheduler_enable_metrics", config.enable_metrics);
            config.enable_task_timeout = config_manager.get<bool>("scheduler_enable_task_timeout", config.enable_task_timeout);
            config.default_task_timeout_ms = config_manager.get<int>("scheduler_default_task_timeout_ms", config.default_task_timeout_ms);
            config.max_retry_attempts = config_manager.get<int>("scheduler_max_retry_attempts", config.max_retry_attempts);
            config.retry_delay_ms = config_manager.get<int>("scheduler_retry_delay_ms", config.retry_delay_ms);
            config.enable_priority_queue = config_manager.get<bool>("scheduler_enable_priority_queue", config.enable_priority_queue);
            config.high_priority_threshold = config_manager.get<int>("scheduler_high_priority_threshold", config.high_priority_threshold);

            return config;
        }

        void SchedulerConfig::validate() const {
            if (worker_threads < 1) {
                throw std::invalid_argument("Scheduler worker threads must be greater than 0");
            }
            if (max_pending_tasks < 1) {
                throw std::invalid_argument("Max pending tasks must be greater than 0");
            }
            if (task_queue_size < 1) {
                throw std::invalid_argument("Task queue size must be greater than 0");
            }
            if (tick_interval_ms < 1) {
                throw std::invalid_argument("Tick interval must be greater than 0");
            }
            if (default_task_timeout_ms < 1000) {
                throw std::invalid_argument("Default task timeout must be at least 1000 ms");
            }
            if (max_retry_attempts < 0) {
                throw std::invalid_argument("Max retry attempts cannot be negative");
            }
            if (retry_delay_ms < 0) {
                throw std::invalid_argument("Retry delay cannot be negative");
            }
            if (high_priority_threshold < 0) {
                throw std::invalid_argument("High priority threshold cannot be negative");
            }
        }

        ThreadPoolConfig ThreadPoolConfig::fromConfigManager() {
            auto& config_manager = ConfigManager::getInstance();
            ThreadPoolConfig config;

            config.core_pool_size = config_manager.get<int>("thread_pool_core_size", config.core_pool_size);
            config.maximum_pool_size = config_manager.get<int>("thread_pool_max_size", config.maximum_pool_size);
            config.keep_alive_time_ms = config_manager.get<int>("thread_pool_keep_alive_ms", config.keep_alive_time_ms);
            config.queue_capacity = config_manager.get<int>("thread_pool_queue_capacity", config.queue_capacity);
            config.allow_core_thread_timeout = config_manager.get<bool>("thread_pool_allow_core_timeout", config.allow_core_thread_timeout);
            config.rejection_policy = config_manager.get<std::string>("thread_pool_rejection_policy", config.rejection_policy);
            config.prestartAllCoreThreads = config_manager.get<bool>("thread_pool_prestart_core_threads", config.prestartAllCoreThreads);
            config.thread_priority = config_manager.get<int>("thread_pool_thread_priority", config.thread_priority);
            config.thread_name_prefix = config_manager.get<std::string>("thread_pool_name_prefix", config.thread_name_prefix);
            config.enable_monitoring = config_manager.get<bool>("thread_pool_enable_monitoring", config.enable_monitoring);
            config.monitoring_interval_ms = config_manager.get<int>("thread_pool_monitoring_interval_ms", config.monitoring_interval_ms);

            return config;
        }

        void ThreadPoolConfig::validate() const {
            if (core_pool_size < 0) {
                throw std::invalid_argument("Core pool size cannot be negative");
            }
            if (maximum_pool_size < 1) {
                throw std::invalid_argument("Maximum pool size must be greater than 0");
            }
            if (maximum_pool_size < core_pool_size) {
                throw std::invalid_argument("Maximum pool size must be >= core pool size");
            }
            if (keep_alive_time_ms < 0) {
                throw std::invalid_argument("Keep alive time cannot be negative");
            }
            if (queue_capacity < 0) {
                throw std::invalid_argument("Queue capacity cannot be negative");
            }
            std::set<std::string> valid_policies = {"abort", "discard", "caller_runs", "discard_oldest"};
            if (valid_policies.find(rejection_policy) == valid_policies.end()) {
                throw std::invalid_argument("Invalid rejection policy: " + rejection_policy);
            }
            if (thread_priority < -20 || thread_priority > 19) {
                throw std::invalid_argument("Thread priority must be between -20 and 19");
            }
            if (monitoring_interval_ms < 1000) {
                throw std::invalid_argument("Monitoring interval must be at least 1000 ms");
            }
        }






    }
}