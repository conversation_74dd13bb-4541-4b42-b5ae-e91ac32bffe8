# 简化的HTTP模块CMakeLists.txt
# 用于调试构建问题

cmake_minimum_required(VERSION 3.10)

# 直接列出所有源文件
set(HTTP_SOURCES
        ${CMAKE_CURRENT_SOURCE_DIR}/http_request.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/http_response.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/http_middleware.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/http_router.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/http_session.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/http_server.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/http_client.cpp
)

# 检查每个源文件是否存在
foreach(source_file ${HTTP_SOURCES})
    if(NOT EXISTS ${source_file})
        message(FATAL_ERROR "Source file does not exist: ${source_file}")
    else()
        message(STATUS "Found source file: ${source_file}")
    endif()
endforeach()

# 创建库
add_library(http_module STATIC ${HTTP_SOURCES})

# 设置C++标准
set_target_properties(http_module PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
)

# 包含目录
target_include_directories(http_module PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

# 基础链接
target_link_libraries(http_module PUBLIC pthread)

message(STATUS "HTTP module created successfully")
