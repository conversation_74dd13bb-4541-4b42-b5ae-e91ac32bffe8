# 主要测试配置文件 - YAML格式
# 用于测试ConfigManager的完整功能

# 系统配置
system:
  version: "2.0.0"
  environment: "test"
  description: "Config模块综合测试环境"
  debug_mode: true

# 数据库配置 - MySQL
mysql_host: "test-mysql-server"
mysql_port: 3306
mysql_user: "test_user"
mysql_password: "test_password_123"
mysql_database: "test_database"
mysql_pool_size: 10
mysql_max_pool_size: 50
mysql_connection_timeout: 30
mysql_read_timeout: 60
mysql_write_timeout: 60
mysql_charset: "utf8mb4"
mysql_ssl_mode: "DISABLED"
mysql_autocommit: true
mysql_isolation_level: "REPEATABLE-READ"

# Redis配置
redis_host: "test-redis-server"
redis_port: 6379
redis_password: "redis_test_pass"
redis_database: 2
redis_pool_size: 8
redis_max_pool_size: 20
redis_connection_timeout: 10
redis_read_timeout: 30
redis_write_timeout: 30

# 服务器配置
server_host: "0.0.0.0"
server_port: 8080
thread_pool_size: 8
max_connections: 1000
keep_alive_timeout: 60
enable_ssl: true
ssl_cert_file: "/etc/ssl/certs/server.crt"
ssl_key_file: "/etc/ssl/private/server.key"

# Kafka生产者配置
kafka_producer_brokers: "test-kafka1:9092,test-kafka2:9092"
kafka_producer_client_id: "test-producer-client"
kafka_producer_acks: "all"
kafka_producer_retries: 5
kafka_producer_batch_size: 32768
kafka_producer_linger_ms: 10
kafka_producer_compression_type: "gzip"
kafka_producer_enable_idempotence: true
kafka_producer_max_in_flight_requests: 5

# Kafka消费者配置
kafka_consumer_brokers: "test-kafka1:9092,test-kafka2:9092"
kafka_consumer_group_id: "test-consumer-group"
kafka_consumer_client_id: "test-consumer-client"
kafka_consumer_auto_offset_reset: "earliest"
kafka_consumer_enable_auto_commit: false
kafka_consumer_max_poll_records: 1000
kafka_consumer_session_timeout_ms: 30000
kafka_consumer_heartbeat_interval_ms: 3000
kafka_consumer_isolation_level: "read_committed"

# 日志配置
log_level: "INFO"
log_console_enabled: true
log_console_colored: true
log_console_level: "DEBUG"
log_console_pattern: "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] %v"

log_file_enabled: true
log_file_path: "/var/log/test_app.log"
log_file_level: "INFO"
log_file_max_size: 104857600  # 100MB
log_file_max_files: 10
log_file_rotation_policy: "size"
log_file_rotation_time: "00:00"

log_network_enabled: false
log_network_protocol: "tcp"
log_network_host: "log-collector"
log_network_port: 514
log_network_level: "ERROR"

log_async_enabled: true
log_async_queue_size: 8192
log_async_thread_count: 2
log_async_overflow_policy: "block"

# 网络配置
network_bind_address: "0.0.0.0"
network_listen_port: 9090
network_backlog: 256
network_worker_threads: 4
network_io_threads: 2
network_tcp_nodelay: true
network_so_reuseaddr: true
network_so_keepalive: true
network_send_buffer_size: 65536
network_recv_buffer_size: 65536
network_connection_timeout: 15
network_read_timeout: 60
network_write_timeout: 60
network_max_connections: 2000
network_enable_epoll: true

# 调度器配置
scheduler_worker_threads: 4
scheduler_max_pending_tasks: 1000
scheduler_task_queue_size: 500
scheduler_tick_interval_ms: 100
scheduler_enable_metrics: true
scheduler_enable_task_timeout: true
scheduler_default_task_timeout_ms: 10000
scheduler_max_retry_attempts: 3
scheduler_retry_delay_ms: 1000
scheduler_enable_priority_queue: true
scheduler_high_priority_threshold: 100

# 线程池配置
thread_pool_core_size: 4
thread_pool_max_size: 16
thread_pool_keep_alive_ms: 60000
thread_pool_queue_capacity: 1000
thread_pool_allow_core_timeout: true
thread_pool_rejection_policy: "caller_runs"
thread_pool_prestart_core_threads: true
thread_pool_thread_priority: 0
thread_pool_name_prefix: "test-worker-"
thread_pool_enable_monitoring: true
thread_pool_monitoring_interval_ms: 5000

# 测试专用配置
test_string_value: "comprehensive_test_string"
test_integer_value: 12345
test_boolean_value: true
test_double_value: 3.141592653589793
test_array_value: ["item1", "item2", "item3"]

# 性能测试配置
performance_test_iterations: 10000
performance_test_threads: 4
performance_test_timeout_ms: 30000

# 边界值测试
boundary_min_port: 1
boundary_max_port: 65535
boundary_zero_value: 0
boundary_negative_value: -1
boundary_large_number: 2147483647
boundary_empty_string: ""
boundary_long_string: "这是一个很长的字符串用于测试配置管理器处理长字符串的能力和性能表现以及内存使用情况"
