# Redis测试环境配置文件
# 专门用于Redis综合测试的配置

database:
  redis:
    # ==================== Redis连接配置 ====================
    host: "redis"                       # Redis服务器地址
    port: 6379                          # Redis服务器端口
    password: "123456"                  # Redis密码
    database: 0                         # 使用数据库0进行测试
    
    # ==================== 连接池配置 ====================
    pool:
      initial_size: 3                   # 测试用较小的初始连接数
      max_size: 10                      # 测试用较小的最大连接数
      min_size: 1                       # 最小连接数
      idle_timeout: 60                  # 测试用较短的空闲超时时间（秒）
      health_check_interval: 10         # 测试用较短的健康检查间隔（秒）
    
    # ==================== 线程池集成配置 ====================
    thread_pool:
      enable_async_operations: true     # 启用异步操作进行测试
      core_size: 2                      # 测试用较小的核心线程数
      max_size: 4                       # 测试用较小的最大线程数
      keep_alive_ms: 30000             # 测试用较短的保活时间
      queue_capacity: 100              # 测试用较小的队列容量
      enable_monitoring: true          # 启用监控以便观察测试过程

# ==================== 日志配置 ====================
logging:
  level: "DEBUG"                       # 测试时使用DEBUG级别
  enable_console: true                 # 启用控制台输出
  enable_file: true                    # 启用文件输出
  log_file_path: "logs/redis_test.log" # 测试日志文件
  enable_async: false                  # 测试时禁用异步日志以确保日志完整性

# ==================== 测试配置 ====================
test:
  redis:
    # 测试用的键前缀，避免与生产数据冲突
    key_prefix: "test:"
    
    # 性能测试配置
    performance:
      iterations: 1000                 # 性能测试迭代次数
      concurrent_threads: 5            # 并发测试线程数
      operations_per_thread: 20       # 每个线程的操作数
    
    # 超时配置
    timeouts:
      connection_timeout_ms: 1000      # 连接获取超时
      operation_timeout_ms: 500       # 操作超时
      async_operation_timeout_s: 5     # 异步操作超时
