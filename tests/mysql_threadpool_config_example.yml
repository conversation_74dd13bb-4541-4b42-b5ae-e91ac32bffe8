# MySQL连接池线程池集成配置示例
# 展示如何配置MySQL连接池的线程池功能

database:
  mysql:
    # 基础连接配置
    host: "localhost"
    port: 3306
    user: "root"
    password: "password"
    database: "test_db"
    
    # 连接池配置
    pool:
      initial_size: 5          # 初始连接数
      max_size: 20             # 最大连接数
      min_size: 2              # 最小连接数
      idle_timeout: 600        # 空闲超时时间(秒)
      health_check_interval: 30 # 健康检查间隔(秒)
    
    # 连接属性配置
    connection:
      auto_reconnect: true
      charset: "utf8mb4"
      connect_timeout: 10      # 连接超时(秒)
      read_timeout: 30         # 读取超时(秒)
      write_timeout: 30        # 写入超时(秒)
    
    # ==================== 线程池配置 ====================
    thread_pool:
      # 核心线程数 - 始终保持活跃的线程数量
      # 建议设置为CPU核心数的1/4到1/2
      core_size: 2
      
      # 最大线程数 - 线程池可以创建的最大线程数
      # 建议设置为CPU核心数的1到2倍
      max_size: 8
      
      # 任务队列容量 - 等待执行的任务队列大小
      # 建议根据预期的并发连接创建需求设置
      queue_capacity: 100
      
      # 线程存活时间(毫秒) - 空闲线程的最大存活时间
      # 超过核心线程数的线程在空闲时间超过此值后会被回收
      keep_alive_ms: 60000
      
      # 是否启用异步操作 - 控制是否使用线程池进行异步操作
      # true: 启用异步连接创建、健康检查、连接清理
      # false: 使用传统的同步操作
      enable_async_operations: true

# ==================== 性能调优建议 ====================
# 
# 1. 核心线程数(core_size)调优：
#    - 低负载环境: 1-2个线程
#    - 中等负载环境: 2-4个线程  
#    - 高负载环境: 4-8个线程
#
# 2. 最大线程数(max_size)调优：
#    - 应该是核心线程数的2-4倍
#    - 不建议超过16个线程(避免过多的上下文切换)
#
# 3. 队列容量(queue_capacity)调优：
#    - 小型应用: 50-100
#    - 中型应用: 100-500
#    - 大型应用: 500-1000
#
# 4. 线程存活时间(keep_alive_ms)调优：
#    - 频繁操作: 30000-60000ms (30-60秒)
#    - 偶发操作: 60000-300000ms (1-5分钟)
#
# 5. 异步操作开关(enable_async_operations)：
#    - 高并发环境建议启用
#    - 简单应用可以禁用以减少复杂性

# ==================== 不同场景的推荐配置 ====================

# 开发环境配置:
# thread_pool:
#   core_size: 1
#   max_size: 4
#   queue_capacity: 50
#   keep_alive_ms: 30000
#   enable_async_operations: true

# 测试环境配置:
# thread_pool:
#   core_size: 2
#   max_size: 6
#   queue_capacity: 100
#   keep_alive_ms: 60000
#   enable_async_operations: true

# 生产环境配置:
# thread_pool:
#   core_size: 4
#   max_size: 12
#   queue_capacity: 200
#   keep_alive_ms: 60000
#   enable_async_operations: true

# 高负载生产环境配置:
# thread_pool:
#   core_size: 6
#   max_size: 16
#   queue_capacity: 500
#   keep_alive_ms: 90000
#   enable_async_operations: true
