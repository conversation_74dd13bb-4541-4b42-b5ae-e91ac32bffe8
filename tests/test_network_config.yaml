# 网络模块测试配置文件
# 用于测试配置热更新功能

network:
  # Socket基础配置
  bind_address: "127.0.0.1"
  port: 8080
  backlog: 1024
  reuse_address: true
  reuse_port: false
  tcp_no_delay: true
  socket_keep_alive: true
  
  # Keep-Alive参数配置
  keep_alive_idle_time: 600    # 空闲时间（秒）
  keep_alive_interval: 60      # 探测间隔（秒）
  keep_alive_probes: 3         # 探测次数
  
  # Epoll配置
  enable_epoll: true
  epoll_timeout: 10000         # epoll_wait超时时间（毫秒）
  max_events: 1024             # 最大事件数
  init_event_list_size: 48     # 初始事件列表大小
  
  # EventLoop配置
  enable_event_loop_monitoring: true
  enable_pending_functors_optimization: true
  
  # 线程池配置
  enable_thread_pool: false
  thread_pool_size: 4
  use_dedicated_io_threads: false
  
  # 性能优化配置
  enable_channel_validation: true
  enable_epoll_resize_optimization: true
  epoll_resize_factor: 1.5

# 测试专用配置
test:
  # 测试超时设置
  test_timeout_ms: 5000
  
  # 性能测试参数
  performance_test_iterations: 1000
  stress_test_duration_seconds: 10
  
  # 并发测试参数
  max_concurrent_connections: 100
  concurrent_task_count: 10000
  
  # 内存测试参数
  memory_test_iterations: 1000
  memory_leak_threshold_mb: 10

# 日志配置
logging:
  level: "DEBUG"
  enable_console_output: true
  enable_file_output: true
  log_file_path: "logs/network_test.log"
  max_log_file_size_mb: 100
  max_log_files: 5
