# 环境变量配置文件
# 用于测试ConfigManager的环境变量加载功能

# 系统环境变量
SYSTEM_VERSION=2.0.0
SYSTEM_ENVIRONMENT=test
SYSTEM_DEBUG_MODE=true

# 数据库环境变量
MYSQL_HOST=env-mysql-server
MYSQL_PORT=3306
MYSQL_USER=env_test_user
MYSQL_PASSWORD=env_test_password
MYSQL_DATABASE=env_test_database
MYSQL_POOL_SIZE=20
MYSQL_MAX_POOL_SIZE=100

# Redis环境变量
REDIS_HOST=env-redis-server
REDIS_PORT=6379
REDIS_PASSWORD=env_redis_password
REDIS_DATABASE=4
REDIS_POOL_SIZE=15
REDIS_MAX_POOL_SIZE=40

# 服务器环境变量
SERVER_HOST=0.0.0.0
SERVER_PORT=8082
THREAD_POOL_SIZE=10
MAX_CONNECTIONS=1200
KEEP_ALIVE_TIMEOUT=90
ENABLE_SSL=true
SSL_CERT_FILE=/etc/ssl/env/server.crt
SSL_KEY_FILE=/etc/ssl/env/server.key

# Kafka环境变量
KAFKA_PRODUCER_BROKERS=env-kafka1:9092,env-kafka2:9092,env-kafka3:9092
KAFKA_PRODUCER_CLIENT_ID=env-producer-client
KAFKA_PRODUCER_ACKS=all
KAFKA_PRODUCER_RETRIES=10
KAFKA_PRODUCER_BATCH_SIZE=65536
KAFKA_PRODUCER_COMPRESSION_TYPE=lz4
KAFKA_PRODUCER_ENABLE_IDEMPOTENCE=true

KAFKA_CONSUMER_BROKERS=env-kafka1:9092,env-kafka2:9092,env-kafka3:9092
KAFKA_CONSUMER_GROUP_ID=env-consumer-group
KAFKA_CONSUMER_CLIENT_ID=env-consumer-client
KAFKA_CONSUMER_AUTO_OFFSET_RESET=earliest
KAFKA_CONSUMER_ENABLE_AUTO_COMMIT=false
KAFKA_CONSUMER_MAX_POLL_RECORDS=2000
KAFKA_CONSUMER_ISOLATION_LEVEL=read_committed

# 日志环境变量
LOG_LEVEL=ERROR
LOG_CONSOLE_ENABLED=true
LOG_CONSOLE_COLORED=true
LOG_CONSOLE_LEVEL=FATAL
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/var/log/env_test.log
LOG_FILE_LEVEL=ERROR
LOG_FILE_MAX_SIZE=209715200
LOG_FILE_MAX_FILES=20
LOG_ASYNC_ENABLED=true
LOG_ASYNC_QUEUE_SIZE=16384
LOG_ASYNC_THREAD_COUNT=4

# 网络环境变量
NETWORK_BIND_ADDRESS=0.0.0.0
NETWORK_LISTEN_PORT=9092
NETWORK_BACKLOG=512
NETWORK_WORKER_THREADS=8
NETWORK_IO_THREADS=4
NETWORK_TCP_NODELAY=true
NETWORK_SO_REUSEADDR=true
NETWORK_SO_KEEPALIVE=true
NETWORK_SEND_BUFFER_SIZE=131072
NETWORK_RECV_BUFFER_SIZE=131072
NETWORK_CONNECTION_TIMEOUT=30
NETWORK_READ_TIMEOUT=120
NETWORK_WRITE_TIMEOUT=120
NETWORK_MAX_CONNECTIONS=5000
NETWORK_ENABLE_EPOLL=true

# 调度器环境变量
SCHEDULER_WORKER_THREADS=8
SCHEDULER_MAX_PENDING_TASKS=2000
SCHEDULER_TASK_QUEUE_SIZE=1000
SCHEDULER_TICK_INTERVAL_MS=50
SCHEDULER_ENABLE_METRICS=true
SCHEDULER_ENABLE_TASK_TIMEOUT=true
SCHEDULER_DEFAULT_TASK_TIMEOUT_MS=15000
SCHEDULER_MAX_RETRY_ATTEMPTS=5
SCHEDULER_RETRY_DELAY_MS=2000
SCHEDULER_ENABLE_PRIORITY_QUEUE=true
SCHEDULER_HIGH_PRIORITY_THRESHOLD=200

# 线程池环境变量
THREAD_POOL_CORE_SIZE=8
THREAD_POOL_MAX_SIZE=32
THREAD_POOL_KEEP_ALIVE_MS=120000
THREAD_POOL_QUEUE_CAPACITY=2000
THREAD_POOL_ALLOW_CORE_TIMEOUT=true
THREAD_POOL_REJECTION_POLICY=abort
THREAD_POOL_PRESTART_CORE_THREADS=true
THREAD_POOL_THREAD_PRIORITY=0
THREAD_POOL_NAME_PREFIX=env-worker-
THREAD_POOL_ENABLE_MONITORING=true
THREAD_POOL_MONITORING_INTERVAL_MS=10000

# 测试数据环境变量
TEST_STRING_VALUE=env_test_string_value
TEST_INTEGER_VALUE=98765
TEST_BOOLEAN_VALUE=true
TEST_DOUBLE_VALUE=1.414213562373095
TEST_ARRAY_VALUE=env_item1,env_item2,env_item3,env_item4

# 性能测试环境变量
PERFORMANCE_TEST_ITERATIONS=20000
PERFORMANCE_TEST_THREADS=8
PERFORMANCE_TEST_TIMEOUT_MS=60000

# 边界值测试环境变量
BOUNDARY_MIN_PORT=1024
BOUNDARY_MAX_PORT=65535
BOUNDARY_ZERO_VALUE=0
BOUNDARY_NEGATIVE_VALUE=-999
BOUNDARY_LARGE_NUMBER=9223372036854775807
BOUNDARY_EMPTY_STRING=
BOUNDARY_LONG_STRING=这是一个超长的环境变量字符串用于测试配置管理器处理环境变量时的边界情况和性能表现包括内存使用效率和解析速度等各项指标

# 特殊字符测试
SPECIAL_CHARS_TEST=!@#$%^&*()_+-=[]{}|;:,.<>?
UNICODE_TEST=测试中文字符和特殊符号★☆♠♣♥♦
QUOTES_TEST="带引号的字符串测试"
SPACES_TEST=带 空 格 的 字 符 串
MULTILINE_TEST=第一行\n第二行\n第三行

# 路径测试
PATH_TEST_UNIX=/usr/local/bin:/usr/bin:/bin
PATH_TEST_WINDOWS=C:\Program Files\Test;C:\Windows\System32
FILE_PATH_TEST=/etc/config/test.conf
URL_TEST=https://test.example.com:8443/api/v1/config

# 数值边界测试
INT_MAX_TEST=2147483647
INT_MIN_TEST=-2147483648
LONG_MAX_TEST=9223372036854775807
DOUBLE_PRECISION_TEST=3.141592653589793238462643383279502884197
SCIENTIFIC_NOTATION_TEST=1.23e-10
