{"system": {"version": "2.0.0", "environment": "test", "description": "JSON格式配置测试", "debug_mode": true}, "database": {"mysql": {"host": "json-mysql-server", "port": 3306, "user": "json_test_user", "password": "json_test_pass", "database": "json_test_db", "pool_size": 15, "max_pool_size": 60, "connection_timeout": 25, "charset": "utf8mb4"}, "redis": {"host": "json-redis-server", "port": 6379, "password": "json_redis_pass", "database": 3, "pool_size": 12, "max_pool_size": 30}}, "server": {"host": "127.0.0.1", "port": 8081, "thread_pool_size": 6, "max_connections": 800, "keep_alive_timeout": 45, "enable_ssl": false}, "kafka": {"producer": {"brokers": "json-kafka:9092", "client_id": "<PERSON><PERSON>-producer", "acks": "1", "retries": 3, "batch_size": 16384, "compression_type": "snappy", "enable_idempotence": false}, "consumer": {"brokers": "json-kafka:9092", "group_id": "json-consumer-group", "client_id": "json-consumer", "auto_offset_reset": "latest", "enable_auto_commit": true, "max_poll_records": 500, "isolation_level": "read_uncommitted"}}, "logging": {"level": "WARN", "console": {"enabled": true, "colored": false, "level": "ERROR", "pattern": "[%Y-%m-%d %H:%M:%S] [%l] %v"}, "file": {"enabled": true, "path": "/tmp/json_test.log", "level": "WARN", "max_size": 52428800, "max_files": 5, "rotation_policy": "daily"}, "async": {"enabled": false, "queue_size": 4096, "thread_count": 1, "overflow_policy": "overrun_oldest"}}, "network": {"bind_address": "127.0.0.1", "listen_port": 9091, "backlog": 128, "worker_threads": 2, "io_threads": 1, "tcp_nodelay": false, "so_reuseaddr": false, "so_keepalive": false, "send_buffer_size": 32768, "recv_buffer_size": 32768, "connection_timeout": 20, "read_timeout": 45, "write_timeout": 45, "max_connections": 1500, "enable_epoll": false}, "scheduler": {"worker_threads": 3, "max_pending_tasks": 750, "task_queue_size": 300, "tick_interval_ms": 75, "enable_metrics": false, "enable_task_timeout": false, "default_task_timeout_ms": 8000, "max_retry_attempts": 2, "retry_delay_ms": 750, "enable_priority_queue": false, "high_priority_threshold": 75}, "thread_pool": {"core_size": 3, "max_size": 12, "keep_alive_ms": 45000, "queue_capacity": 750, "allow_core_timeout": false, "rejection_policy": "discard", "prestart_core_threads": false, "thread_priority": 5, "name_prefix": "json-worker-", "enable_monitoring": false, "monitoring_interval_ms": 4000}, "test_data": {"string_value": "json_test_string", "integer_value": 54321, "boolean_value": false, "double_value": 2.718281828459045, "array_value": ["json_item1", "json_item2", "json_item3"], "nested_object": {"nested_string": "nested_value", "nested_number": 999, "nested_boolean": true}}, "performance": {"test_iterations": 5000, "test_threads": 2, "timeout_ms": 15000}, "boundaries": {"min_value": 1, "max_value": 999999, "zero_value": 0, "negative_value": -100, "empty_string": "", "null_value": null, "large_string": "JSON格式的长字符串测试用于验证配置管理器对JSON格式配置文件的解析能力和性能表现"}}