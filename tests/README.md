# Tests Directory Structure

## 概述
此目录包含游戏微服务项目的综合测试套件，经过整理后只保留核心测试文件。

## 测试文件

### 保留的测试文件
1. **common_modules_comprehensive_test.cpp** - 通用模块综合测试
   - 测试线程池、任务调度器、数据库连接池等通用模块
   - 包含MySQL和Redis数据库测试（如果库可用）

2. **config_module_comprehensive_test.cpp** - 配置模块综合测试
   - 测试ConfigManager的所有功能
   - 支持YAML、JSON、环境变量配置
   - 包含热重载和配置监听功能测试

3. **kafka_comprehensive_test.cpp** - Kafka模块综合测试
   - 测试Kafka生产者和消费者
   - 包含配置验证、错误处理、性能测试
   - 支持真实Kafka服务器(kafka:9092)连接测试

4. **network_module_comprehensive_test.cpp** - 网络模块综合测试
   - 测试网络相关功能（仅Linux/macOS）
   - 包含Socket、EventLoop、Channel等组件测试

### 配置文件
- **kafka_test_config.yaml** - Kafka测试配置
- **test_config.env** - 环境变量配置文件
- **test_config_main.json** - JSON格式配置文件
- **test_config_main.yml** - YAML格式配置文件

## 构建和运行测试

### 构建所有测试
```bash
cd cmake-build-docker-debug
make
```

### 运行所有测试
```bash
make test
# 或者详细输出
make run_all_tests
```

### 运行特定模块测试
```bash
# 网络模块测试
make run_network_tests

# Kafka模块测试
make run_kafka_tests

# 通用模块测试
make run_common_tests

# Config模块测试
make run_config_tests
```

### 直接运行测试可执行文件
```bash
./tests/network_comprehensive_test
./tests/kafka_comprehensive_test
./tests/common_modules_comprehensive_test
./tests/config_module_comprehensive_test
```

### 使用CTest运行
```bash
# 运行所有测试
ctest --verbose

# 运行特定测试
ctest -R Network
ctest -R Kafka
ctest -R CommonModules
ctest -R Config

# 带超时运行
ctest --timeout 60
```

## 测试依赖

### 必需依赖
- pthread (线程支持)
- C++17 标准库

### 可选依赖
- **librdkafka++** - Kafka功能测试
- **yaml-cpp** - YAML配置文件支持
- **nlohmann/json** - JSON配置文件支持
- **MySQL Connector/C++** 或 **MySQL C API** - 数据库测试
- **hiredis** - Redis数据库测试

### 平台支持
- **Linux**: 支持所有测试
- **macOS**: 支持所有测试
- **Windows**: 部分测试受限（网络模块测试跳过）

## 测试超时设置
- 网络模块测试: 5分钟
- Kafka模块测试: 2分钟
- 通用模块测试: 1分钟
- Config模块测试: 2分钟

## 获取帮助
```bash
make test_help
```

## CMake库配置

### 清理后的库命名
经过整理，所有库名称已从 `temp_*` 前缀改为更简洁的命名：

- `config_lib` - 配置管理库
- `logger_lib` - 日志库
- `network_lib` - 网络库
- `thread_pool_lib` - 线程池库（通用模块）
- `thread_pool_lib_kafka` - 线程池库（Kafka专用）
- `task_scheduler_lib` - 任务调度器库
- `kafka_lib` - Kafka库
- `database_lib` - 数据库库（MySQL）
- `redis_lib` - Redis库

### 库依赖关系
```
network_lib -> logger_lib -> config_lib
kafka_lib -> thread_pool_lib_kafka -> logger_lib -> config_lib
common_modules_test -> thread_pool_lib + task_scheduler_lib + database_lib + redis_lib
```

## 注意事项
1. 确保Kafka服务器在 `kafka:9092` 可用（用于Kafka测试）
2. 数据库测试需要相应的数据库服务器运行
3. 某些测试可能需要特定的系统权限
4. 在CI/CD环境中建议使用超时设置避免测试挂起
5. 所有库名称已标准化，不再使用 `temp_` 前缀
