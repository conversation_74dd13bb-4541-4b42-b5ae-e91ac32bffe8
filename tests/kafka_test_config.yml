# Kafka测试环境配置文件
# 专门用于Kafka综合测试的配置

kafka:
  # ==================== Kafka生产者测试配置 ====================
  producer:
    # 基础连接配置
    brokers: "kafka:9092"               # Kafka服务器地址
    client_id: "test-producer"          # 测试生产者客户端ID
    
    # 可靠性配置（测试用）
    acks: 1                             # 确认级别
    retries: 2                          # 测试用较少重试次数
    
    # 性能配置（测试用）
    batch_size: 8192                    # 测试用较小批处理大小
    linger_ms: 5                        # 消息缓冲时间
    buffer_memory: 16777216             # 测试用较小缓冲区
    compression_type: "snappy"          # 压缩算法
    
    # 超时配置（测试用）
    request_timeout_ms: 10000           # 测试用较短超时时间
    delivery_timeout_ms: 30000          # 测试用较短投递超时
    
    # 线程池集成配置（测试用）
    thread_pool:
      enable_async_operations: true     # 启用异步操作进行测试
      core_size: 2                      # 测试用较小核心线程数
      max_size: 4                       # 测试用较小最大线程数
      keep_alive_ms: 30000             # 测试用较短保活时间
      queue_capacity: 100              # 测试用较小队列容量
      enable_monitoring: true          # 启用监控以便观察测试过程
      name_prefix: "test-producer-"    # 测试线程名前缀

  # ==================== Kafka消费者测试配置 ====================
  consumer:
    # 基础连接配置
    brokers: "kafka:9092"               # Kafka服务器地址
    group_id: "test-group"              # 测试消费者组ID
    client_id: "test-consumer"          # 测试消费者客户端ID
    
    # 消费配置（测试用）
    session_timeout_ms: 10000           # 测试用较短会话超时
    max_poll_interval_ms: 60000         # 测试用较短轮询间隔
    offset_reset: "earliest"            # 从最早偏移量开始
    enable_auto_commit: false           # 禁用自动提交以便测试手动提交
    auto_commit_interval_ms: 2000       # 测试用较短自动提交间隔
    
    # 线程池集成配置（测试用）
    thread_pool:
      enable_async_operations: true     # 启用异步操作进行测试
      core_size: 2                      # 测试用较小核心线程数
      max_size: 4                       # 测试用较小最大线程数
      keep_alive_ms: 30000             # 测试用较短保活时间
      queue_capacity: 100              # 测试用较小队列容量
      enable_monitoring: true          # 启用监控以便观察测试过程
      name_prefix: "test-consumer-"    # 测试线程名前缀

# ==================== 日志配置 ====================
logging:
  level: "DEBUG"                       # 测试时使用DEBUG级别
  enable_console: true                 # 启用控制台输出
  enable_file: true                    # 启用文件输出
  log_file_path: "logs/kafka_test.log" # 测试日志文件
  enable_async: false                  # 测试时禁用异步日志以确保日志完整性

# ==================== 测试配置 ====================
test:
  kafka:
    # 测试用的主题配置
    topics:
      - "test-topic"
      - "perf-topic"
      - "async-topic"
    
    # 性能测试配置
    performance:
      message_count: 1000              # 性能测试消息数量
      concurrent_producers: 3          # 并发生产者数量
      concurrent_consumers: 2          # 并发消费者数量
      batch_size: 10                   # 批量操作大小
    
    # 超时配置
    timeouts:
      producer_timeout_ms: 5000        # 生产者操作超时
      consumer_timeout_ms: 3000        # 消费者操作超时
      async_operation_timeout_s: 10    # 异步操作超时
      
    # 重试配置
    retries:
      max_attempts: 3                  # 最大重试次数
      retry_delay_ms: 1000            # 重试延迟时间
