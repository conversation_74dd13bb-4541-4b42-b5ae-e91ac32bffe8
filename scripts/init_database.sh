#!/bin/bash

# 数据库初始化脚本
# Database Initialization Script for Auth Service
# 版本: 1.0.0
# 作者: 29108
# 日期: 2025/7/21

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="3306"
DEFAULT_USER="root"
DEFAULT_PASSWORD=""
DEFAULT_DATABASE="game_microservices"

# SQL文件路径
SQL_DIR="${PROJECT_ROOT}/sql/auth_service"
CREATE_TABLES_SQL="${SQL_DIR}/01_create_tables.sql"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 打印横幅
print_banner() {
    echo -e "${BLUE}"
    cat << 'EOF'
    ╔══════════════════════════════════════════════════════════════╗
    ║                    数据库初始化工具                            ║
    ║                Database Initialization Tool                  ║
    ║                                                              ║
    ║  用于Auth Service的数据库表结构初始化                          ║
    ║  支持自动创建数据库和表结构                                    ║
    ║  包含完整的索引和约束定义                                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -H, --host HOST         MySQL主机地址 (默认: $DEFAULT_HOST)"
    echo "  -P, --port PORT         MySQL端口 (默认: $DEFAULT_PORT)"
    echo "  -u, --user USER         MySQL用户名 (默认: $DEFAULT_USER)"
    echo "  -p, --password PASS     MySQL密码"
    echo "  -d, --database DB       数据库名 (默认: $DEFAULT_DATABASE)"
    echo "  -f, --force             强制重新创建表（危险操作）"
    echo "  --check-only            仅检查表是否存在"
    echo "  --drop-database         删除整个数据库（极危险操作）"
    echo ""
    echo "示例:"
    echo "  $0                                           # 使用默认配置"
    echo "  $0 -H localhost -u root -p mypassword        # 指定连接参数"
    echo "  $0 --check-only                              # 仅检查表状态"
    echo "  $0 -f                                        # 强制重新创建表"
    echo ""
    echo "注意:"
    echo "  - 如果不指定密码，将提示输入"
    echo "  - 强制重新创建表会删除所有数据"
    echo "  - 建议在生产环境使用前先备份数据"
    echo ""
}

# 检查MySQL客户端
check_mysql_client() {
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装"
        log_error "请安装MySQL客户端: sudo apt-get install mysql-client"
        exit 1
    fi
    log_info "✓ MySQL客户端已安装"
}

# 检查SQL文件
check_sql_files() {
    if [[ ! -f "$CREATE_TABLES_SQL" ]]; then
        log_error "SQL文件不存在: $CREATE_TABLES_SQL"
        exit 1
    fi
    log_info "✓ SQL文件存在: $CREATE_TABLES_SQL"
}

# 测试数据库连接
test_connection() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    
    log_info "测试数据库连接..."
    
    if [[ -n "$password" ]]; then
        mysql -h"$host" -P"$port" -u"$user" -p"$password" -e "SELECT 1;" &> /dev/null
    else
        mysql -h"$host" -P"$port" -u"$user" -e "SELECT 1;" &> /dev/null
    fi
    
    if [[ $? -eq 0 ]]; then
        log_info "✓ 数据库连接成功"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 创建数据库
create_database() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local database=$5
    
    log_info "创建数据库: $database"
    
    local mysql_cmd="mysql -h$host -P$port -u$user"
    if [[ -n "$password" ]]; then
        mysql_cmd="$mysql_cmd -p$password"
    fi
    
    $mysql_cmd -e "CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    if [[ $? -eq 0 ]]; then
        log_info "✓ 数据库创建成功"
        return 0
    else
        log_error "数据库创建失败"
        return 1
    fi
}

# 检查表是否存在
check_tables() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local database=$5
    
    log_info "检查数据库表..."
    
    local mysql_cmd="mysql -h$host -P$port -u$user"
    if [[ -n "$password" ]]; then
        mysql_cmd="$mysql_cmd -p$password"
    fi
    
    # 定义必需的表
    local required_tables=(
        "users"
        "user_roles"
        "game_user_data"
        "game_servers"
        "user_sessions"
    )
    
    local existing_tables=()
    local missing_tables=()
    
    for table in "${required_tables[@]}"; do
        local count=$($mysql_cmd -D"$database" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$database' AND table_name = '$table';" -s -N 2>/dev/null || echo "0")
        
        if [[ "$count" -gt 0 ]]; then
            existing_tables+=("$table")
            log_info "✓ 表存在: $table"
        else
            missing_tables+=("$table")
            log_warn "✗ 表缺失: $table"
        fi
    done
    
    echo "existing_tables:${existing_tables[*]}"
    echo "missing_tables:${missing_tables[*]}"
}

# 执行SQL文件
execute_sql_file() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local database=$5
    local sql_file=$6
    
    log_info "执行SQL文件: $(basename "$sql_file")"
    
    local mysql_cmd="mysql -h$host -P$port -u$user"
    if [[ -n "$password" ]]; then
        mysql_cmd="$mysql_cmd -p$password"
    fi
    
    $mysql_cmd -D"$database" < "$sql_file"
    
    if [[ $? -eq 0 ]]; then
        log_info "✓ SQL文件执行成功"
        return 0
    else
        log_error "SQL文件执行失败"
        return 1
    fi
}

# 删除数据库
drop_database() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local database=$5
    
    log_warn "即将删除数据库: $database"
    read -p "确认删除数据库? 输入 'YES' 确认: " confirm
    
    if [[ "$confirm" != "YES" ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    local mysql_cmd="mysql -h$host -P$port -u$user"
    if [[ -n "$password" ]]; then
        mysql_cmd="$mysql_cmd -p$password"
    fi
    
    $mysql_cmd -e "DROP DATABASE IF EXISTS $database;"
    
    if [[ $? -eq 0 ]]; then
        log_info "✓ 数据库删除成功"
        return 0
    else
        log_error "数据库删除失败"
        return 1
    fi
}

# 主函数
main() {
    local host="$DEFAULT_HOST"
    local port="$DEFAULT_PORT"
    local user="$DEFAULT_USER"
    local password="$DEFAULT_PASSWORD"
    local database="$DEFAULT_DATABASE"
    local force_recreate=false
    local check_only=false
    local drop_db=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                host="$2"
                shift 2
                ;;
            -P|--port)
                port="$2"
                shift 2
                ;;
            -u|--user)
                user="$2"
                shift 2
                ;;
            -p|--password)
                password="$2"
                shift 2
                ;;
            -d|--database)
                database="$2"
                shift 2
                ;;
            -f|--force)
                force_recreate=true
                shift
                ;;
            --check-only)
                check_only=true
                shift
                ;;
            --drop-database)
                drop_db=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定密码，提示输入
    if [[ -z "$password" ]]; then
        read -s -p "请输入MySQL密码: " password
        echo
    fi
    
    # 打印横幅
    print_banner
    
    # 检查依赖
    check_mysql_client
    check_sql_files
    
    # 测试连接
    if ! test_connection "$host" "$port" "$user" "$password"; then
        exit 1
    fi
    
    # 删除数据库操作
    if [[ "$drop_db" == true ]]; then
        drop_database "$host" "$port" "$user" "$password" "$database"
        exit 0
    fi
    
    # 创建数据库
    if ! create_database "$host" "$port" "$user" "$password" "$database"; then
        exit 1
    fi
    
    # 检查表状态
    table_status=$(check_tables "$host" "$port" "$user" "$password" "$database")
    
    if [[ "$check_only" == true ]]; then
        log_info "表检查完成"
        exit 0
    fi
    
    # 执行SQL文件
    if ! execute_sql_file "$host" "$port" "$user" "$password" "$database" "$CREATE_TABLES_SQL"; then
        exit 1
    fi
    
    # 再次检查表状态
    log_info "验证表创建结果..."
    check_tables "$host" "$port" "$user" "$password" "$database" > /dev/null
    
    log_info "数据库初始化完成！"
    log_info "数据库: $database"
    log_info "主机: $host:$port"
}

# 执行主函数
main "$@"
