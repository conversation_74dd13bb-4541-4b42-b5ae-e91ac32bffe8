# 游戏微服务架构图

## 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        Client[游戏客户端]
        WebClient[Web客户端]
        MobileClient[移动客户端]
    end
    
    subgraph "网关层"
        Gateway[API网关]
        LoadBalancer[负载均衡器]
    end
    
    subgraph "核心服务层"
        UserService[用户服务]
        AuthService[认证服务]
        GameService[游戏服务]
        LeaderboardService[排行榜服务]
        ConfigService[配置服务]
    end
    
    subgraph "基础设施层"
        ServiceRegistry[服务注册中心]
        MessageQueue[消息队列 Kafka]
        Database[(MySQL集群)]
        Cache[(Redis集群)]
        Logger[日志系统]
    end
    
    Client --> Gateway
    WebClient --> Gateway
    MobileClient --> Gateway
    
    Gateway --> LoadBalancer
    LoadBalancer --> UserService
    LoadBalancer --> AuthService
    LoadBalancer --> GameService
    LoadBalancer --> LeaderboardService
    LoadBalancer --> ConfigService
    
    UserService --> Database
    UserService --> Cache
    UserService --> MessageQueue
    
    AuthService --> Database
    AuthService --> Cache
    
    GameService --> Database
    GameService --> Cache
    GameService --> MessageQueue
    
    LeaderboardService --> Database
    LeaderboardService --> Cache
    
    ConfigService --> Database
    
    UserService --> ServiceRegistry
    AuthService --> ServiceRegistry
    GameService --> ServiceRegistry
    LeaderboardService --> ServiceRegistry
    ConfigService --> ServiceRegistry
    
    UserService --> Logger
    AuthService --> Logger
    GameService --> Logger
    LeaderboardService --> Logger
    ConfigService --> Logger
```

## 线程池架构图

```mermaid
graph TB
    subgraph "应用层"
        WebServer[Web服务器]
        GameLogic[游戏逻辑处理]
        MessageHandler[消息处理器]
    end
    
    subgraph "线程池层"
        NetworkPool[网络IO线程池<br/>2-4线程]
        BusinessPool[业务逻辑线程池<br/>CPU核心数*2]
        DatabasePool[数据库线程池<br/>8-16线程]
        BackgroundPool[后台任务线程池<br/>2-4线程]
    end
    
    subgraph "任务调度层"
        TaskScheduler[任务调度器]
        TaskQueue[任务队列]
        TimerWheel[时间轮]
    end
    
    subgraph "底层资源"
        Database[(数据库)]
        Cache[(缓存)]
        MessageQueue[消息队列]
        FileSystem[文件系统]
    end
    
    WebServer --> NetworkPool
    GameLogic --> BusinessPool
    MessageHandler --> BusinessPool
    
    NetworkPool --> TaskQueue
    BusinessPool --> TaskQueue
    DatabasePool --> Database
    DatabasePool --> Cache
    BackgroundPool --> FileSystem
    BackgroundPool --> MessageQueue
    
    TaskScheduler --> TaskQueue
    TaskScheduler --> TimerWheel
    TaskQueue --> DatabasePool
    TaskQueue --> BackgroundPool
```

## 数据库架构图

```mermaid
graph TB
    subgraph "应用服务层"
        UserSvc[用户服务]
        AuthSvc[认证服务]
        GameSvc[游戏服务]
        LeaderSvc[排行榜服务]
    end
    
    subgraph "数据访问层"
        UserDAO[用户DAO]
        AuthDAO[认证DAO]
        GameDAO[游戏DAO]
        LeaderDAO[排行榜DAO]
    end
    
    subgraph "连接池层"
        MySQLPool[MySQL连接池<br/>初始:5 最大:50]
        RedisPool[Redis连接池<br/>初始:3 最大:20]
    end
    
    subgraph "数据存储层"
        UserDB[(用户数据库)]
        AuthDB[(认证数据库)]
        GameDB[(游戏数据库)]
        LeaderDB[(排行榜数据库)]
        
        UserCache[(用户缓存)]
        SessionCache[(会话缓存)]
        GameCache[(游戏缓存)]
        LeaderCache[(排行榜缓存)]
    end
    
    UserSvc --> UserDAO
    AuthSvc --> AuthDAO
    GameSvc --> GameDAO
    LeaderSvc --> LeaderDAO
    
    UserDAO --> MySQLPool
    AuthDAO --> MySQLPool
    GameDAO --> MySQLPool
    LeaderDAO --> MySQLPool
    
    UserDAO --> RedisPool
    AuthDAO --> RedisPool
    GameDAO --> RedisPool
    LeaderDAO --> RedisPool
    
    MySQLPool --> UserDB
    MySQLPool --> AuthDB
    MySQLPool --> GameDB
    MySQLPool --> LeaderDB
    
    RedisPool --> UserCache
    RedisPool --> SessionCache
    RedisPool --> GameCache
    RedisPool --> LeaderCache
```

## 消息流架构图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant User as 用户服务
    participant Game as 游戏服务
    participant Kafka as 消息队列
    participant DB as 数据库
    participant Cache as 缓存
    
    Client->>Gateway: 登录请求
    Gateway->>Auth: 验证凭据
    Auth->>DB: 查询用户信息
    DB-->>Auth: 返回用户数据
    Auth->>Cache: 创建会话
    Auth-->>Gateway: 返回JWT令牌
    Gateway-->>Client: 登录成功
    
    Client->>Gateway: 创建游戏房间
    Gateway->>Auth: 验证JWT令牌
    Auth->>Cache: 验证会话
    Auth-->>Gateway: 验证通过
    Gateway->>Game: 创建房间请求
    Game->>DB: 保存房间信息
    Game->>Kafka: 发布房间创建事件
    Game-->>Gateway: 返回房间信息
    Gateway-->>Client: 房间创建成功
    
    Kafka->>User: 房间创建事件
    User->>Cache: 更新用户状态
    
    Client->>Gateway: 加入游戏房间
    Gateway->>Game: 加入房间请求
    Game->>Cache: 检查房间状态
    Game->>DB: 更新房间信息
    Game->>Kafka: 发布玩家加入事件
    Game-->>Gateway: 加入成功
    Gateway-->>Client: WebSocket连接建立
```

## 部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx负载均衡器]
    end
    
    subgraph "Kubernetes集群"
        subgraph "网关命名空间"
            GW1[API网关-1]
            GW2[API网关-2]
        end
        
        subgraph "服务命名空间"
            US1[用户服务-1]
            US2[用户服务-2]
            AS1[认证服务-1]
            AS2[认证服务-2]
            GS1[游戏服务-1]
            GS2[游戏服务-2]
            LS1[排行榜服务-1]
            LS2[排行榜服务-2]
        end
        
        subgraph "基础设施命名空间"
            SR[服务注册中心]
            CS[配置服务]
        end
    end
    
    subgraph "数据层"
        subgraph "MySQL集群"
            MySQL1[(MySQL主节点)]
            MySQL2[(MySQL从节点1)]
            MySQL3[(MySQL从节点2)]
        end
        
        subgraph "Redis集群"
            Redis1[(Redis节点1)]
            Redis2[(Redis节点2)]
            Redis3[(Redis节点3)]
        end
        
        subgraph "Kafka集群"
            Kafka1[Kafka节点1]
            Kafka2[Kafka节点2]
            Kafka3[Kafka节点3]
        end
    end
    
    subgraph "监控层"
        Prometheus[Prometheus]
        Grafana[Grafana]
        ELK[ELK日志栈]
    end
    
    LB --> GW1
    LB --> GW2
    
    GW1 --> US1
    GW1 --> AS1
    GW1 --> GS1
    GW1 --> LS1
    
    GW2 --> US2
    GW2 --> AS2
    GW2 --> GS2
    GW2 --> LS2
    
    US1 --> MySQL1
    US2 --> MySQL2
    AS1 --> MySQL1
    AS2 --> MySQL3
    
    US1 --> Redis1
    US2 --> Redis2
    AS1 --> Redis1
    AS2 --> Redis3
    
    GS1 --> Kafka1
    GS2 --> Kafka2
    LS1 --> Kafka3
    
    US1 --> SR
    US2 --> SR
    AS1 --> SR
    AS2 --> SR
    GS1 --> SR
    GS2 --> SR
    LS1 --> SR
    LS2 --> SR
    
    Prometheus --> US1
    Prometheus --> US2
    Prometheus --> AS1
    Prometheus --> AS2
    Prometheus --> GS1
    Prometheus --> GS2
    
    Grafana --> Prometheus
    ELK --> US1
    ELK --> AS1
    ELK --> GS1
```

## 数据流图

```mermaid
flowchart TD
    subgraph "数据输入"
        UserInput[用户输入]
        GameEvents[游戏事件]
        SystemEvents[系统事件]
    end
    
    subgraph "数据处理"
        Validation[数据验证]
        BusinessLogic[业务逻辑处理]
        DataTransform[数据转换]
    end
    
    subgraph "数据存储"
        PrimaryDB[(主数据库)]
        Cache[(缓存层)]
        MessageQueue[消息队列]
        LogStorage[(日志存储)]
    end
    
    subgraph "数据输出"
        APIResponse[API响应]
        RealTimeUpdate[实时更新]
        Reports[报表数据]
        Notifications[通知消息]
    end
    
    UserInput --> Validation
    GameEvents --> Validation
    SystemEvents --> Validation
    
    Validation --> BusinessLogic
    BusinessLogic --> DataTransform
    
    DataTransform --> PrimaryDB
    DataTransform --> Cache
    DataTransform --> MessageQueue
    DataTransform --> LogStorage
    
    PrimaryDB --> APIResponse
    Cache --> RealTimeUpdate
    MessageQueue --> Notifications
    LogStorage --> Reports
    
    APIResponse --> UserInput
    RealTimeUpdate --> GameEvents
    Notifications --> SystemEvents
```

## 安全架构图

```mermaid
graph TB
    subgraph "外部访问"
        Internet[互联网]
        CDN[CDN]
    end
    
    subgraph "安全边界"
        WAF[Web应用防火墙]
        DDoS[DDoS防护]
        SSL[SSL终端]
    end
    
    subgraph "认证授权"
        OAuth[OAuth2.0]
        JWT[JWT令牌]
        RBAC[角色权限控制]
    end
    
    subgraph "网络安全"
        VPC[虚拟私有云]
        SecurityGroup[安全组]
        NetworkACL[网络ACL]
    end
    
    subgraph "数据安全"
        Encryption[数据加密]
        KeyManagement[密钥管理]
        DataMasking[数据脱敏]
    end
    
    subgraph "审计监控"
        AccessLog[访问日志]
        SecurityLog[安全日志]
        Monitoring[安全监控]
        AlertSystem[告警系统]
    end
    
    Internet --> CDN
    CDN --> WAF
    WAF --> DDoS
    DDoS --> SSL
    
    SSL --> OAuth
    OAuth --> JWT
    JWT --> RBAC
    
    RBAC --> VPC
    VPC --> SecurityGroup
    SecurityGroup --> NetworkACL
    
    NetworkACL --> Encryption
    Encryption --> KeyManagement
    KeyManagement --> DataMasking
    
    DataMasking --> AccessLog
    AccessLog --> SecurityLog
    SecurityLog --> Monitoring
    Monitoring --> AlertSystem
```

这些架构图展示了游戏微服务项目的各个层面：

1. **整体架构图**：展示了系统的总体结构和组件关系
2. **线程池架构图**：说明了线程池的分层设计和任务调度机制
3. **数据库架构图**：展示了数据访问层和连接池的设计
4. **消息流架构图**：描述了典型的用户操作流程
5. **部署架构图**：展示了在Kubernetes环境中的部署方案
6. **数据流图**：说明了数据在系统中的流转过程
7. **安全架构图**：展示了多层次的安全防护体系

这些图表可以帮助开发团队更好地理解系统架构，指导具体的实现工作。
