# InetAddress类使用指南

## 概述

`InetAddress`类是一个用于封装IPv4网络地址的C++类，提供了便捷的接口来处理IP地址和端口号。该类简化了网络编程中地址操作的复杂性，并提供了与socket系统调用的兼容性。

## 主要功能

- **地址创建**：支持多种方式创建网络地址对象
- **格式转换**：IP地址字符串与二进制格式之间的转换
- **地址验证**：验证IP地址格式的有效性
- **类型检查**：检查地址类型（回环、任意地址等）
- **Socket兼容**：与socket系统调用完全兼容
- **比较操作**：支持地址对象之间的比较

## 类接口说明

### 构造函数

```cpp
// 1. 使用端口号创建地址（可选择绑定类型）
InetAddress(uint16_t port = 0, bool loopbackOnly = false);

// 2. 使用IP地址字符串和端口号创建
InetAddress(const std::string& ip, uint16_t port);
```

### 地址信息获取

```cpp
std::string toIp() const;           // 获取IP地址字符串
std::string toIpPort() const;       // 获取"IP:端口"格式字符串
uint16_t port() const;              // 获取端口号
```

### 地址类型检查

```cpp
bool isLoopback() const;            // 是否为回环地址(127.0.0.1)
bool isAnyAddress() const;          // 是否为任意地址(0.0.0.0)
bool isValid() const;               // 是否为有效地址
```

### Socket接口

```cpp
const struct sockaddr* getSockAddr() const;        // 获取sockaddr指针
void setSockAddr(const struct sockaddr_in& addr);  // 设置sockaddr结构
static socklen_t getSockAddrSize();                // 获取结构大小
```

### 静态工具方法

```cpp
static bool isValidIpAddress(const std::string& ipStr);  // 验证IP地址格式
static std::string getLocalIpAddress();                  // 获取本机IP地址
```

## 使用示例

### 1. 创建服务器地址

```cpp
#include "common/network/inet_address.h"
using namespace network;

// 创建HTTP服务器地址（绑定到所有接口）
InetAddress httpServer(80, false);
std::cout << "HTTP服务器: " << httpServer.toIpPort() << std::endl;
// 输出: HTTP服务器: 0.0.0.0:80

// 创建本地测试服务器
InetAddress localServer(8080, true);
std::cout << "本地服务器: " << localServer.toIpPort() << std::endl;
// 输出: 本地服务器: 127.0.0.1:8080
```

### 2. 创建客户端连接地址

```cpp
// 连接到远程服务器
InetAddress remoteServer("*************", 3000);
std::cout << "远程服务器: " << remoteServer.toIpPort() << std::endl;
// 输出: 远程服务器: *************:3000

// 验证地址有效性
if (remoteServer.isValid()) {
    std::cout << "地址有效，可以连接" << std::endl;
}
```

### 3. 地址验证

```cpp
// 验证IP地址格式
std::vector<std::string> addresses = {
    "127.0.0.1",        // 有效
    "***********",      // 有效
    "256.1.1.1",        // 无效：超出范围
    "192.168.1"         // 无效：格式不完整
};

for (const auto& addr : addresses) {
    bool valid = InetAddress::isValidIpAddress(addr);
    std::cout << addr << " -> " << (valid ? "有效" : "无效") << std::endl;
}
```

### 4. Socket编程集成

```cpp
#include <sys/socket.h>

// 创建socket
int sockfd = socket(AF_INET, SOCK_STREAM, 0);

// 创建地址对象
InetAddress serverAddr("127.0.0.1", 8080);

// 绑定地址
if (bind(sockfd, serverAddr.getSockAddr(), InetAddress::getSockAddrSize()) == -1) {
    perror("bind failed");
    return -1;
}

// 开始监听
if (listen(sockfd, 5) == -1) {
    perror("listen failed");
    return -1;
}

std::cout << "服务器启动在: " << serverAddr.toIpPort() << std::endl;
```

### 5. 客户端连接处理

```cpp
// 接受客户端连接
struct sockaddr_in clientAddr;
socklen_t clientAddrLen = sizeof(clientAddr);
int clientfd = accept(sockfd, (struct sockaddr*)&clientAddr, &clientAddrLen);

if (clientfd != -1) {
    // 使用InetAddress封装客户端地址
    InetAddress client;
    client.setSockAddr(clientAddr);
    
    std::cout << "客户端连接: " << client.toIpPort() << std::endl;
    
    // 检查是否为本地连接
    if (client.isLoopback()) {
        std::cout << "本地客户端连接" << std::endl;
    }
}
```

### 6. 地址比较

```cpp
InetAddress addr1("*************", 8080);
InetAddress addr2("*************", 8080);
InetAddress addr3("*************", 8080);

if (addr1 == addr2) {
    std::cout << "地址1和地址2相同" << std::endl;
}

if (addr1 != addr3) {
    std::cout << "地址1和地址3不同" << std::endl;
}
```

## 错误处理

```cpp
try {
    // 尝试创建无效IP地址
    InetAddress invalidAddr("256.256.256.256", 8080);
} catch (const std::invalid_argument& e) {
    std::cerr << "IP地址格式错误: " << e.what() << std::endl;
} catch (const std::runtime_error& e) {
    std::cerr << "系统调用失败: " << e.what() << std::endl;
}
```

## 编译和测试

### 编译库

```bash
# 在项目根目录下
mkdir build && cd build
cmake ..
make network_lib
```

### 运行测试

```bash
# 启用测试编译
cmake -DBUILD_NETWORK_TESTS=ON ..
make
ctest
```

### 编译示例

```bash
# 编译使用示例
g++ -std=c++17 -I../include examples/inet_address_example.cpp -L./lib -lnetwork_lib -o inet_example
./inet_example
```

## 注意事项

1. **字节序转换**：类内部自动处理网络字节序和主机字节序的转换
2. **异常安全**：构造函数可能抛出异常，需要适当的错误处理
3. **线程安全**：类本身是线程安全的，但多线程修改同一对象需要外部同步
4. **IPv4限制**：当前版本仅支持IPv4，不支持IPv6
5. **平台兼容**：主要针对Linux/Unix平台设计

## 常见用途

- **网络服务器**：创建监听地址
- **客户端连接**：指定连接目标
- **服务发现**：地址注册和查找
- **负载均衡**：后端服务器地址管理
- **网络配置**：解析和验证配置文件中的地址信息
