# 微服务API接口文档

## 概述

本文档详细描述了微服务架构中所有可用的API接口，包括认证服务、API网关服务发现、游戏相关API等。所有接口都支持JSON格式的请求和响应。

## 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │───▶│   API网关       │───▶│   认证服务      │
│  (Frontend)     │    │  (API Gateway)  │    │ (Auth Service)  │
│  Port: 3000     │    │   Port: 8080    │    │   Port: 8008    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   其他微服务    │
                       │ (Other Services)│
                       └─────────────────┘
```

## 目录

1. [认证服务API (Auth Service)](#认证服务api)
2. [游戏相关API](#游戏相关api)
3. [API网关服务发现API](#api网关服务发现api)
4. [API网关管理API](#api网关管理api)
5. [错误码说明](#错误码说明)
6. [使用示例](#使用示例)

---

## 认证服务API

### 基础信息
- **直接访问地址**: `http://localhost:8008`
- **通过API网关访问**: `http://localhost:8080/api/v1/auth/*` (推荐)
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

### 1. 用户注册

**接口地址**: `POST /api/v1/auth/register`

**描述**: 注册新用户账户，创建用户凭据

**请求头**:
```http
Content-Type: application/json
```

**请求体**:
```json
{
  "username": "testuser",
  "password": "TestPass123!",
  "email": "<EMAIL>"
}
```

**请求参数说明**:
| 参数 | 类型 | 必填 | 长度限制 | 说明 |
|------|------|------|----------|------|
| username | string | 是 | 3-20字符 | 用户名，只能包含字母、数字、下划线 |
| password | string | 是 | 8-50字符 | 密码，必须包含字母和数字 |
| email | string | 是 | 5-100字符 | 有效的邮箱地址 |

**成功响应** (200):
```json
{
  "success": true,
  "message": "用户注册成功",
  "data": {
    "user_id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "created_at": "2025-07-25T10:30:00Z",
    "status": "active"
  },
  "timestamp": **********
}
```

**错误响应**:
```json
// 400 - 用户名已存在
{
  "success": false,
  "error_code": "USER_EXISTS",
  "error_message": "用户名已存在",
  "timestamp": **********
}

// 400 - 密码强度不足
{
  "success": false,
  "error_code": "WEAK_PASSWORD",
  "error_message": "密码必须包含至少8个字符，包括字母和数字",
  "timestamp": **********
}

// 400 - 邮箱格式错误
{
  "success": false,
  "error_code": "INVALID_EMAIL",
  "error_message": "邮箱格式不正确",
  "timestamp": **********
}
```

### 2. 用户登录

**接口地址**: `POST /api/v1/auth/login`

**描述**: 用户登录验证，获取访问令牌和刷新令牌

**请求头**:
```http
Content-Type: application/json
User-Agent: YourApp/1.0.0 (optional)
```

**请求体**:
```json
{
  "username": "testuser",
  "password": "TestPass123!",
  "remember_me": false
}
```

**请求参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| username | string | 是 | 用户名或邮箱地址 |
| password | string | 是 | 用户密码 |
| remember_me | boolean | 否 | 是否记住登录状态，默认false |

**成功响应** (200):
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.signature",
    "refresh_token": "refresh_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "refresh_expires_in": 604800,
    "user_info": {
      "user_id": 12345,
      "username": "testuser",
      "email": "<EMAIL>",
      "last_login": "2025-07-25T10:30:00Z",
      "login_count": 42,
      "status": "active"
    }
  },
  "timestamp": **********
}
```

**错误响应**:
```json
// 401 - 凭据错误
{
  "success": false,
  "error_code": "INVALID_CREDENTIALS",
  "error_message": "用户名或密码错误",
  "timestamp": **********
}

// 423 - 账户被锁定
{
  "success": false,
  "error_code": "ACCOUNT_LOCKED",
  "error_message": "账户已被锁定，请联系管理员",
  "timestamp": **********
}
```

### 3. 用户登出

**接口地址**: `POST /api/v1/auth/logout`

**描述**: 用户登出，使当前访问令牌和刷新令牌失效

**请求头**:
```http
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{
  "all_devices": false
}
```

**请求参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| all_devices | boolean | 否 | 是否登出所有设备，默认false |

**成功响应** (200):
```json
{
  "success": true,
  "message": "登出成功",
  "data": {
    "logged_out_devices": 1
  },
  "timestamp": **********
}
```

### 4. 刷新令牌

**接口地址**: `POST /api/v1/auth/refresh`

**描述**: 使用刷新令牌获取新的访问令牌

**请求头**:
```http
Content-Type: application/json
```

**请求体**:
```json
{
  "refresh_token": "refresh_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "令牌刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  },
  "timestamp": **********
}
```

**错误响应**:
```json
// 401 - 刷新令牌无效
{
  "success": false,
  "error_code": "INVALID_REFRESH_TOKEN",
  "error_message": "刷新令牌无效或已过期",
  "timestamp": **********
}
```

### 5. 验证令牌

**接口地址**: `POST /api/v1/auth/validate`

**描述**: 验证访问令牌的有效性，获取用户信息

**请求头**:
```http
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "令牌有效",
  "data": {
    "user_id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "issued_at": "2025-07-25T10:30:00Z",
    "expires_at": "2025-07-25T11:30:00Z",
    "remaining_seconds": 2847
  },
  "timestamp": **********
}
```

**错误响应**:
```json
// 401 - 令牌无效
{
  "success": false,
  "error_code": "INVALID_TOKEN",
  "error_message": "访问令牌无效",
  "timestamp": **********
}

// 401 - 令牌过期
{
  "success": false,
  "error_code": "TOKEN_EXPIRED",
  "error_message": "访问令牌已过期",
  "timestamp": **********
}
```

---

## 游戏相关API

### 6. 游戏登录

**接口地址**: `POST /api/v1/game/login`

**描述**: 用户登录到游戏服务器，获取游戏会话令牌

**请求头**:
```http
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**请求体**:
```json
{
  "game_type": "snake",
  "server_preference": "low_latency",
  "region": "asia",
  "client_version": "1.0.0"
}
```

**请求参数说明**:
| 参数 | 类型 | 必填 | 可选值 | 说明 |
|------|------|------|--------|------|
| game_type | string | 是 | snake, tetris, puzzle | 游戏类型 |
| server_preference | string | 否 | low_latency, high_performance, balanced | 服务器偏好 |
| region | string | 否 | asia, europe, america | 地区偏好 |
| client_version | string | 否 | - | 客户端版本号 |

**成功响应** (200):
```json
{
  "success": true,
  "message": "游戏登录成功",
  "data": {
    "server_info": {
      "server_id": "game-server-001",
      "name": "亚洲蛇形游戏服务器1",
      "host": "game1.example.com",
      "port": 9001,
      "game_type": "snake",
      "region": "asia",
      "max_players": 100,
      "current_players": 45,
      "ping": 25,
      "status": "online"
    },
    "session_token": "game_session_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "session_id": "sess_**********_12345",
    "expires_in": 7200,
    "game_config": {
      "max_score": 999999,
      "time_limit": 300,
      "difficulty": "normal"
    }
  },
  "timestamp": **********
}
```

**错误响应**:
```json
// 503 - 服务器已满
{
  "success": false,
  "error_code": "GAME_SERVER_FULL",
  "error_message": "所有游戏服务器已满，请稍后重试",
  "data": {
    "retry_after": 60,
    "queue_position": 15
  },
  "timestamp": **********
}

// 400 - 不支持的游戏类型
{
  "success": false,
  "error_code": "GAME_TYPE_NOT_SUPPORTED",
  "error_message": "不支持的游戏类型: unknown_game",
  "data": {
    "supported_types": ["snake", "tetris", "puzzle"]
  },
  "timestamp": **********
}
```

### 7. 获取游戏服务器列表

**接口地址**: `GET /api/v1/game/servers`

**描述**: 获取可用的游戏服务器列表（公开接口，无需认证）

**请求头**:
```http
Content-Type: application/json
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| game_type | string | 否 | 过滤特定游戏类型 |
| region | string | 否 | 过滤特定地区 |
| status | string | 否 | 过滤服务器状态 (online, maintenance) |
| sort_by | string | 否 | 排序字段 (ping, players, name) |
| limit | integer | 否 | 返回数量限制，默认50，最大100 |
| offset | integer | 否 | 分页偏移量，默认0 |

**请求示例**:
```
GET /api/v1/game/servers?game_type=snake&region=asia&status=online&sort_by=ping&limit=10
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "获取服务器列表成功",
  "data": {
    "servers": [
      {
        "server_id": "game-server-001",
        "name": "亚洲蛇形游戏服务器1",
        "host": "game1.example.com",
        "port": 9001,
        "game_type": "snake",
        "region": "asia",
        "status": "online",
        "max_players": 100,
        "current_players": 45,
        "ping": 25,
        "load_percentage": 45,
        "version": "1.2.3",
        "features": ["ranked", "casual", "tournament"],
        "last_updated": "2025-07-25T10:29:00Z"
      },
      {
        "server_id": "game-server-002",
        "name": "亚洲俄罗斯方块服务器1",
        "host": "game2.example.com",
        "port": 9002,
        "game_type": "tetris",
        "region": "asia",
        "status": "online",
        "max_players": 200,
        "current_players": 120,
        "ping": 30,
        "load_percentage": 60,
        "version": "2.1.0",
        "features": ["ranked", "casual"],
        "last_updated": "2025-07-25T10:28:00Z"
      }
    ],
    "pagination": {
      "total_count": 25,
      "current_page": 1,
      "total_pages": 3,
      "limit": 10,
      "offset": 0
    },
    "summary": {
      "total_servers": 25,
      "online_servers": 23,
      "maintenance_servers": 2,
      "total_players": 1250,
      "total_capacity": 2500
    }
  },
  "timestamp": **********
}
```

---

## API网关服务发现API

### 基础信息
- **服务地址**: `http://localhost:8080`
- **用途**: 微服务注册、发现和管理
- **认证**: 部分接口需要服务认证

### 8. 服务注册

**接口地址**: `POST /api/v1/services/register`

**描述**: 注册新的微服务实例（主要供内部服务使用）

**请求头**:
```http
Content-Type: application/json
User-Agent: ServiceName/1.0.0
Connection: close
```

**请求体**:
```json
{
  "service_name": "auth_service",
  "host": "0.0.0.0",
  "port": 8008,
  "service_version": "1.0.0",
  "health_check_endpoint": "/health",
  "weight": 100,
  "tags": ["auth", "user-management"],
  "metadata": {
    "environment": "production",
    "datacenter": "dc1"
  },
  "endpoints": [
    "/api/v1/auth/register",
    "/api/v1/auth/login",
    "/api/v1/auth/logout",
    "/api/v1/auth/refresh",
    "/api/v1/auth/validate",
    "/api/v1/game/login",
    "/api/v1/game/servers"
  ]
}
```

**请求参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| service_name | string | 是 | 服务名称，唯一标识 |
| host | string | 是 | 服务主机地址 |
| port | integer | 是 | 服务端口号 |
| service_version | string | 是 | 服务版本号 |
| health_check_endpoint | string | 是 | 健康检查端点 |
| weight | integer | 否 | 负载均衡权重，默认100 |
| tags | array | 否 | 服务标签 |
| metadata | object | 否 | 服务元数据 |
| endpoints | array | 是 | 服务提供的端点列表 |

**成功响应** (200):
```json
{
  "success": true,
  "message": "服务注册成功",
  "data": {
    "service_id": "auth_service_0.0.0.0_8008",
    "registered_at": "2025-07-25T10:30:00Z"
  },
  "timestamp": **********
}
```

### 9. 服务注销

**接口地址**: `DELETE /api/v1/services/deregister`

**描述**: 注销微服务实例

**请求头**:
```http
Content-Type: application/json
```

**请求体**:
```json
{
  "service_name": "auth_service",
  "host": "0.0.0.0",
  "port": 8008
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "服务注销成功",
  "data": {
    "deregistered_at": "2025-07-25T10:35:00Z"
  },
  "timestamp": **********
}
```

### 10. 服务心跳

**接口地址**: `POST /api/v1/services/heartbeat`

**描述**: 发送服务心跳，保持服务活跃状态

**请求头**:
```http
Content-Type: application/json
```

**请求体**:
```json
{
  "service_name": "auth_service",
  "host": "0.0.0.0",
  "port": 8008,
  "status": "healthy",
  "load": 45.5,
  "memory_usage": 67.2
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "心跳更新成功",
  "data": {
    "next_heartbeat_in": 30
  },
  "timestamp": **********
}
```

### 11. 获取服务列表

**接口地址**: `GET /api/v1/services`

**描述**: 获取所有已注册的服务列表

**请求头**:
```http
Content-Type: application/json
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| service_name | string | 否 | 过滤特定服务 |
| healthy_only | boolean | 否 | 只返回健康的服务，默认false |
| tag | string | 否 | 按标签过滤 |
| format | string | 否 | 响应格式 (json, yaml)，默认json |

**请求示例**:
```
GET /api/v1/services?healthy_only=true&tag=auth
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "服务列表获取成功",
  "data": {
    "services": [
      {
        "service_name": "auth_service",
        "service_version": "1.0.0",
        "instances": [
          {
            "instance_id": "auth_service_0.0.0.0_8008",
            "host": "0.0.0.0",
            "port": 8008,
            "healthy": true,
            "weight": 100,
            "last_heartbeat": "2025-07-25T10:30:00Z",
            "registered_at": "2025-07-25T09:00:00Z",
            "status": "healthy",
            "load": 45.5,
            "memory_usage": 67.2,
            "tags": ["auth", "user-management"],
            "endpoints": [
              "/api/v1/auth/register",
              "/api/v1/auth/login"
            ]
          }
        ]
      }
    ],
    "summary": {
      "total_services": 1,
      "total_instances": 1,
      "healthy_instances": 1,
      "unhealthy_instances": 0
    }
  },
  "timestamp": **********
}
```

### 12. 获取服务统计

**接口地址**: `GET /api/v1/services/stats`

**描述**: 获取服务统计信息和性能指标

**请求头**:
```http
Content-Type: application/json
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| period | string | 否 | 统计周期 (1h, 24h, 7d)，默认1h |
| service_name | string | 否 | 特定服务的统计 |

**成功响应** (200):
```json
{
  "success": true,
  "message": "统计信息获取成功",
  "data": {
    "overview": {
      "total_services": 3,
      "total_instances": 5,
      "healthy_instances": 4,
      "unhealthy_instances": 1,
      "average_load": 52.3,
      "average_memory_usage": 68.7
    },
    "services": {
      "auth_service_total": 2,
      "auth_service_healthy": 2,
      "user_service_total": 2,
      "user_service_healthy": 1,
      "game_service_total": 1,
      "game_service_healthy": 1
    },
    "performance": {
      "total_requests_1h": 15420,
      "average_response_time_ms": 45.2,
      "error_rate_percentage": 0.12,
      "throughput_rps": 4.28
    },
    "health_checks": {
      "total_checks": 1800,
      "successful_checks": 1798,
      "failed_checks": 2,
      "success_rate": 99.89
    }
  },
  "timestamp": **********
}
```

---

## API网关管理API

### 13. 健康检查

**接口地址**: `GET /health`

**描述**: 检查API网关的健康状态和组件状态

**请求头**: 无特殊要求

**成功响应** (200):
```json
{
  "status": "UP",
  "message": "OK",
  "timestamp": **********,
  "version": "1.0.0",
  "uptime_seconds": 86400,
  "components": {
    "http_server": "UP",
    "route_manager": "UP",
    "load_balancer": "UP",
    "rate_limiter": "UP",
    "circuit_breaker": "UP",
    "service_discovery": "UP"
  },
  "system_info": {
    "cpu_usage": 25.5,
    "memory_usage": 512.3,
    "disk_usage": 45.2,
    "network_connections": 150
  },
  "async_response": true,
  "response_method": "async"
}
```

**错误响应** (503):
```json
{
  "status": "DOWN",
  "message": "HTTP server not running",
  "timestamp": **********,
  "version": "1.0.0",
  "components": {
    "http_server": "DOWN",
    "route_manager": "UP",
    "load_balancer": "UP",
    "rate_limiter": "UP",
    "circuit_breaker": "UP"
  },
  "errors": [
    "HTTP server failed to start on port 8080"
  ]
}
```

### 14. 获取指标信息

**接口地址**: `GET /metrics`

**描述**: 获取API网关的详细性能指标（Prometheus格式兼容）

**请求头**: 
```http
Accept: application/json
# 或者
Accept: text/plain (Prometheus格式)
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| format | string | 否 | 输出格式 (json, prometheus)，默认json |
| period | string | 否 | 统计周期 (5m, 1h, 24h)，默认5m |

**成功响应** (200) - JSON格式:
```json
{
  "timestamp": **********,
  "uptime_seconds": 86400,
  "requests": {
    "total_requests": 125000,
    "total_responses": 124800,
    "requests_per_second": 1.45,
    "active_connections": 25,
    "peak_connections": 150
  },
  "response_times": {
    "average_ms": 45.2,
    "p50_ms": 35.0,
    "p95_ms": 120.0,
    "p99_ms": 250.0,
    "max_ms": 1500.0
  },
  "errors": {
    "total_errors": 200,
    "error_rate": 0.16,
    "timeout_count": 15,
    "connection_errors": 5,
    "http_4xx": 150,
    "http_5xx": 45
  },
  "routes": {
    "total_routes": 15,
    "active_routes": 12,
    "route_hits": {
      "/api/v1/auth/login": 45000,
      "/api/v1/auth/validate": 35000,
      "/api/v1/game/servers": 25000
    }
  },
  "load_balancing": {
    "total_backend_requests": 124500,
    "backend_errors": 45,
    "average_backend_response_time_ms": 38.5
  },
  "system": {
    "cpu_usage_percent": 25.5,
    "memory_usage_mb": 512.3,
    "goroutines": 45,
    "gc_pause_ms": 2.3
  }
}
```

**成功响应** (200) - Prometheus格式:
```
# HELP api_gateway_requests_total Total number of requests
# TYPE api_gateway_requests_total counter
api_gateway_requests_total{method="GET",status="200"} 75000
api_gateway_requests_total{method="POST",status="200"} 45000
api_gateway_requests_total{method="POST",status="400"} 150

# HELP api_gateway_request_duration_seconds Request duration in seconds
# TYPE api_gateway_request_duration_seconds histogram
api_gateway_request_duration_seconds_bucket{le="0.1"} 50000
api_gateway_request_duration_seconds_bucket{le="0.5"} 120000
api_gateway_request_duration_seconds_bucket{le="1.0"} 124500
api_gateway_request_duration_seconds_bucket{le="+Inf"} 125000
```

---

## 错误码说明

### HTTP状态码映射
| HTTP状态码 | 说明 | 常见场景 |
|-----------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数验证失败、JSON格式错误 |
| 401 | 未授权 | 令牌无效、未提供认证信息 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 接口不存在、用户不存在 |
| 409 | 冲突 | 资源已存在 |
| 429 | 请求过多 | 触发限流 |
| 500 | 服务器错误 | 内部错误 |
| 502 | 网关错误 | 后端服务不可用 |
| 503 | 服务不可用 | 服务维护、过载 |

### 通用错误码
| 错误码 | HTTP状态码 | 说明 |
|--------|-----------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| INVALID_JSON | 400 | JSON格式错误 |
| MISSING_FIELDS | 400 | 缺少必需字段 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 禁止访问 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| RATE_LIMITED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

### 认证相关错误码
| 错误码 | HTTP状态码 | 说明 |
|--------|-----------|------|
| INVALID_CREDENTIALS | 401 | 用户名或密码错误 |
| TOKEN_EXPIRED | 401 | 访问令牌已过期 |
| INVALID_TOKEN | 401 | 无效的访问令牌 |
| INVALID_REFRESH_TOKEN | 401 | 无效的刷新令牌 |
| USER_EXISTS | 400 | 用户已存在 |
| WEAK_PASSWORD | 400 | 密码强度不足 |
| INVALID_EMAIL | 400 | 邮箱格式错误 |
| ACCOUNT_LOCKED | 423 | 账户被锁定 |
| ACCOUNT_DISABLED | 403 | 账户被禁用 |

### 游戏相关错误码
| 错误码 | HTTP状态码 | 说明 |
|--------|-----------|------|
| GAME_SERVER_FULL | 503 | 游戏服务器已满 |
| GAME_TYPE_NOT_SUPPORTED | 400 | 不支持的游戏类型 |
| SERVER_UNAVAILABLE | 503 | 服务器不可用 |
| GAME_SESSION_EXPIRED | 401 | 游戏会话已过期 |
| INVALID_GAME_TOKEN | 401 | 无效的游戏令牌 |

### 服务发现相关错误码
| 错误码 | HTTP状态码 | 说明 |
|--------|-----------|------|
| SERVICE_NOT_FOUND | 404 | 服务不存在 |
| SERVICE_ALREADY_EXISTS | 409 | 服务已存在 |
| HEARTBEAT_FAILED | 400 | 心跳更新失败 |
| INVALID_SERVICE_CONFIG | 400 | 无效的服务配置 |

---

## 使用示例

### 前端完整登录流程

```javascript
class AuthService {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
    this.token = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  // 用户注册
  async register(username, password, email) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username,
          password,
          email
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error_message);
      }

      return data.data;
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  }

  // 用户登录
  async login(username, password, rememberMe = false) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username,
          password,
          remember_me: rememberMe
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error_message);
      }

      // 保存令牌
      this.token = data.data.token;
      this.refreshToken = data.data.refresh_token;
      
      localStorage.setItem('access_token', this.token);
      localStorage.setItem('refresh_token', this.refreshToken);
      
      return data.data;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  // 刷新令牌
  async refreshAccessToken() {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token: this.refreshToken
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        // 刷新失败，清除本地令牌
        this.logout();
        throw new Error(data.error_message);
      }

      // 更新访问令牌
      this.token = data.data.token;
      localStorage.setItem('access_token', this.token);
      
      return data.data;
    } catch (error) {
      console.error('刷新令牌失败:', error);
      throw error;
    }
  }

  // 带自动重试的API请求
  async apiRequest(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { 'Authorization': `Bearer ${this.token}` })
      }
    };

    const mergedOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    };

    try {
      let response = await fetch(url, mergedOptions);
      
      // 如果令牌过期，尝试刷新
      if (response.status === 401 && this.refreshToken) {
        await this.refreshAccessToken();
        
        // 更新请求头中的令牌
        mergedOptions.headers['Authorization'] = `Bearer ${this.token}`;
        response = await fetch(url, mergedOptions);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error_message);
      }

      return data;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  // 游戏登录
  async gameLogin(gameType, serverPreference = 'balanced') {
    return await this.apiRequest(`${this.baseUrl}/api/v1/game/login`, {
      method: 'POST',
      body: JSON.stringify({
        game_type: gameType,
        server_preference: serverPreference
      })
    });
  }

  // 用户登出
  async logout(allDevices = false) {
    try {
      if (this.token) {
        await this.apiRequest(`${this.baseUrl}/api/v1/auth/logout`, {
          method: 'POST',
          body: JSON.stringify({
            all_devices: allDevices
          })
        });
      }
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      // 清除本地存储
      this.token = null;
      this.refreshToken = null;
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  // 检查登录状态
  isLoggedIn() {
    return !!this.token;
  }
}

// 使用示例
const auth = new AuthService();

// 登录
try {
  const loginResult = await auth.login('testuser', 'TestPass123!');
  console.log('登录成功:', loginResult.user_info);
  
  // 游戏登录
  const gameResult = await auth.gameLogin('snake', 'low_latency');
  console.log('游戏登录成功:', gameResult.data.server_info);
  
} catch (error) {
  console.error('操作失败:', error.message);
}
```

### 获取游戏服务器列表示例

```javascript
class GameService {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
  }

  // 获取游戏服务器列表
  async getGameServers(filters = {}) {
    const params = new URLSearchParams();
    
    if (filters.gameType) params.append('game_type', filters.gameType);
    if (filters.region) params.append('region', filters.region);
    if (filters.status) params.append('status', filters.status);
    if (filters.sortBy) params.append('sort_by', filters.sortBy);
    if (filters.limit) params.append('limit', filters.limit);
    if (filters.offset) params.append('offset', filters.offset);

    try {
      const response = await fetch(`${this.baseUrl}/api/v1/game/servers?${params}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error_message);
      }

      return data.data;
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      throw error;
    }
  }

  // 按延迟排序获取最佳服务器
  async getBestServers(gameType, region = 'asia', limit = 5) {
    return await this.getGameServers({
      gameType,
      region,
      status: 'online',
      sortBy: 'ping',
      limit
    });
  }
}

// 使用示例
const gameService = new GameService();

// 获取蛇形游戏的最佳服务器
const bestServers = await gameService.getBestServers('snake', 'asia', 3);
console.log('推荐服务器:', bestServers.servers);
```

### 服务监控示例

```javascript
class MonitoringService {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
  }

  // 获取系统健康状态
  async getHealthStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return await response.json();
    } catch (error) {
      console.error('获取健康状态失败:', error);
      return { status: 'DOWN', message: 'Connection failed' };
    }
  }

  // 获取服务统计
  async getServiceStats() {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/services/stats`);
      const data = await response.json();
      return data.success ? data.data : null;
    } catch (error) {
      console.error('获取服务统计失败:', error);
      return null;
    }
  }

  // 获取性能指标
  async getMetrics() {
    try {
      const response = await fetch(`${this.baseUrl}/metrics`);
      return await response.json();
    } catch (error) {
      console.error('获取指标失败:', error);
      return null;
    }
  }

  // 实时监控
  startMonitoring(interval = 30000) {
    return setInterval(async () => {
      const health = await this.getHealthStatus();
      const stats = await this.getServiceStats();
      
      console.log('系统状态:', health.status);
      console.log('服务统计:', stats?.overview);
      
      // 触发状态更新事件
      window.dispatchEvent(new CustomEvent('systemStatus', {
        detail: { health, stats }
      }));
    }, interval);
  }
}

// 使用示例
const monitoring = new MonitoringService();

// 开始监控
const monitoringId = monitoring.startMonitoring(10000); // 每10秒检查一次

// 监听状态更新
window.addEventListener('systemStatus', (event) => {
  const { health, stats } = event.detail;
  
  // 更新UI显示
  updateHealthIndicator(health.status);
  updateServiceStats(stats);
});

// 停止监控
// clearInterval(monitoringId);
```

---

## 注意事项

### 1. 认证和授权
- **JWT令牌**: 访问令牌有效期为1小时，刷新令牌有效期为7天
- **令牌刷新**: 建议在令牌过期前5分钟自动刷新
- **权限检查**: 某些接口需要特定权限，请检查用户角色

### 2. 请求限制
- **频率限制**: 每个IP每分钟最多100个请求
- **并发限制**: 每个用户最多10个并发连接
- **重试策略**: 建议使用指数退避算法进行重试

### 3. 错误处理
- **统一格式**: 所有API都遵循统一的错误响应格式
- **错误码**: 使用标准化的错误码便于前端处理
- **日志记录**: 重要操作会记录详细日志

### 4. 性能优化
- **缓存策略**: 游戏服务器列表等数据建议缓存5分钟
- **连接复用**: 使用HTTP/1.1的Keep-Alive或HTTP/2
- **压缩**: 支持gzip压缩，减少传输数据量

### 5. 安全考虑
- **HTTPS**: 生产环境必须使用HTTPS
- **CORS**: 已配置跨域支持，但建议限制允许的域名
- **输入验证**: 所有输入都会进行严格验证
- **SQL注入**: 使用参数化查询防止SQL注入

### 6. 监控和调试
- **请求ID**: 每个请求都有唯一ID，便于追踪
- **响应时间**: 监控API响应时间，及时发现性能问题
- **错误率**: 监控错误率，确保服务稳定性

---

## 附录

### A. 环境配置

#### 开发环境
```bash
# API网关
API_GATEWAY_URL=http://localhost:8080

# 认证服务
AUTH_SERVICE_URL=http://localhost:8008

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=game_microservices

REDIS_HOST=localhost
REDIS_PORT=6379
```

#### 生产环境
```bash
# API网关
API_GATEWAY_URL=https://api.yourdomain.com

# 认证服务
AUTH_SERVICE_URL=https://auth.yourdomain.com

# 负载均衡
LOAD_BALANCER_STRATEGY=round_robin
HEALTH_CHECK_INTERVAL=30s
```

### B. SDK和工具

#### Qt/C++ SDK
```cpp
// apiclient.h - Qt API客户端
class ApiClient : public QObject {
    Q_OBJECT
public:
    struct ApiResponse {
        bool success = false;
        int statusCode = 0;
        QJsonObject data;
        QString errorMessage;
        QString errorCode;
    };

    explicit ApiClient(const QString &baseUrl, QObject *parent = nullptr);

    void get(const QString &endpoint, const QJsonObject &params = QJsonObject{});
    void post(const QString &endpoint, const QJsonObject &data);
    void setToken(const QString &token);
    bool isAuthenticated() const;

signals:
    void requestFinished(const ApiResponse &response);
    void authenticationRequired();
};

// authservice.h - 认证服务
class AuthService : public QObject {
    Q_OBJECT
public:
    struct UserInfo {
        int userId = 0;
        QString username;
        QString email;
        QString status;
    };

    void login(const QString &username, const QString &password, bool rememberMe = false);
    void registerUser(const QString &username, const QString &password, const QString &email);
    bool isAuthenticated() const;

signals:
    void loginSucceeded(const UserInfo &userInfo);
    void loginFailed(const QString &error);
};

// 使用示例
AuthService *auth = new AuthService(this);
connect(auth, &AuthService::loginSucceeded, [](const AuthService::UserInfo &user) {
    qDebug() << "登录成功:" << user.username;
});
auth->login("testuser", "TestPass123!");
```

#### JavaScript/TypeScript SDK
```typescript
// 类型定义
interface LoginRequest {
  username: string;
  password: string;
  remember_me?: boolean;
}

interface LoginResponse {
  success: boolean;
  data: {
    token: string;
    refresh_token: string;
    expires_in: number;
    user_info: UserInfo;
  };
}

interface UserInfo {
  user_id: number;
  username: string;
  email: string;
  last_login: string;
  status: 'active' | 'inactive' | 'locked';
}

// API客户端
class GameMicroservicesClient {
  private baseUrl: string;
  private token?: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async login(request: LoginRequest): Promise<LoginResponse> {
    // 实现登录逻辑
  }

  async getGameServers(gameType?: string): Promise<GameServer[]> {
    // 实现获取游戏服务器逻辑
  }
}
```

#### Python SDK
```python
import requests
from typing import Optional, Dict, Any

class GameMicroservicesClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.token: Optional[str] = None
        self.session = requests.Session()

    def login(self, username: str, password: str, remember_me: bool = False) -> Dict[str, Any]:
        """用户登录"""
        response = self.session.post(
            f"{self.base_url}/api/v1/auth/login",
            json={
                "username": username,
                "password": password,
                "remember_me": remember_me
            }
        )
        data = response.json()
        if data.get("success"):
            self.token = data["data"]["token"]
            self.session.headers.update({
                "Authorization": f"Bearer {self.token}"
            })
        return data

    def get_game_servers(self, game_type: Optional[str] = None) -> Dict[str, Any]:
        """获取游戏服务器列表"""
        params = {}
        if game_type:
            params["game_type"] = game_type

        response = self.session.get(
            f"{self.base_url}/api/v1/game/servers",
            params=params
        )
        return response.json()
```

### C. 测试用例

#### Postman集合
```json
{
  "info": {
    "name": "Game Microservices API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080"
    },
    {
      "key": "token",
      "value": ""
    }
  ],
  "item": [
    {
      "name": "Auth",
      "item": [
        {
          "name": "Register",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"TestPass123!\",\n  \"email\": \"<EMAIL>\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/v1/auth/register",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "auth", "register"]
            }
          }
        },
        {
          "name": "Login",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"TestPass123!\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/v1/auth/login",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "auth", "login"]
            }
          },
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "if (pm.response.code === 200) {",
                  "    const response = pm.response.json();",
                  "    if (response.success) {",
                  "        pm.collectionVariables.set('token', response.data.token);",
                  "    }",
                  "}"
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}
```

#### cURL测试脚本
```bash
#!/bin/bash

# 设置基础URL
BASE_URL="http://localhost:8080"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "=== 微服务API测试 ==="

# 测试健康检查
echo -e "\n${GREEN}1. 测试健康检查${NC}"
curl -s "$BASE_URL/health" | jq '.'

# 测试用户注册
echo -e "\n${GREEN}2. 测试用户注册${NC}"
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser_'$(date +%s)'",
    "password": "TestPass123!",
    "email": "test_'$(date +%s)'@example.com"
  }')
echo "$REGISTER_RESPONSE" | jq '.'

# 测试用户登录
echo -e "\n${GREEN}3. 测试用户登录${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "TestPass123!"
  }')
echo "$LOGIN_RESPONSE" | jq '.'

# 提取token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')

if [ -n "$TOKEN" ]; then
  echo -e "\n${GREEN}4. 测试游戏服务器列表${NC}"
  curl -s "$BASE_URL/api/v1/game/servers" | jq '.'

  echo -e "\n${GREEN}5. 测试游戏登录${NC}"
  curl -s -X POST "$BASE_URL/api/v1/game/login" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
      "game_type": "snake",
      "server_preference": "low_latency"
    }' | jq '.'

  echo -e "\n${GREEN}6. 测试服务统计${NC}"
  curl -s "$BASE_URL/api/v1/services/stats" | jq '.'
else
  echo -e "\n${RED}登录失败，跳过需要认证的测试${NC}"
fi

echo -e "\n=== 测试完成 ==="
```

### D. 故障排除

#### 常见问题

**1. 连接被拒绝**
```bash
# 检查服务是否运行
curl -v http://localhost:8080/health

# 检查端口是否被占用
netstat -tlnp | grep 8080
lsof -i :8080
```

**2. 认证失败**
```bash
# 检查token格式
echo "YOUR_TOKEN" | base64 -d

# 验证token
curl -X POST http://localhost:8080/api/v1/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**3. 服务发现问题**
```bash
# 检查服务注册状态
curl http://localhost:8080/api/v1/services

# 检查服务健康状态
curl http://localhost:8080/api/v1/services/stats
```

#### 日志分析

**API网关日志**
```bash
# 查看实时日志
tail -f logs/api_gateway.log

# 搜索错误
grep -i error logs/api_gateway.log

# 分析请求模式
grep "HTTP.*200" logs/api_gateway.log | wc -l
```

**认证服务日志**
```bash
# 查看认证相关日志
tail -f logs/auth_service.log | grep -i auth

# 分析登录失败
grep "登录失败" logs/auth_service.log
```

### E. 版本历史

#### v1.0.0 (2025-07-25)
- 初始版本发布
- 基础认证功能
- 游戏服务器管理
- API网关路由

#### v1.1.0 (计划中)
- 添加用户角色管理
- 支持OAuth2.0
- 增强监控指标
- 支持WebSocket连接

#### v1.2.0 (计划中)
- 多租户支持
- 高级负载均衡策略
- 分布式链路追踪
- 自动扩缩容

---

## 联系信息

- **开发团队**: 29108
- **文档版本**: 1.0.0
- **最后更新**: 2025-07-25
- **支持邮箱**: <EMAIL>
- **GitHub**: https://github.com/yourusername/game-microservices

---

这份文档提供了完整的API接口说明，包括详细的请求/响应示例、错误处理、使用示例和故障排除指南。可以作为前端开发的详细参考手册，也可以用于API测试和集成开发。建议根据实际需求调整和扩展相关内容。
