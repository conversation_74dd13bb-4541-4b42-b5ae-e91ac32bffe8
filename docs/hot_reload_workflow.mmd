flowchart TD
    %% 配置热更新详细工作流程
    
    Start([配置文件变更]) --> FileWatch{ConfigManager<br/>文件监控}
    
    FileWatch -->|检测到变更| LoadConfig[重新加载配置文件]
    LoadConfig --> ParseConfig[解析新配置]
    ParseConfig --> ValidateConfig{配置验证}
    
    ValidateConfig -->|验证失败| LogError[记录错误日志]
    LogError --> End([结束])
    
    ValidateConfig -->|验证成功| TriggerCallback[触发配置变更回调]
    TriggerCallback --> EventLoopThread{在EventLoop<br/>IO线程中处理}
    
    EventLoopThread --> AnalyzeChanges[分析配置变更]
    AnalyzeChanges --> SafetyCheck{安全性检查}
    
    SafetyCheck -->|不安全变更| RejectChange[拒绝变更]
    RejectChange --> LogWarning[记录警告日志]
    LogWarning --> End
    
    SafetyCheck -->|安全变更| BackupConfig[备份当前配置]
    BackupConfig --> UpdateConfig[更新配置对象]
    UpdateConfig --> ApplyChanges[按优先级应用变更]
    
    ApplyChanges --> MonitoringChanges{监控配置变更?}
    MonitoringChanges -->|是| ApplyMonitoring[应用监控配置]
    ApplyMonitoring --> SocketChanges{Socket配置变更?}
    MonitoringChanges -->|否| SocketChanges
    
    SocketChanges -->|是| ApplySocket[应用Socket配置]
    ApplySocket --> GetChannels[获取所有Channel]
    GetChannels --> UpdateSockets[更新Socket选项]
    UpdateSockets --> ThreadPoolChanges{线程池配置变更?}
    SocketChanges -->|否| ThreadPoolChanges
    
    ThreadPoolChanges -->|是| CheckThreadPool{线程池启用状态}
    CheckThreadPool -->|启用| CreateThreadPool[创建/调整线程池]
    CheckThreadPool -->|禁用| DisableThreadPool[禁用线程池]
    CreateThreadPool --> EpollChanges{Epoll配置变更?}
    DisableThreadPool --> EpollChanges
    ThreadPoolChanges -->|否| EpollChanges
    
    EpollChanges -->|是| CheckEventLoop{EventLoop运行状态}
    CheckEventLoop -->|运行中| LogPending[记录待更新配置]
    CheckEventLoop -->|已停止| SaveChannelState[保存Channel状态]
    SaveChannelState --> RecreateEpoll[重建Epoll实例]
    RecreateEpoll --> RestoreChannels[恢复Channel状态]
    RestoreChannels --> NotifyComplete[通知配置更新完成]
    LogPending --> NotifyComplete
    EpollChanges -->|否| NotifyComplete
    
    NotifyComplete --> CalculateHash[计算配置哈希]
    CalculateHash --> LogSuccess[记录成功日志]
    LogSuccess --> TriggerCallbacks[触发外部回调]
    TriggerCallbacks --> End
    
    %% 错误处理分支
    UpdateConfig -->|应用失败| RollbackConfig[回滚到备份配置]
    ApplySocket -->|应用失败| RollbackConfig
    CreateThreadPool -->|创建失败| RollbackConfig
    RecreateEpoll -->|重建失败| RollbackConfig
    
    RollbackConfig --> LogRollback[记录回滚日志]
    LogRollback --> NotifyFailure[通知配置更新失败]
    NotifyFailure --> End
    
    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef process fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class Start,End startEnd
    class LoadConfig,ParseConfig,TriggerCallback,AnalyzeChanges,BackupConfig,UpdateConfig,ApplyChanges,ApplyMonitoring,ApplySocket,GetChannels,UpdateSockets,CreateThreadPool,DisableThreadPool,SaveChannelState,RecreateEpoll,RestoreChannels,NotifyComplete,CalculateHash,TriggerCallbacks process
    class FileWatch,ValidateConfig,EventLoopThread,SafetyCheck,MonitoringChanges,SocketChanges,ThreadPoolChanges,CheckThreadPool,EpollChanges,CheckEventLoop decision
    class LogError,RejectChange,LogWarning,RollbackConfig,LogRollback,NotifyFailure error
    class LogSuccess success
