# 前端集成指南

## 概述

本指南专门为前端开发者提供与微服务API的集成方案，包括认证流程、状态管理、错误处理等最佳实践。支持多种前端技术栈：

- **Qt/C++**: 桌面应用开发
- **JavaScript/TypeScript**: Web应用开发
- **React**: 现代Web框架
- **Vue.js**: 渐进式Web框架

## 快速开始

### 1. 环境配置

#### Qt/C++配置
```cpp
// config.h
#ifndef CONFIG_H
#define CONFIG_H

#include <QString>

class Config {
public:
    static QString getApiBaseUrl() {
#ifdef QT_DEBUG
        return "http://localhost:8080";
#else
        return "https://api.yourdomain.com";
#endif
    }

    static QString getAuthServiceUrl() {
#ifdef QT_DEBUG
        return "http://localhost:8008";
#else
        return "https://auth.yourdomain.com";
#endif
    }
};

#endif // CONFIG_H
```

#### JavaScript配置
```javascript
// config.js
const config = {
  development: {
    apiBaseUrl: 'http://localhost:8080',
    authServiceUrl: 'http://localhost:8008'
  },
  production: {
    apiBaseUrl: 'https://api.yourdomain.com',
    authServiceUrl: 'https://auth.yourdomain.com'
  }
};

export default config[process.env.NODE_ENV || 'development'];
```

### 2. API客户端封装

#### Qt/C++ API客户端

```cpp
// apiclient.h
#ifndef APICLIENT_H
#define APICLIENT_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QSettings>
#include <QTimer>

class ApiClient : public QObject
{
    Q_OBJECT

public:
    struct ApiResponse {
        bool success = false;
        int statusCode = 0;
        QJsonObject data;
        QString errorMessage;
        QString errorCode;
    };

    explicit ApiClient(const QString &baseUrl, QObject *parent = nullptr);

    // 异步API调用
    void get(const QString &endpoint, const QJsonObject &params = QJsonObject{});
    void post(const QString &endpoint, const QJsonObject &data);
    void put(const QString &endpoint, const QJsonObject &data);
    void del(const QString &endpoint);

    // 认证相关
    void setToken(const QString &token);
    QString getToken() const;
    void clearToken();
    bool isAuthenticated() const;

signals:
    void requestFinished(const ApiResponse &response);
    void authenticationRequired();
    void networkError(const QString &error);

private slots:
    void onReplyFinished();
    void onRefreshTokenFinished();

private:
    QNetworkAccessManager *m_networkManager;
    QString m_baseUrl;
    QString m_accessToken;
    QString m_refreshToken;
    QSettings *m_settings;

    QNetworkRequest createRequest(const QString &endpoint);
    void handleResponse(QNetworkReply *reply);
    void refreshAccessToken();
    ApiResponse parseResponse(QNetworkReply *reply);
};

#endif // APICLIENT_H
```

```cpp
// apiclient.cpp
#include "apiclient.h"
#include "config.h"
#include <QUrlQuery>
#include <QJsonParseError>
#include <QDebug>

ApiClient::ApiClient(const QString &baseUrl, QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_baseUrl(baseUrl)
    , m_settings(new QSettings(this))
{
    // 从设置中恢复令牌
    m_accessToken = m_settings->value("access_token").toString();
    m_refreshToken = m_settings->value("refresh_token").toString();
}

void ApiClient::get(const QString &endpoint, const QJsonObject &params)
{
    QUrl url(m_baseUrl + endpoint);

    if (!params.isEmpty()) {
        QUrlQuery query;
        for (auto it = params.begin(); it != params.end(); ++it) {
            query.addQueryItem(it.key(), it.value().toString());
        }
        url.setQuery(query);
    }

    QNetworkRequest request = createRequest(endpoint);
    request.setUrl(url);

    QNetworkReply *reply = m_networkManager->get(request);
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onReplyFinished);
}

void ApiClient::post(const QString &endpoint, const QJsonObject &data)
{
    QNetworkRequest request = createRequest(endpoint);

    QJsonDocument doc(data);
    QByteArray jsonData = doc.toJson();

    QNetworkReply *reply = m_networkManager->post(request, jsonData);
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onReplyFinished);
}

QNetworkRequest ApiClient::createRequest(const QString &endpoint)
{
    QNetworkRequest request;
    request.setUrl(QUrl(m_baseUrl + endpoint));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    if (!m_accessToken.isEmpty()) {
        request.setRawHeader("Authorization", ("Bearer " + m_accessToken).toUtf8());
    }

    return request;
}

void ApiClient::onReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    // 处理401错误，尝试刷新令牌
    if (reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt() == 401
        && !m_refreshToken.isEmpty()) {

        reply->deleteLater();
        refreshAccessToken();
        return;
    }

    handleResponse(reply);
    reply->deleteLater();
}

void ApiClient::handleResponse(QNetworkReply *reply)
{
    ApiResponse response = parseResponse(reply);
    emit requestFinished(response);
}

ApiClient::ApiResponse ApiClient::parseResponse(QNetworkReply *reply)
{
    ApiResponse response;
    response.statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

    if (reply->error() != QNetworkReply::NoError) {
        response.success = false;
        response.errorMessage = reply->errorString();
        return response;
    }

    QByteArray data = reply->readAll();
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        response.success = false;
        response.errorMessage = "JSON解析错误: " + parseError.errorString();
        return response;
    }

    QJsonObject jsonObj = doc.object();
    response.success = jsonObj["success"].toBool();
    response.data = jsonObj["data"].toObject();
    response.errorMessage = jsonObj["error_message"].toString();
    response.errorCode = jsonObj["error_code"].toString();

    return response;
}

void ApiClient::setToken(const QString &token)
{
    m_accessToken = token;
    m_settings->setValue("access_token", token);
}

void ApiClient::refreshAccessToken()
{
    QJsonObject data;
    data["refresh_token"] = m_refreshToken;

    QNetworkRequest request = createRequest("/api/v1/auth/refresh");
    request.setRawHeader("Authorization", ""); // 清除Authorization头

    QJsonDocument doc(data);
    QNetworkReply *reply = m_networkManager->post(request, doc.toJson());
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onRefreshTokenFinished);
}

void ApiClient::onRefreshTokenFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    ApiResponse response = parseResponse(reply);

    if (response.success) {
        QString newToken = response.data["token"].toString();
        setToken(newToken);
        qDebug() << "令牌刷新成功";
    } else {
        // 刷新失败，清除令牌
        clearToken();
        emit authenticationRequired();
    }

    reply->deleteLater();
}

void ApiClient::clearToken()
{
    m_accessToken.clear();
    m_refreshToken.clear();
    m_settings->remove("access_token");
    m_settings->remove("refresh_token");
}

bool ApiClient::isAuthenticated() const
{
    return !m_accessToken.isEmpty();
}
```

#### JavaScript API客户端

```javascript
// api/client.js
class ApiClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.token = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { 'Authorization': `Bearer ${this.token}` })
      },
      ...options
    };

    try {
      let response = await fetch(url, config);
      
      // 自动刷新令牌
      if (response.status === 401 && this.refreshToken) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          config.headers['Authorization'] = `Bearer ${this.token}`;
          response = await fetch(url, config);
        }
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new ApiError(data.error_code, data.error_message, response.status);
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError('NETWORK_ERROR', error.message, 0);
    }
  }

  async refreshAccessToken() {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh_token: this.refreshToken })
      });

      const data = await response.json();
      if (data.success) {
        this.token = data.data.token;
        localStorage.setItem('access_token', this.token);
        return true;
      }
      
      this.logout();
      return false;
    } catch (error) {
      this.logout();
      return false;
    }
  }

  logout() {
    this.token = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
}

class ApiError extends Error {
  constructor(code, message, status) {
    super(message);
    this.code = code;
    this.status = status;
  }
}

export { ApiClient, ApiError };
```

### 3. 认证服务

#### Qt/C++ 认证服务

```cpp
// authservice.h
#ifndef AUTHSERVICE_H
#define AUTHSERVICE_H

#include <QObject>
#include <QJsonObject>
#include "apiclient.h"

class AuthService : public QObject
{
    Q_OBJECT

public:
    struct UserInfo {
        int userId = 0;
        QString username;
        QString email;
        QString lastLogin;
        QString status;
    };

    explicit AuthService(QObject *parent = nullptr);

    // 认证操作
    void registerUser(const QString &username, const QString &password, const QString &email);
    void login(const QString &username, const QString &password, bool rememberMe = false);
    void logout(bool allDevices = false);
    void validateToken();
    void refreshToken();

    // 状态查询
    bool isAuthenticated() const;
    UserInfo getCurrentUser() const;
    QString getToken() const;

signals:
    void loginSucceeded(const UserInfo &userInfo);
    void loginFailed(const QString &error);
    void registerSucceeded();
    void registerFailed(const QString &error);
    void logoutCompleted();
    void tokenValidated(const UserInfo &userInfo);
    void tokenExpired();
    void authenticationRequired();

private slots:
    void onApiResponse(const ApiClient::ApiResponse &response);

private:
    ApiClient *m_apiClient;
    UserInfo m_currentUser;
    QString m_pendingOperation;

    void saveUserInfo(const QJsonObject &userInfo);
    void clearUserInfo();
    UserInfo parseUserInfo(const QJsonObject &data) const;
};

#endif // AUTHSERVICE_H
```

```cpp
// authservice.cpp
#include "authservice.h"
#include "config.h"
#include <QSettings>
#include <QDebug>

AuthService::AuthService(QObject *parent)
    : QObject(parent)
    , m_apiClient(new ApiClient(Config::getApiBaseUrl(), this))
{
    connect(m_apiClient, &ApiClient::requestFinished,
            this, &AuthService::onApiResponse);
    connect(m_apiClient, &ApiClient::authenticationRequired,
            this, &AuthService::authenticationRequired);

    // 恢复用户信息
    QSettings settings;
    if (settings.contains("user_info")) {
        QJsonObject userObj = QJsonDocument::fromJson(
            settings.value("user_info").toByteArray()).object();
        m_currentUser = parseUserInfo(userObj);
    }
}

void AuthService::registerUser(const QString &username, const QString &password, const QString &email)
{
    m_pendingOperation = "register";

    QJsonObject data;
    data["username"] = username;
    data["password"] = password;
    data["email"] = email;

    m_apiClient->post("/api/v1/auth/register", data);
}

void AuthService::login(const QString &username, const QString &password, bool rememberMe)
{
    m_pendingOperation = "login";

    QJsonObject data;
    data["username"] = username;
    data["password"] = password;
    data["remember_me"] = rememberMe;

    m_apiClient->post("/api/v1/auth/login", data);
}

void AuthService::logout(bool allDevices)
{
    m_pendingOperation = "logout";

    QJsonObject data;
    data["all_devices"] = allDevices;

    m_apiClient->post("/api/v1/auth/logout", data);
}

void AuthService::validateToken()
{
    if (!isAuthenticated()) {
        emit authenticationRequired();
        return;
    }

    m_pendingOperation = "validate";
    m_apiClient->post("/api/v1/auth/validate", QJsonObject{});
}

void AuthService::onApiResponse(const ApiClient::ApiResponse &response)
{
    if (m_pendingOperation == "register") {
        if (response.success) {
            emit registerSucceeded();
        } else {
            emit registerFailed(response.errorMessage);
        }
    }
    else if (m_pendingOperation == "login") {
        if (response.success) {
            // 保存令牌和用户信息
            QString token = response.data["token"].toString();
            QString refreshToken = response.data["refresh_token"].toString();

            m_apiClient->setToken(token);

            QSettings settings;
            settings.setValue("refresh_token", refreshToken);

            // 保存用户信息
            QJsonObject userInfo = response.data["user_info"].toObject();
            saveUserInfo(userInfo);
            m_currentUser = parseUserInfo(userInfo);

            emit loginSucceeded(m_currentUser);
        } else {
            emit loginFailed(response.errorMessage);
        }
    }
    else if (m_pendingOperation == "logout") {
        clearUserInfo();
        emit logoutCompleted();
    }
    else if (m_pendingOperation == "validate") {
        if (response.success) {
            m_currentUser = parseUserInfo(response.data);
            emit tokenValidated(m_currentUser);
        } else {
            emit tokenExpired();
        }
    }

    m_pendingOperation.clear();
}

void AuthService::saveUserInfo(const QJsonObject &userInfo)
{
    QSettings settings;
    QJsonDocument doc(userInfo);
    settings.setValue("user_info", doc.toJson());
}

void AuthService::clearUserInfo()
{
    m_apiClient->clearToken();
    m_currentUser = UserInfo{};

    QSettings settings;
    settings.remove("user_info");
    settings.remove("refresh_token");
}

AuthService::UserInfo AuthService::parseUserInfo(const QJsonObject &data) const
{
    UserInfo info;
    info.userId = data["user_id"].toInt();
    info.username = data["username"].toString();
    info.email = data["email"].toString();
    info.lastLogin = data["last_login"].toString();
    info.status = data["status"].toString();
    return info;
}

bool AuthService::isAuthenticated() const
{
    return m_apiClient->isAuthenticated();
}

AuthService::UserInfo AuthService::getCurrentUser() const
{
    return m_currentUser;
}

QString AuthService::getToken() const
{
    return m_apiClient->getToken();
}
```

#### JavaScript 认证服务

```javascript
// services/auth.js
import { ApiClient } from '../api/client.js';
import config from '../config.js';

class AuthService extends ApiClient {
  constructor() {
    super(config.apiBaseUrl);
  }

  async register(userData) {
    const response = await this.request('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
    return response.data;
  }

  async login(credentials) {
    const response = await this.request('/api/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    });

    const { token, refresh_token, user_info } = response.data;
    
    this.token = token;
    this.refreshToken = refresh_token;
    
    localStorage.setItem('access_token', token);
    localStorage.setItem('refresh_token', refresh_token);
    localStorage.setItem('user_info', JSON.stringify(user_info));

    return response.data;
  }

  async logout(allDevices = false) {
    try {
      await this.request('/api/v1/auth/logout', {
        method: 'POST',
        body: JSON.stringify({ all_devices: allDevices })
      });
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      this.clearLocalStorage();
    }
  }

  async validateToken() {
    try {
      const response = await this.request('/api/v1/auth/validate', {
        method: 'POST',
        body: JSON.stringify({})
      });
      return response.data;
    } catch (error) {
      this.clearLocalStorage();
      throw error;
    }
  }

  clearLocalStorage() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_info');
    this.token = null;
    this.refreshToken = null;
  }

  isAuthenticated() {
    return !!this.token;
  }

  getCurrentUser() {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  }
}

export default new AuthService();
```

### 4. 游戏服务

#### Qt/C++ 游戏服务

```cpp
// gameservice.h
#ifndef GAMESERVICE_H
#define GAMESERVICE_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include "apiclient.h"

class GameService : public QObject
{
    Q_OBJECT

public:
    struct GameServer {
        QString serverId;
        QString name;
        QString host;
        int port = 0;
        QString gameType;
        QString region;
        QString status;
        int maxPlayers = 0;
        int currentPlayers = 0;
        int ping = 0;
        double loadPercentage = 0.0;
        QString version;
        QStringList features;
    };

    struct GameLoginResult {
        GameServer serverInfo;
        QString sessionToken;
        QString sessionId;
        int expiresIn = 0;
        QJsonObject gameConfig;
    };

    explicit GameService(QObject *parent = nullptr);

    // 游戏操作
    void getGameServers(const QString &gameType = QString(),
                       const QString &region = QString(),
                       const QString &status = "online");
    void joinGame(const QString &gameType, const QString &serverPreference = "balanced");
    void getBestServers(const QString &gameType, const QString &region = "asia", int limit = 5);

signals:
    void gameServersReceived(const QList<GameServer> &servers);
    void gameJoinSucceeded(const GameLoginResult &result);
    void gameJoinFailed(const QString &error);
    void requestFailed(const QString &error);

private slots:
    void onApiResponse(const ApiClient::ApiResponse &response);

private:
    ApiClient *m_apiClient;
    QString m_pendingOperation;

    GameServer parseGameServer(const QJsonObject &data) const;
    GameLoginResult parseGameLoginResult(const QJsonObject &data) const;
};

#endif // GAMESERVICE_H
```

```cpp
// gameservice.cpp
#include "gameservice.h"
#include "config.h"
#include <QJsonDocument>
#include <QDebug>

GameService::GameService(QObject *parent)
    : QObject(parent)
    , m_apiClient(new ApiClient(Config::getApiBaseUrl(), this))
{
    connect(m_apiClient, &ApiClient::requestFinished,
            this, &GameService::onApiResponse);
}

void GameService::getGameServers(const QString &gameType, const QString &region, const QString &status)
{
    m_pendingOperation = "getGameServers";

    QJsonObject params;
    if (!gameType.isEmpty()) params["game_type"] = gameType;
    if (!region.isEmpty()) params["region"] = region;
    if (!status.isEmpty()) params["status"] = status;

    m_apiClient->get("/api/v1/game/servers", params);
}

void GameService::joinGame(const QString &gameType, const QString &serverPreference)
{
    m_pendingOperation = "joinGame";

    QJsonObject data;
    data["game_type"] = gameType;
    data["server_preference"] = serverPreference;

    m_apiClient->post("/api/v1/game/login", data);
}

void GameService::getBestServers(const QString &gameType, const QString &region, int limit)
{
    m_pendingOperation = "getGameServers";

    QJsonObject params;
    params["game_type"] = gameType;
    params["region"] = region;
    params["status"] = "online";
    params["sort_by"] = "ping";
    params["limit"] = limit;

    m_apiClient->get("/api/v1/game/servers", params);
}

void GameService::onApiResponse(const ApiClient::ApiResponse &response)
{
    if (m_pendingOperation == "getGameServers") {
        if (response.success) {
            QJsonArray serversArray = response.data["servers"].toArray();
            QList<GameServer> servers;

            for (const QJsonValue &value : serversArray) {
                servers.append(parseGameServer(value.toObject()));
            }

            emit gameServersReceived(servers);
        } else {
            emit requestFailed(response.errorMessage);
        }
    }
    else if (m_pendingOperation == "joinGame") {
        if (response.success) {
            GameLoginResult result = parseGameLoginResult(response.data);
            emit gameJoinSucceeded(result);
        } else {
            emit gameJoinFailed(response.errorMessage);
        }
    }

    m_pendingOperation.clear();
}

GameService::GameServer GameService::parseGameServer(const QJsonObject &data) const
{
    GameServer server;
    server.serverId = data["server_id"].toString();
    server.name = data["name"].toString();
    server.host = data["host"].toString();
    server.port = data["port"].toInt();
    server.gameType = data["game_type"].toString();
    server.region = data["region"].toString();
    server.status = data["status"].toString();
    server.maxPlayers = data["max_players"].toInt();
    server.currentPlayers = data["current_players"].toInt();
    server.ping = data["ping"].toInt();
    server.loadPercentage = data["load_percentage"].toDouble();
    server.version = data["version"].toString();

    QJsonArray featuresArray = data["features"].toArray();
    for (const QJsonValue &value : featuresArray) {
        server.features.append(value.toString());
    }

    return server;
}

GameService::GameLoginResult GameService::parseGameLoginResult(const QJsonObject &data) const
{
    GameLoginResult result;
    result.serverInfo = parseGameServer(data["server_info"].toObject());
    result.sessionToken = data["session_token"].toString();
    result.sessionId = data["session_id"].toString();
    result.expiresIn = data["expires_in"].toInt();
    result.gameConfig = data["game_config"].toObject();

    return result;
}
```

#### JavaScript 游戏服务

```javascript
// services/game.js
import { ApiClient } from '../api/client.js';
import config from '../config.js';

class GameService extends ApiClient {
  constructor() {
    super(config.apiBaseUrl);
  }

  async getGameServers(filters = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value);
      }
    });

    const endpoint = `/api/v1/game/servers${params.toString() ? `?${params}` : ''}`;
    const response = await this.request(endpoint);
    return response.data;
  }

  async joinGame(gameType, serverPreference = 'balanced') {
    const response = await this.request('/api/v1/game/login', {
      method: 'POST',
      body: JSON.stringify({
        game_type: gameType,
        server_preference: serverPreference
      })
    });
    return response.data;
  }

  async getBestServers(gameType, region = 'asia', limit = 5) {
    return await this.getGameServers({
      game_type: gameType,
      region: region,
      status: 'online',
      sort_by: 'ping',
      limit: limit
    });
  }
}

export default new GameService();
```

## Qt/C++集成示例

### 1. 主窗口集成

```cpp
// mainwindow.h
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QLabel>
#include <QMenuBar>
#include <QStatusBar>
#include "authservice.h"
#include "gameservice.h"
#include "loginwidget.h"
#include "gameserverwidget.h"

QT_BEGIN_NAMESPACE
class QAction;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onLoginSucceeded(const AuthService::UserInfo &userInfo);
    void onLoginFailed(const QString &error);
    void onLogoutCompleted();
    void onAuthenticationRequired();
    void showLoginPage();
    void showGamePage();
    void logout();

private:
    void setupUI();
    void setupMenus();
    void updateUserInterface();

    QStackedWidget *m_centralStack;
    LoginWidget *m_loginWidget;
    GameServerWidget *m_gameWidget;

    AuthService *m_authService;
    GameService *m_gameService;

    QLabel *m_userLabel;
    QAction *m_loginAction;
    QAction *m_logoutAction;
};

#endif // MAINWINDOW_H
```

```cpp
// mainwindow.cpp
#include "mainwindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMenuBar>
#include <QStatusBar>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_authService(new AuthService(this))
    , m_gameService(new GameService(this))
{
    setupUI();
    setupMenus();

    // 连接认证服务信号
    connect(m_authService, &AuthService::loginSucceeded,
            this, &MainWindow::onLoginSucceeded);
    connect(m_authService, &AuthService::loginFailed,
            this, &MainWindow::onLoginFailed);
    connect(m_authService, &AuthService::logoutCompleted,
            this, &MainWindow::onLogoutCompleted);
    connect(m_authService, &AuthService::authenticationRequired,
            this, &MainWindow::onAuthenticationRequired);

    // 检查是否已登录
    if (m_authService->isAuthenticated()) {
        m_authService->validateToken();
    } else {
        showLoginPage();
    }

    updateUserInterface();
}

void MainWindow::setupUI()
{
    m_centralStack = new QStackedWidget(this);
    setCentralWidget(m_centralStack);

    // 登录页面
    m_loginWidget = new LoginWidget(m_authService, this);
    m_centralStack->addWidget(m_loginWidget);

    // 游戏页面
    m_gameWidget = new GameServerWidget(m_gameService, this);
    m_centralStack->addWidget(m_gameWidget);

    // 状态栏
    m_userLabel = new QLabel(this);
    statusBar()->addPermanentWidget(m_userLabel);

    setWindowTitle("游戏客户端");
    resize(800, 600);
}

void MainWindow::setupMenus()
{
    QMenu *accountMenu = menuBar()->addMenu("账户");

    m_loginAction = accountMenu->addAction("登录");
    connect(m_loginAction, &QAction::triggered, this, &MainWindow::showLoginPage);

    m_logoutAction = accountMenu->addAction("登出");
    connect(m_logoutAction, &QAction::triggered, this, &MainWindow::logout);

    accountMenu->addSeparator();
    accountMenu->addAction("退出", this, &QWidget::close);
}

void MainWindow::onLoginSucceeded(const AuthService::UserInfo &userInfo)
{
    m_userLabel->setText(QString("欢迎, %1").arg(userInfo.username));
    showGamePage();
    updateUserInterface();

    statusBar()->showMessage("登录成功", 3000);
}

void MainWindow::onLoginFailed(const QString &error)
{
    QMessageBox::warning(this, "登录失败", error);
    showLoginPage();
}

void MainWindow::onLogoutCompleted()
{
    m_userLabel->clear();
    showLoginPage();
    updateUserInterface();

    statusBar()->showMessage("已登出", 3000);
}

void MainWindow::onAuthenticationRequired()
{
    QMessageBox::information(this, "提示", "登录已过期，请重新登录");
    showLoginPage();
}

void MainWindow::showLoginPage()
{
    m_centralStack->setCurrentWidget(m_loginWidget);
}

void MainWindow::showGamePage()
{
    m_centralStack->setCurrentWidget(m_gameWidget);
}

void MainWindow::logout()
{
    m_authService->logout();
}

void MainWindow::updateUserInterface()
{
    bool authenticated = m_authService->isAuthenticated();
    m_loginAction->setVisible(!authenticated);
    m_logoutAction->setVisible(authenticated);
}
```

### 2. 登录界面

```cpp
// loginwidget.h
#ifndef LOGINWIDGET_H
#define LOGINWIDGET_H

#include <QWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QCheckBox>
#include <QLabel>
#include <QProgressBar>
#include "authservice.h"

class LoginWidget : public QWidget
{
    Q_OBJECT

public:
    explicit LoginWidget(AuthService *authService, QWidget *parent = nullptr);

private slots:
    void onLoginClicked();
    void onRegisterClicked();
    void onLoginSucceeded(const AuthService::UserInfo &userInfo);
    void onLoginFailed(const QString &error);
    void onRegisterSucceeded();
    void onRegisterFailed(const QString &error);

private:
    void setupUI();
    void setUIEnabled(bool enabled);

    AuthService *m_authService;

    QLineEdit *m_usernameEdit;
    QLineEdit *m_passwordEdit;
    QLineEdit *m_emailEdit;
    QCheckBox *m_rememberCheck;
    QPushButton *m_loginButton;
    QPushButton *m_registerButton;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;

    bool m_isRegisterMode;
};

#endif // LOGINWIDGET_H
```

```cpp
// loginwidget.cpp
#include "loginwidget.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QMessageBox>

LoginWidget::LoginWidget(AuthService *authService, QWidget *parent)
    : QWidget(parent)
    , m_authService(authService)
    , m_isRegisterMode(false)
{
    setupUI();

    connect(m_authService, &AuthService::loginSucceeded,
            this, &LoginWidget::onLoginSucceeded);
    connect(m_authService, &AuthService::loginFailed,
            this, &LoginWidget::onLoginFailed);
    connect(m_authService, &AuthService::registerSucceeded,
            this, &LoginWidget::onRegisterSucceeded);
    connect(m_authService, &AuthService::registerFailed,
            this, &LoginWidget::onRegisterFailed);
}

void LoginWidget::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 标题
    QLabel *titleLabel = new QLabel("游戏客户端登录", this);
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;");
    mainLayout->addWidget(titleLabel);

    // 登录表单
    QGroupBox *formGroup = new QGroupBox("账户信息", this);
    QFormLayout *formLayout = new QFormLayout(formGroup);

    m_usernameEdit = new QLineEdit(this);
    m_usernameEdit->setPlaceholderText("请输入用户名");
    formLayout->addRow("用户名:", m_usernameEdit);

    m_passwordEdit = new QLineEdit(this);
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    m_passwordEdit->setPlaceholderText("请输入密码");
    formLayout->addRow("密码:", m_passwordEdit);

    m_emailEdit = new QLineEdit(this);
    m_emailEdit->setPlaceholderText("请输入邮箱地址");
    m_emailEdit->setVisible(false);
    formLayout->addRow("邮箱:", m_emailEdit);

    m_rememberCheck = new QCheckBox("记住我", this);
    formLayout->addRow("", m_rememberCheck);

    mainLayout->addWidget(formGroup);

    // 按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();

    m_loginButton = new QPushButton("登录", this);
    m_loginButton->setDefault(true);
    connect(m_loginButton, &QPushButton::clicked, this, &LoginWidget::onLoginClicked);
    buttonLayout->addWidget(m_loginButton);

    m_registerButton = new QPushButton("注册", this);
    connect(m_registerButton, &QPushButton::clicked, this, &LoginWidget::onRegisterClicked);
    buttonLayout->addWidget(m_registerButton);

    mainLayout->addLayout(buttonLayout);

    // 状态显示
    m_statusLabel = new QLabel(this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("color: red;");
    mainLayout->addWidget(m_statusLabel);

    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);
    mainLayout->addWidget(m_progressBar);

    mainLayout->addStretch();

    // 回车键登录
    connect(m_passwordEdit, &QLineEdit::returnPressed, this, &LoginWidget::onLoginClicked);
}

void LoginWidget::onLoginClicked()
{
    QString username = m_usernameEdit->text().trimmed();
    QString password = m_passwordEdit->text();

    if (username.isEmpty() || password.isEmpty()) {
        m_statusLabel->setText("请输入用户名和密码");
        return;
    }

    if (m_isRegisterMode) {
        QString email = m_emailEdit->text().trimmed();
        if (email.isEmpty()) {
            m_statusLabel->setText("请输入邮箱地址");
            return;
        }

        setUIEnabled(false);
        m_progressBar->setVisible(true);
        m_statusLabel->setText("正在注册...");

        m_authService->registerUser(username, password, email);
    } else {
        setUIEnabled(false);
        m_progressBar->setVisible(true);
        m_statusLabel->setText("正在登录...");

        m_authService->login(username, password, m_rememberCheck->isChecked());
    }
}

void LoginWidget::onRegisterClicked()
{
    m_isRegisterMode = !m_isRegisterMode;

    if (m_isRegisterMode) {
        m_emailEdit->setVisible(true);
        m_loginButton->setText("注册");
        m_registerButton->setText("返回登录");
        m_rememberCheck->setVisible(false);
    } else {
        m_emailEdit->setVisible(false);
        m_loginButton->setText("登录");
        m_registerButton->setText("注册");
        m_rememberCheck->setVisible(true);
    }

    m_statusLabel->clear();
}

void LoginWidget::onLoginSucceeded(const AuthService::UserInfo &userInfo)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);
    m_statusLabel->setText("登录成功!");
    m_statusLabel->setStyleSheet("color: green;");
}

void LoginWidget::onLoginFailed(const QString &error)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);
    m_statusLabel->setText(error);
    m_statusLabel->setStyleSheet("color: red;");
}

void LoginWidget::onRegisterSucceeded()
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);
    m_statusLabel->setText("注册成功! 请登录");
    m_statusLabel->setStyleSheet("color: green;");

    // 切换到登录模式
    onRegisterClicked();
}

void LoginWidget::onRegisterFailed(const QString &error)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);
    m_statusLabel->setText(error);
    m_statusLabel->setStyleSheet("color: red;");
}

void LoginWidget::setUIEnabled(bool enabled)
{
    m_usernameEdit->setEnabled(enabled);
    m_passwordEdit->setEnabled(enabled);
    m_emailEdit->setEnabled(enabled);
    m_rememberCheck->setEnabled(enabled);
    m_loginButton->setEnabled(enabled);
    m_registerButton->setEnabled(enabled);
}
```

### 3. 游戏服务器列表界面

```cpp
// gameserverwidget.h
#ifndef GAMESERVERWIDGET_H
#define GAMESERVERWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QTimer>
#include "gameservice.h"

class GameServerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit GameServerWidget(GameService *gameService, QWidget *parent = nullptr);

private slots:
    void onRefreshClicked();
    void onJoinClicked();
    void onFilterChanged();
    void onGameServersReceived(const QList<GameService::GameServer> &servers);
    void onGameJoinSucceeded(const GameService::GameLoginResult &result);
    void onGameJoinFailed(const QString &error);
    void onRequestFailed(const QString &error);
    void onAutoRefreshTimer();

private:
    void setupUI();
    void updateServerList(const QList<GameService::GameServer> &servers);
    void setUIEnabled(bool enabled);
    GameService::GameServer getSelectedServer() const;

    GameService *m_gameService;

    QComboBox *m_gameTypeCombo;
    QComboBox *m_regionCombo;
    QPushButton *m_refreshButton;
    QPushButton *m_joinButton;
    QTableWidget *m_serverTable;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    QTimer *m_autoRefreshTimer;

    QList<GameService::GameServer> m_servers;
};

#endif // GAMESERVERWIDGET_H
```

```cpp
// gameserverwidget.cpp
#include "gameserverwidget.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QHeaderView>
#include <QMessageBox>
#include <QDesktopServices>
#include <QUrl>

GameServerWidget::GameServerWidget(GameService *gameService, QWidget *parent)
    : QWidget(parent)
    , m_gameService(gameService)
    , m_autoRefreshTimer(new QTimer(this))
{
    setupUI();

    connect(m_gameService, &GameService::gameServersReceived,
            this, &GameServerWidget::onGameServersReceived);
    connect(m_gameService, &GameService::gameJoinSucceeded,
            this, &GameServerWidget::onGameJoinSucceeded);
    connect(m_gameService, &GameService::gameJoinFailed,
            this, &GameServerWidget::onGameJoinFailed);
    connect(m_gameService, &GameService::requestFailed,
            this, &GameServerWidget::onRequestFailed);

    // 自动刷新定时器
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &GameServerWidget::onAutoRefreshTimer);
    m_autoRefreshTimer->start(30000); // 30秒自动刷新

    // 初始加载
    onRefreshClicked();
}

void GameServerWidget::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 标题
    QLabel *titleLabel = new QLabel("游戏服务器列表", this);
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;");
    mainLayout->addWidget(titleLabel);

    // 过滤器
    QHBoxLayout *filterLayout = new QHBoxLayout();

    filterLayout->addWidget(new QLabel("游戏类型:", this));
    m_gameTypeCombo = new QComboBox(this);
    m_gameTypeCombo->addItems({"全部", "snake", "tetris", "puzzle"});
    connect(m_gameTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &GameServerWidget::onFilterChanged);
    filterLayout->addWidget(m_gameTypeCombo);

    filterLayout->addWidget(new QLabel("地区:", this));
    m_regionCombo = new QComboBox(this);
    m_regionCombo->addItems({"全部", "asia", "europe", "america"});
    connect(m_regionCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &GameServerWidget::onFilterChanged);
    filterLayout->addWidget(m_regionCombo);

    filterLayout->addStretch();

    m_refreshButton = new QPushButton("刷新", this);
    connect(m_refreshButton, &QPushButton::clicked, this, &GameServerWidget::onRefreshClicked);
    filterLayout->addWidget(m_refreshButton);

    mainLayout->addLayout(filterLayout);

    // 服务器列表
    m_serverTable = new QTableWidget(this);
    m_serverTable->setColumnCount(8);
    QStringList headers = {"服务器名称", "游戏类型", "地区", "延迟", "玩家数", "负载", "状态", "版本"};
    m_serverTable->setHorizontalHeaderLabels(headers);
    m_serverTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_serverTable->setAlternatingRowColors(true);
    m_serverTable->horizontalHeader()->setStretchLastSection(true);
    mainLayout->addWidget(m_serverTable);

    // 操作按钮
    QHBoxLayout *actionLayout = new QHBoxLayout();
    actionLayout->addStretch();

    m_joinButton = new QPushButton("加入游戏", this);
    m_joinButton->setEnabled(false);
    connect(m_joinButton, &QPushButton::clicked, this, &GameServerWidget::onJoinClicked);
    actionLayout->addWidget(m_joinButton);

    mainLayout->addLayout(actionLayout);

    // 状态显示
    m_statusLabel = new QLabel(this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(m_statusLabel);

    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);
    mainLayout->addWidget(m_progressBar);

    // 表格选择事件
    connect(m_serverTable, &QTableWidget::itemSelectionChanged, [this]() {
        m_joinButton->setEnabled(m_serverTable->currentRow() >= 0);
    });
}

void GameServerWidget::onRefreshClicked()
{
    setUIEnabled(false);
    m_progressBar->setVisible(true);
    m_statusLabel->setText("正在获取服务器列表...");

    QString gameType = m_gameTypeCombo->currentText();
    if (gameType == "全部") gameType.clear();

    QString region = m_regionCombo->currentText();
    if (region == "全部") region.clear();

    m_gameService->getGameServers(gameType, region);
}

void GameServerWidget::onJoinClicked()
{
    GameService::GameServer server = getSelectedServer();
    if (server.serverId.isEmpty()) {
        QMessageBox::warning(this, "警告", "请选择一个服务器");
        return;
    }

    if (server.status != "online") {
        QMessageBox::warning(this, "警告", "选择的服务器不在线");
        return;
    }

    if (server.currentPlayers >= server.maxPlayers) {
        QMessageBox::warning(this, "警告", "选择的服务器已满");
        return;
    }

    setUIEnabled(false);
    m_progressBar->setVisible(true);
    m_statusLabel->setText("正在连接游戏服务器...");

    m_gameService->joinGame(server.gameType, "low_latency");
}

void GameServerWidget::onFilterChanged()
{
    onRefreshClicked();
}

void GameServerWidget::onGameServersReceived(const QList<GameService::GameServer> &servers)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);

    m_servers = servers;
    updateServerList(servers);

    m_statusLabel->setText(QString("找到 %1 个服务器").arg(servers.size()));
}

void GameServerWidget::onGameJoinSucceeded(const GameService::GameLoginResult &result)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);

    QString message = QString("成功连接到服务器: %1\n"
                             "会话ID: %2\n"
                             "服务器地址: %3:%4")
                      .arg(result.serverInfo.name)
                      .arg(result.sessionId)
                      .arg(result.serverInfo.host)
                      .arg(result.serverInfo.port);

    QMessageBox::information(this, "连接成功", message);

    // 可以在这里启动游戏客户端
    QString gameUrl = QString("game://%1:%2?token=%3")
                      .arg(result.serverInfo.host)
                      .arg(result.serverInfo.port)
                      .arg(result.sessionToken);

    QDesktopServices::openUrl(QUrl(gameUrl));
}

void GameServerWidget::onGameJoinFailed(const QString &error)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);
    m_statusLabel->setText("连接失败: " + error);

    QMessageBox::warning(this, "连接失败", error);
}

void GameServerWidget::onRequestFailed(const QString &error)
{
    setUIEnabled(true);
    m_progressBar->setVisible(false);
    m_statusLabel->setText("请求失败: " + error);
}

void GameServerWidget::onAutoRefreshTimer()
{
    if (m_refreshButton->isEnabled()) {
        onRefreshClicked();
    }
}

void GameServerWidget::updateServerList(const QList<GameService::GameServer> &servers)
{
    m_serverTable->setRowCount(servers.size());

    for (int i = 0; i < servers.size(); ++i) {
        const GameService::GameServer &server = servers[i];

        m_serverTable->setItem(i, 0, new QTableWidgetItem(server.name));
        m_serverTable->setItem(i, 1, new QTableWidgetItem(server.gameType));
        m_serverTable->setItem(i, 2, new QTableWidgetItem(server.region));
        m_serverTable->setItem(i, 3, new QTableWidgetItem(QString("%1ms").arg(server.ping)));
        m_serverTable->setItem(i, 4, new QTableWidgetItem(QString("%1/%2").arg(server.currentPlayers).arg(server.maxPlayers)));
        m_serverTable->setItem(i, 5, new QTableWidgetItem(QString("%1%").arg(server.loadPercentage, 0, 'f', 1)));

        QTableWidgetItem *statusItem = new QTableWidgetItem(server.status == "online" ? "在线" : "维护");
        if (server.status == "online") {
            statusItem->setBackground(QColor(144, 238, 144)); // 浅绿色
        } else {
            statusItem->setBackground(QColor(255, 182, 193)); // 浅红色
        }
        m_serverTable->setItem(i, 6, statusItem);

        m_serverTable->setItem(i, 7, new QTableWidgetItem(server.version));
    }

    m_serverTable->resizeColumnsToContents();
}

void GameServerWidget::setUIEnabled(bool enabled)
{
    m_gameTypeCombo->setEnabled(enabled);
    m_regionCombo->setEnabled(enabled);
    m_refreshButton->setEnabled(enabled);
    m_joinButton->setEnabled(enabled && m_serverTable->currentRow() >= 0);
    m_serverTable->setEnabled(enabled);
}

GameService::GameServer GameServerWidget::getSelectedServer() const
{
    int row = m_serverTable->currentRow();
    if (row >= 0 && row < m_servers.size()) {
        return m_servers[row];
    }
    return GameService::GameServer{};
}
```

## React集成示例

### 1. 认证Context

```jsx
// contexts/AuthContext.jsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import authService from '../services/auth.js';

const AuthContext = createContext();

const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null };
    case 'LOGIN_SUCCESS':
      return { 
        ...state, 
        loading: false, 
        isAuthenticated: true, 
        user: action.payload.user,
        error: null 
      };
    case 'LOGIN_FAILURE':
      return { 
        ...state, 
        loading: false, 
        isAuthenticated: false, 
        user: null, 
        error: action.payload 
      };
    case 'LOGOUT':
      return { 
        ...state, 
        isAuthenticated: false, 
        user: null, 
        error: null 
      };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    isAuthenticated: authService.isAuthenticated(),
    user: authService.getCurrentUser(),
    loading: false,
    error: null
  });

  useEffect(() => {
    // 验证现有token
    if (state.isAuthenticated) {
      authService.validateToken().catch(() => {
        dispatch({ type: 'LOGOUT' });
      });
    }
  }, []);

  const login = async (credentials) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const result = await authService.login(credentials);
      dispatch({ 
        type: 'LOGIN_SUCCESS', 
        payload: { user: result.user_info } 
      });
      return result;
    } catch (error) {
      dispatch({ 
        type: 'LOGIN_FAILURE', 
        payload: error.message 
      });
      throw error;
    }
  };

  const logout = async (allDevices = false) => {
    await authService.logout(allDevices);
    dispatch({ type: 'LOGOUT' });
  };

  const register = async (userData) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const result = await authService.register(userData);
      return result;
    } catch (error) {
      dispatch({ 
        type: 'LOGIN_FAILURE', 
        payload: error.message 
      });
      throw error;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      logout,
      register,
      clearError
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 2. 登录组件

```jsx
// components/LoginForm.jsx
import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const LoginForm = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });
  const { login, loading, error, clearError } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearError();
    
    try {
      await login({
        username: formData.username,
        password: formData.password,
        remember_me: formData.rememberMe
      });
      // 登录成功，路由会自动跳转
    } catch (error) {
      // 错误已经在context中处理
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <h2>用户登录</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="form-group">
        <label htmlFor="username">用户名</label>
        <input
          type="text"
          id="username"
          name="username"
          value={formData.username}
          onChange={handleChange}
          required
          disabled={loading}
        />
      </div>

      <div className="form-group">
        <label htmlFor="password">密码</label>
        <input
          type="password"
          id="password"
          name="password"
          value={formData.password}
          onChange={handleChange}
          required
          disabled={loading}
        />
      </div>

      <div className="form-group">
        <label>
          <input
            type="checkbox"
            name="rememberMe"
            checked={formData.rememberMe}
            onChange={handleChange}
            disabled={loading}
          />
          记住我
        </label>
      </div>

      <button type="submit" disabled={loading}>
        {loading ? '登录中...' : '登录'}
      </button>
    </form>
  );
};

export default LoginForm;
```

### 3. 游戏服务器列表组件

```jsx
// components/GameServerList.jsx
import React, { useState, useEffect } from 'react';
import gameService from '../services/game';

const GameServerList = ({ gameType = 'snake' }) => {
  const [servers, setServers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    game_type: gameType,
    region: 'asia',
    status: 'online'
  });

  useEffect(() => {
    loadServers();
  }, [filters]);

  const loadServers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await gameService.getGameServers(filters);
      setServers(data.servers || []);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleJoinServer = async (server) => {
    try {
      const result = await gameService.joinGame(
        server.game_type, 
        'low_latency'
      );
      
      // 处理游戏登录成功
      console.log('游戏登录成功:', result);
      
      // 可以跳转到游戏页面或显示连接信息
      window.location.href = `game://${result.server_info.host}:${result.server_info.port}`;
      
    } catch (error) {
      alert(`加入游戏失败: ${error.message}`);
    }
  };

  if (loading) return <div className="loading">加载中...</div>;
  if (error) return <div className="error">错误: {error}</div>;

  return (
    <div className="game-server-list">
      <h3>游戏服务器列表</h3>
      
      <div className="filters">
        <select 
          value={filters.region} 
          onChange={(e) => setFilters({...filters, region: e.target.value})}
        >
          <option value="asia">亚洲</option>
          <option value="europe">欧洲</option>
          <option value="america">美洲</option>
        </select>
      </div>

      <div className="server-grid">
        {servers.map(server => (
          <div key={server.server_id} className="server-card">
            <h4>{server.name}</h4>
            <div className="server-info">
              <span className="ping">延迟: {server.ping}ms</span>
              <span className="players">
                玩家: {server.current_players}/{server.max_players}
              </span>
              <span className={`status ${server.status}`}>
                {server.status === 'online' ? '在线' : '维护中'}
              </span>
            </div>
            <button 
              onClick={() => handleJoinServer(server)}
              disabled={server.status !== 'online' || server.current_players >= server.max_players}
              className="join-button"
            >
              {server.current_players >= server.max_players ? '已满' : '加入游戏'}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GameServerList;
```

## Vue.js集成示例

### 1. Pinia状态管理

```javascript
// stores/auth.js
import { defineStore } from 'pinia';
import authService from '../services/auth.js';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isAuthenticated: authService.isAuthenticated(),
    user: authService.getCurrentUser(),
    loading: false,
    error: null
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated,
    currentUser: (state) => state.user,
    hasError: (state) => !!state.error
  },

  actions: {
    async login(credentials) {
      this.loading = true;
      this.error = null;
      
      try {
        const result = await authService.login(credentials);
        this.isAuthenticated = true;
        this.user = result.user_info;
        return result;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async logout(allDevices = false) {
      await authService.logout(allDevices);
      this.isAuthenticated = false;
      this.user = null;
      this.error = null;
    },

    async register(userData) {
      this.loading = true;
      this.error = null;
      
      try {
        const result = await authService.register(userData);
        return result;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    clearError() {
      this.error = null;
    }
  }
});
```

### 2. Vue组件

```vue
<!-- components/LoginForm.vue -->
<template>
  <form @submit.prevent="handleLogin" class="login-form">
    <h2>用户登录</h2>
    
    <div v-if="authStore.hasError" class="error-message">
      {{ authStore.error }}
    </div>

    <div class="form-group">
      <label for="username">用户名</label>
      <input
        v-model="form.username"
        type="text"
        id="username"
        required
        :disabled="authStore.loading"
      />
    </div>

    <div class="form-group">
      <label for="password">密码</label>
      <input
        v-model="form.password"
        type="password"
        id="password"
        required
        :disabled="authStore.loading"
      />
    </div>

    <div class="form-group">
      <label>
        <input
          v-model="form.rememberMe"
          type="checkbox"
          :disabled="authStore.loading"
        />
        记住我
      </label>
    </div>

    <button type="submit" :disabled="authStore.loading">
      {{ authStore.loading ? '登录中...' : '登录' }}
    </button>
  </form>
</template>

<script setup>
import { reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth.js';

const router = useRouter();
const authStore = useAuthStore();

const form = reactive({
  username: '',
  password: '',
  rememberMe: false
});

const handleLogin = async () => {
  authStore.clearError();
  
  try {
    await authStore.login({
      username: form.username,
      password: form.password,
      remember_me: form.rememberMe
    });
    
    router.push('/dashboard');
  } catch (error) {
    // 错误已经在store中处理
  }
};
</script>
```

### 4. Qt项目配置

#### CMakeLists.txt
```cmake
cmake_minimum_required(VERSION 3.16)
project(GameClient VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt6配置
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)

qt6_standard_project_setup()

# 源文件
set(SOURCES
    main.cpp
    mainwindow.cpp
    loginwidget.cpp
    gameserverwidget.cpp
    apiclient.cpp
    authservice.cpp
    gameservice.cpp
    config.cpp
)

set(HEADERS
    mainwindow.h
    loginwidget.h
    gameserverwidget.h
    apiclient.h
    authservice.h
    gameservice.h
    config.h
)

# 创建可执行文件
qt6_add_executable(GameClient ${SOURCES} ${HEADERS})

# 链接Qt库
target_link_libraries(GameClient
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
)

# 编译器特定设置
if(MSVC)
    target_compile_options(GameClient PRIVATE /W4)
else()
    target_compile_options(GameClient PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 调试/发布配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(GameClient PRIVATE QT_DEBUG)
else()
    target_compile_definitions(GameClient PRIVATE QT_NO_DEBUG_OUTPUT)
endif()
```

#### main.cpp
```cpp
#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include "mainwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 应用程序信息
    app.setApplicationName("GameClient");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("YourCompany");
    app.setOrganizationDomain("yourdomain.com");

    // 设置样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/icons/app.png"));

    // 创建主窗口
    MainWindow window;
    window.show();

    return app.exec();
}
```

#### .pro文件 (如果使用qmake)
```pro
QT += core widgets network

CONFIG += c++17

TARGET = GameClient
TEMPLATE = app

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    loginwidget.cpp \
    gameserverwidget.cpp \
    apiclient.cpp \
    authservice.cpp \
    gameservice.cpp

HEADERS += \
    mainwindow.h \
    loginwidget.h \
    gameserverwidget.h \
    apiclient.h \
    authservice.h \
    gameservice.h \
    config.h

# 资源文件
RESOURCES += resources.qrc

# 平台特定配置
win32 {
    RC_ICONS = app.ico
    VERSION = 1.0.0.0
}

macx {
    ICON = app.icns
}

unix:!macx {
    target.path = /usr/local/bin
    INSTALLS += target
}
```

## 错误处理最佳实践

### Qt/C++错误处理

```cpp
// errorhandler.h
#ifndef ERRORHANDLER_H
#define ERRORHANDLER_H

#include <QString>
#include <QObject>

class ErrorHandler : public QObject
{
    Q_OBJECT

public:
    enum ErrorType {
        NetworkError,
        AuthenticationError,
        ValidationError,
        GameError,
        SystemError
    };

    static QString getErrorMessage(const QString &errorCode);
    static ErrorType getErrorType(const QString &errorCode);
    static void showErrorDialog(QWidget *parent, const QString &error);
    static void logError(const QString &context, const QString &error);

signals:
    void errorOccurred(ErrorType type, const QString &message);
};

#endif // ERRORHANDLER_H
```

```cpp
// errorhandler.cpp
#include "errorhandler.h"
#include <QMessageBox>
#include <QDebug>
#include <QDateTime>

QString ErrorHandler::getErrorMessage(const QString &errorCode)
{
    static QHash<QString, QString> errorMessages = {
        {"INVALID_CREDENTIALS", "用户名或密码错误"},
        {"TOKEN_EXPIRED", "登录已过期，请重新登录"},
        {"INVALID_TOKEN", "无效的访问令牌"},
        {"USER_EXISTS", "用户名已存在"},
        {"WEAK_PASSWORD", "密码强度不足"},
        {"INVALID_EMAIL", "邮箱格式不正确"},
        {"VALIDATION_ERROR", "输入信息格式不正确"},
        {"GAME_SERVER_FULL", "游戏服务器已满，请稍后重试"},
        {"GAME_TYPE_NOT_SUPPORTED", "不支持的游戏类型"},
        {"SERVER_UNAVAILABLE", "服务器不可用"},
        {"NETWORK_ERROR", "网络连接失败，请检查网络设置"},
        {"INTERNAL_ERROR", "内部服务器错误"},
        {"SERVICE_UNAVAILABLE", "服务暂时不可用"}
    };

    return errorMessages.value(errorCode, "未知错误: " + errorCode);
}

ErrorHandler::ErrorType ErrorHandler::getErrorType(const QString &errorCode)
{
    if (errorCode.contains("NETWORK") || errorCode.contains("CONNECTION")) {
        return NetworkError;
    } else if (errorCode.contains("TOKEN") || errorCode.contains("CREDENTIALS") || errorCode.contains("AUTH")) {
        return AuthenticationError;
    } else if (errorCode.contains("VALIDATION") || errorCode.contains("INVALID")) {
        return ValidationError;
    } else if (errorCode.contains("GAME") || errorCode.contains("SERVER")) {
        return GameError;
    } else {
        return SystemError;
    }
}

void ErrorHandler::showErrorDialog(QWidget *parent, const QString &error)
{
    QString message = getErrorMessage(error);
    ErrorType type = getErrorType(error);

    QMessageBox::Icon icon;
    QString title;

    switch (type) {
    case NetworkError:
        icon = QMessageBox::Warning;
        title = "网络错误";
        break;
    case AuthenticationError:
        icon = QMessageBox::Information;
        title = "认证错误";
        break;
    case ValidationError:
        icon = QMessageBox::Warning;
        title = "输入错误";
        break;
    case GameError:
        icon = QMessageBox::Critical;
        title = "游戏错误";
        break;
    default:
        icon = QMessageBox::Critical;
        title = "系统错误";
        break;
    }

    QMessageBox msgBox(parent);
    msgBox.setIcon(icon);
    msgBox.setWindowTitle(title);
    msgBox.setText(message);
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.exec();

    logError(title, message);
}

void ErrorHandler::logError(const QString &context, const QString &error)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    qDebug() << QString("[%1] %2: %3").arg(timestamp, context, error);
}
```

### JavaScript错误处理

### 1. 全局错误处理

```javascript
// utils/errorHandler.js
import { ApiError } from '../api/client.js';

export const handleApiError = (error) => {
  if (error instanceof ApiError) {
    switch (error.code) {
      case 'INVALID_CREDENTIALS':
        return '用户名或密码错误';
      case 'TOKEN_EXPIRED':
        return '登录已过期，请重新登录';
      case 'USER_EXISTS':
        return '用户名已存在';
      case 'VALIDATION_ERROR':
        return '输入信息格式不正确';
      case 'GAME_SERVER_FULL':
        return '游戏服务器已满，请稍后重试';
      case 'NETWORK_ERROR':
        return '网络连接失败，请检查网络设置';
      default:
        return error.message || '操作失败，请重试';
    }
  }
  
  return '未知错误，请联系技术支持';
};

export const showErrorNotification = (error) => {
  const message = handleApiError(error);
  
  // 使用你的通知系统
  // 例如: toast.error(message);
  console.error('API Error:', message);
};
```

### 2. 重试机制

```javascript
// utils/retry.js
export const withRetry = async (fn, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // 不重试的错误类型
      if (error.status === 401 || error.status === 403 || error.status === 400) {
        throw error;
      }
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }
  
  throw lastError;
};

// 使用示例
const loadGameServers = () => withRetry(
  () => gameService.getGameServers(),
  3,
  1000
);
```

## 性能优化建议

### 1. 请求缓存

```javascript
// utils/cache.js
class ApiCache {
  constructor(ttl = 5 * 60 * 1000) { // 5分钟默认TTL
    this.cache = new Map();
    this.ttl = ttl;
  }

  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  clear() {
    this.cache.clear();
  }
}

export const apiCache = new ApiCache();

// 在服务中使用
export const getCachedGameServers = async (filters) => {
  const cacheKey = `game_servers_${JSON.stringify(filters)}`;
  
  let data = apiCache.get(cacheKey);
  if (!data) {
    data = await gameService.getGameServers(filters);
    apiCache.set(cacheKey, data);
  }
  
  return data;
};
```

### 2. 请求去重

```javascript
// utils/deduplication.js
class RequestDeduplicator {
  constructor() {
    this.pendingRequests = new Map();
  }

  async request(key, requestFn) {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }

    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }
}

export const deduplicator = new RequestDeduplicator();
```

---

**相关文档**:
- [完整API文档](./API_DOCUMENTATION.md)
- [快速参考](./API_QUICK_REFERENCE.md)

**版本**: 1.0.0  
**更新日期**: 2025-07-25
