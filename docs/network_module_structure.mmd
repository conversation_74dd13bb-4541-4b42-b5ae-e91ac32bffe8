graph TB

%% 主要类定义
    subgraph "EventLoop 事件循环核心"
        EL["EventLoop<br/>事件循环主控制器"]
        EL_THREAD["threadId_<br/>绑定线程ID"]
        EL_LOOP["looping_<br/>循环状态标志"]
        EL_QUIT["quit_<br/>退出标志"]
        EL_ACTIVE["activeChannels_<br/>活跃通道列表"]
        EL_PENDING["pendingFunctors_<br/>待执行任务队列"]
        EL_MUTEX["mutex_<br/>任务队列保护锁"]
        EL_CALLING["callingPendingFunctors_<br/>任务执行标志"]
    end

    subgraph "Epoll IO多路复用器"
        EP["Epoll<br/>IO事件检测器"]
        EP_FD["epollfd_<br/>epoll文件描述符"]
        EP_EVENTS["events_<br/>事件数组"]
        EP_CHANNELS["channels_<br/>Channel映射表"]
        EP_OWNER["ownerLoop_<br/>所属EventLoop"]
    end

    subgraph "Channel 事件通道"
        CH["Channel<br/>事件处理通道"]
        CH_FD["fd_<br/>文件描述符"]
        CH_EVENTS["events_<br/>关注事件"]
        CH_REVENTS["revents_<br/>实际事件"]
        CH_INDEX["index_<br/>epoll状态"]
        CH_LOOP["loop_<br/>所属EventLoop"]
        CH_READ_CB["readCallback_<br/>读事件回调"]
        CH_WRITE_CB["writeCallback_<br/>写事件回调"]
        CH_CLOSE_CB["closeCallback_<br/>关闭回调"]
        CH_ERROR_CB["errorCallback_<br/>错误回调"]
    end

    subgraph "Wakeup 唤醒机制"
        WU["Wakeup System<br/>线程间通信"]
        WU_FD["wakeupFd_<br/>eventfd文件描述符"]
        WU_CH["wakeupChannel_<br/>唤醒通道"]
        WU_FUNC["wakeup函数<br/>唤醒函数"]
        WU_HANDLE["handleRead函数<br/>读取处理"]
        WU_DO["doPendingFunctors函数<br/>执行任务"]
    end

    subgraph "Socket 网络套接字"
        SK["Socket<br/>网络套接字封装"]
        SK_FD["sockfd_<br/>socket文件描述符"]
        SK_ADDR["InetAddress<br/>网络地址"]
        SK_OPT["TCP Options<br/>TCP选项设置"]
    end

    subgraph "InetAddress 网络地址"
        IA["InetAddress<br/>网络地址封装"]
        IA_ADDR["sockaddr_in<br/>地址结构"]
        IA_IP["IP Address<br/>IP地址"]
        IA_PORT["Port<br/>端口号"]
    end

%% 事件循环主流程
    subgraph "Event Loop Main Flow"
        START0(["开始事件循环"])
        POLL0["epoll_wait<br/>等待IO事件"]
        HANDLE_IO0["处理IO事件<br/>调用Channel回调"]
        HANDLE_PENDING0["处理待执行任务<br/>doPendingFunctors"]
        CHECK_QUIT0{"检查退出标志"}
        END0(["结束事件循环"])
    end

%% 唤醒机制流程
    subgraph "Wakeup Mechanism Flow"
        OTHER_THREAD0["其他线程"]
        RUN_IN_LOOP0["runInLoop"]
        QUEUE_IN_LOOP0["queueInLoop"]
        WAKEUP_CALL0["wakeup"]
        WRITE_EVENTFD0["write eventfd"]
        EPOLL_DETECT0["epoll检测到可读"]
        HANDLE_READ0["handleRead"]
        READ_EVENTFD0["read eventfd"]
        DO_FUNCTORS0["doPendingFunctors"]
        EXECUTE_TASKS0["执行用户任务"]
    end

%% 类之间的关系
    EL --> EP & WU_CH & EL_ACTIVE
    EP --> CH
    CH --> EL
    WU_CH --> WU_FD
    SK --> IA
    CH --> SK

%% 数据流向
    EP_FD -.-> POLL0
    CH_FD -.-> EP_FD
    WU_FD -.-> EP_FD

%% 回调流向
    CH_READ_CB -.-> HANDLE_IO0
    CH_WRITE_CB -.-> HANDLE_IO0
    WU_HANDLE -.-> HANDLE_IO0

%% 任务队列流向
    EL_PENDING -.-> WU_DO
    EL_MUTEX -.-> EL_PENDING
%% 流程连接
    START0 --> POLL0
    POLL0 --> HANDLE_IO0
    HANDLE_IO0 --> HANDLE_PENDING0
    HANDLE_PENDING0 --> CHECK_QUIT0
    CHECK_QUIT0 -->|继续| POLL0
    CHECK_QUIT0 -->|退出| END0

    OTHER_THREAD0 --> RUN_IN_LOOP0
    RUN_IN_LOOP0 --> QUEUE_IN_LOOP0
    QUEUE_IN_LOOP0 --> WAKEUP_CALL0
    WAKEUP_CALL0 --> WRITE_EVENTFD0
    WRITE_EVENTFD0 --> EPOLL_DETECT0
    EPOLL_DETECT0 --> HANDLE_READ0
    HANDLE_READ0 --> READ_EVENTFD0
    READ_EVENTFD0 --> DO_FUNCTORS0
    DO_FUNCTORS0 --> EXECUTE_TASKS0

%% 样式定义
    classDef eventLoop fill:#e1f5fe,stroke:#01579b,stroke-width:2px;
    classDef epoll fill:#f3e5f5,stroke:#4a148c,stroke-width:2px;
    classDef channel fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px;
    classDef wakeup fill:#fff3e0,stroke:#e65100,stroke-width:2px;
    classDef socket fill:#fce4ec,stroke:#880e4f,stroke-width:2px;
    classDef flow fill:#f1f8e9,stroke:#33691e,stroke-width:2px;

    class EL,EL_THREAD,EL_LOOP,EL_QUIT,EL_ACTIVE,EL_PENDING,EL_MUTEX,EL_CALLING eventLoop
    class EP,EP_FD,EP_EVENTS,EP_CHANNELS,EP_OWNER epoll
    class CH,CH_FD,CH_EVENTS,CH_REVENTS,CH_INDEX,CH_LOOP,CH_READ_CB,CH_WRITE_CB,CH_CLOSE_CB,CH_ERROR_CB channel
    class WU,WU_FD,WU_CH,WU_FUNC,WU_HANDLE,WU_DO wakeup
    class SK,SK_FD,SK_ADDR,SK_OPT,IA,IA_ADDR,IA_IP,IA_PORT socket
    class START0,POLL0,HANDLE_IO0,HANDLE_PENDING0,CHECK_QUIT0,END0,OTHER_THREAD0,RUN_IN_LOOP0,QUEUE_IN_LOOP0,WAKEUP_CALL0,WRITE_EVENTFD0,EPOLL_DETECT0,HANDLE_READ0,READ_EVENTFD0,DO_FUNCTORS0,EXECUTE_TASKS0 flow