# API快速参考手册

## 基础信息

- **API网关**: `http://localhost:8080`
- **认证服务**: `http://localhost:8008` (直接访问)
- **Content-Type**: `application/json`

## 认证API

### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "TestPass123!",
  "email": "<EMAIL>"
}
```

### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "TestPass123!"
}

# 响应
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_here",
    "expires_in": 3600,
    "user_info": { ... }
  }
}
```

### 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
Content-Type: application/json

{}
```

### 刷新令牌
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "refresh_token_here"
}
```

### 验证令牌
```http
POST /api/v1/auth/validate
Authorization: Bearer {token}
Content-Type: application/json

{}
```

## 游戏API

### 游戏登录
```http
POST /api/v1/game/login
Authorization: Bearer {token}
Content-Type: application/json

{
  "game_type": "snake",
  "server_preference": "low_latency"
}
```

### 获取游戏服务器列表
```http
GET /api/v1/game/servers?game_type=snake&region=asia
# 无需认证
```

## 服务发现API

### 获取服务列表
```http
GET /api/v1/services?healthy_only=true
```

### 获取服务统计
```http
GET /api/v1/services/stats
```

### 服务注册 (内部使用)
```http
POST /api/v1/services/register
Content-Type: application/json

{
  "service_name": "auth_service",
  "host": "0.0.0.0",
  "port": 8008,
  "service_version": "1.0.0",
  "health_check_endpoint": "/health",
  "endpoints": ["/api/v1/auth/login", ...]
}
```

### 服务心跳 (内部使用)
```http
POST /api/v1/services/heartbeat
Content-Type: application/json

{
  "service_name": "auth_service",
  "host": "0.0.0.0",
  "port": 8008
}
```

## 管理API

### 健康检查
```http
GET /health
# 无需认证
```

### 获取指标
```http
GET /metrics
```

## 常用错误码

| 错误码 | 状态码 | 说明 |
|--------|--------|------|
| INVALID_CREDENTIALS | 401 | 用户名或密码错误 |
| TOKEN_EXPIRED | 401 | 访问令牌已过期 |
| INVALID_TOKEN | 401 | 无效的访问令牌 |
| USER_EXISTS | 400 | 用户已存在 |
| VALIDATION_ERROR | 400 | 参数验证失败 |
| GAME_SERVER_FULL | 503 | 游戏服务器已满 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

## 快速测试

### cURL示例
```bash
# 健康检查
curl http://localhost:8080/health

# 用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"TestPass123!","email":"<EMAIL>"}'

# 用户登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"TestPass123!"}'

# 获取游戏服务器
curl http://localhost:8080/api/v1/game/servers

# 游戏登录 (需要先登录获取token)
curl -X POST http://localhost:8080/api/v1/game/login \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"game_type":"snake","server_preference":"low_latency"}'
```

### Qt/C++示例
```cpp
// 创建API客户端
ApiClient *client = new ApiClient("http://localhost:8080");

// 登录
QJsonObject loginData;
loginData["username"] = "testuser";
loginData["password"] = "TestPass123!";

connect(client, &ApiClient::requestFinished, [](const ApiClient::ApiResponse &response) {
    if (response.success) {
        QString token = response.data["token"].toString();
        client->setToken(token);

        // 游戏登录
        QJsonObject gameData;
        gameData["game_type"] = "snake";
        gameData["server_preference"] = "low_latency";
        client->post("/api/v1/game/login", gameData);
    }
});

client->post("/api/v1/auth/login", loginData);
```

### JavaScript示例
```javascript
// 登录
const loginResponse = await fetch('http://localhost:8080/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'testuser',
    password: 'TestPass123!'
  })
});

const { data } = await loginResponse.json();
const token = data.token;

// 游戏登录
const gameResponse = await fetch('http://localhost:8080/api/v1/game/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    game_type: 'snake',
    server_preference: 'low_latency'
  })
});
```

## 注意事项

1. **令牌过期**: 访问令牌1小时过期，刷新令牌7天过期
2. **请求限制**: 每分钟100个请求
3. **CORS**: 已支持跨域请求
4. **HTTPS**: 生产环境必须使用HTTPS
5. **错误处理**: 检查响应中的`success`字段

## 环境配置

### 开发环境
```bash
API_GATEWAY_URL=http://localhost:8080
AUTH_SERVICE_URL=http://localhost:8008
```

### 生产环境
```bash
API_GATEWAY_URL=https://api.yourdomain.com
AUTH_SERVICE_URL=https://auth.yourdomain.com
```

---

**完整文档**: 参见 [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)  
**版本**: 1.0.0  
**更新日期**: 2025-07-25
