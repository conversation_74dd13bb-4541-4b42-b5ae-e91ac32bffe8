# 网络模块配置热更新设计说明

## 🎯 设计目标

基于现有ConfigManager的热更新机制，为网络模块添加企业级配置热更新支持，实现：
- **零停机配置更新**：大部分配置可在运行时动态更新
- **安全性保障**：危险配置变更需要重启确认
- **状态一致性**：配置更新过程中保持系统状态一致
- **监控和日志**：完整的配置变更审计跟踪

## 🏗️ 架构设计

### 1. 分层热更新架构

```
┌─────────────────────────────────────┐
│     ConfigManager (配置源)           │  ← 文件监控、配置加载
├─────────────────────────────────────┤
│     EventLoop (配置协调层)           │  ← 配置分发、安全检查
├─────────────────────────────────────┤
│     Socket/Epoll (系统层)           │  ← 配置应用、状态更新
└─────────────────────────────────────┘
```

### 2. 配置热更新流程

```mermaid
sequenceDiagram
    participant CF as 配置文件
    participant CM as ConfigManager
    participant EL as EventLoop
    participant EP as Epoll
    participant SK as Socket

    CF->>CM: 文件变更检测
    CM->>CM: 重新加载配置
    CM->>EL: 触发配置变更回调
    EL->>EL: 安全性检查
    EL->>EL: 在IO线程中处理
    EL->>EP: 重新配置Epoll
    EL->>SK: 应用Socket配置
    EL->>EL: 更新内部状态
```

## 🔧 核心功能实现

### 1. EventLoop配置热更新支持

#### 关键方法说明

**`enableNetworkConfigHotReload()`**
- **作用**：启用网络配置热更新监听
- **实现**：注册ConfigManager监听器，监控网络相关配置键
- **使用场景**：服务器启动时调用

**`onNetworkConfigChanged()`**
- **作用**：处理配置变更回调
- **实现**：在IO线程中安全处理配置变更
- **安全机制**：配置验证、安全性检查

**`updateNetworkConfig()`**
- **作用**：应用新的网络配置
- **实现**：分类处理不同类型的配置变更
- **状态保持**：确保配置更新过程中系统状态一致

**`safeReconfigureEpoll()`**
- **作用**：安全地重新配置Epoll
- **实现**：保存现有Channel状态，重建Epoll，恢复状态
- **限制**：只能在EventLoop停止时执行

### 2. 配置分类与热更新支持

| 配置类别 | 热更新支持 | 说明 |
|----------|------------|------|
| **Socket基础配置** | | |
| bind_address | ❌ 需要重启 | 地址变更需要重新绑定 |
| port | ❌ 需要重启 | 端口变更需要重新监听 |
| backlog | ✅ 热更新 | 影响新连接，现有连接不受影响 |
| reuse_address | ✅ 热更新 | 应用到新Socket |
| tcp_no_delay | ✅ 热更新 | 应用到现有和新连接 |
| **Keep-Alive配置** | | |
| keep_alive_* | ✅ 热更新 | 应用到现有和新连接 |
| **Epoll配置** | | |
| epoll_timeout | ✅ 热更新 | 立即生效 |
| max_events | ⚠️ 条件热更新 | EventLoop停止时可更新 |
| resize_factor | ✅ 热更新 | 影响后续扩容 |
| **线程池配置** | | |
| enable_thread_pool | ✅ 热更新 | 立即生效 |
| thread_pool_size | ⚠️ 外部管理 | 需要外部重建线程池 |

### 3. 安全性保障机制

#### 配置变更安全检查
```cpp
bool EventLoop::isConfigChangeSafe(const networkConfig& old_config, 
                                  const networkConfig& new_config) const {
    // 运行时不能变更的配置
    if (looping_.load()) {
        if (old_config.bind_address != new_config.bind_address ||
            old_config.port != new_config.port) {
            return false; // 需要重启
        }
    }
    
    // 配置合理性检查
    if (new_config.max_events < old_config.max_events / 2) {
        LOG_WARNING("Large decrease in max_events detected");
    }
    
    return true;
}
```

#### 线程安全保障
- **IO线程执行**：所有配置变更在EventLoop的IO线程中执行
- **原子操作**：使用`runInLoop()`确保操作的原子性
- **状态一致性**：配置更新过程中保持系统状态一致

## 📋 使用指南

### 1. 基本使用

```cpp
// 创建EventLoop并启用热更新
auto event_loop = std::make_unique<EventLoop>(config);
event_loop->enableNetworkConfigHotReload("config/network.yaml");

// 启动服务器
event_loop->loop();

// 停止时禁用热更新
event_loop->disableNetworkConfigHotReload();
```

### 2. 配置文件格式

```yaml
network:
  # 基础配置
  bind_address: "0.0.0.0"    # 需要重启
  port: 8080                 # 需要重启
  backlog: 1024             # 热更新
  
  # Socket选项
  reuse_address: true       # 热更新
  tcp_no_delay: true        # 热更新
  socket_keep_alive: true   # 热更新
  
  # Keep-Alive参数
  keep_alive_idle_time: 600 # 热更新
  keep_alive_interval: 60   # 热更新
  keep_alive_probes: 3      # 热更新
  
  # Epoll配置
  epoll_timeout: 10000      # 热更新
  max_events: 1024          # 条件热更新
  
  # 性能优化
  epoll_resize_factor: 1.5  # 热更新
```

### 3. 热更新测试

```bash
# 修改配置文件
echo "network.keep_alive_idle_time: 300" >> config/network.yaml

# 观察日志输出
tail -f logs/server.log | grep "config changed"
```

## ⚠️ 注意事项

### 1. 配置变更限制

**需要重启的配置**：
- `bind_address`、`port`：网络绑定相关
- `enable_epoll`：IO多路复用机制变更

**条件热更新的配置**：
- `max_events`、`init_event_list_size`：需要EventLoop停止时更新

### 2. 性能考虑

- **配置检查频率**：默认5秒，可根据需要调整
- **内存使用**：配置监听器会占用少量内存
- **CPU开销**：配置变更时有短暂的CPU开销

### 3. 错误处理

- **配置验证失败**：保持原配置，记录错误日志
- **应用失败**：回滚到原配置，记录详细错误信息
- **文件监控失败**：自动重试，记录警告日志

## 🔍 监控和调试

### 1. 日志输出

```
[INFO] Network config hot reload enabled for file: config/network.yaml
[INFO] Network config changed: network.keep_alive_idle_time = 300 (was: 600)
[INFO] Network configuration updated successfully
[WARNING] Unsafe config change detected for key: network.port, skipping update
```

### 2. 配置变更审计

- **变更记录**：所有配置变更都有详细日志
- **回滚信息**：失败时提供回滚建议
- **性能影响**：记录配置变更对性能的影响

## 🚀 扩展性

### 1. 自定义配置监听

```cpp
// 添加自定义配置监听器
config_manager.addChangeListener("custom.config.key",
    [](const std::string& key, const std::string& old_val, const std::string& new_val) {
        // 自定义处理逻辑
    });
```

### 2. 配置变更钩子

```cpp
class CustomEventLoop : public EventLoop {
protected:
    void onConfigUpdateComplete(const networkConfig& old_config, 
                               const networkConfig& new_config) override {
        // 自定义后处理逻辑
    }
};
```

这种设计确保了网络模块配置热更新的安全性、可靠性和可维护性，符合企业级应用的要求。
