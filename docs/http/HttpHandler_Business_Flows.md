# HttpHandler 业务流程详细分析

## 目录
1. [处理器系统初始化流程](#处理器系统初始化流程)
2. [同步处理器执行流程](#同步处理器执行流程)
3. [异步处理器执行流程](#异步处理器执行流程)
4. [处理器链执行流程](#处理器链执行流程)
5. [路由处理器匹配流程](#路由处理器匹配流程)
6. [函数式处理器执行流程](#函数式处理器执行流程)
7. [错误处理和异常恢复流程](#错误处理和异常恢复流程)

## 处理器系统初始化流程

### 1. 处理器创建和注册流程

**业务场景**：创建HTTP请求处理器并注册到路由系统

**执行步骤**：

#### 步骤1：自定义处理器创建
```cpp
// 调用类：main() 函数或HttpServer
// 使用函数：自定义处理器构造函数
class UserHandler : public HttpHandler {
public:
    UserHandler() : HttpHandler("UserHandler") {}
    
    void handle(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        // 处理用户相关请求
    }
};

auto user_handler = std::make_shared<UserHandler>();
```

**内部执行序列**：
1. `HttpHandler::HttpHandler(name)` - 基类构造函数
   - `name_ = name` - 设置处理器名称
   - `enabled_ = true` - 设置启用状态
2. `UserHandler::UserHandler()` - 派生类构造函数
   - 初始化特定业务逻辑

#### 步骤2：函数式处理器创建
```cpp
// 调用类：main() 函数
// 使用函数：FunctionHandler::FunctionHandler()
auto simple_handler = std::make_shared<FunctionHandler>(
    [](const HttpRequest& req, HttpResponse& res) {
        res.ok("Hello World");
    },
    "SimpleHandler"
);

auto context_handler = std::make_shared<FunctionHandler>(
    [](const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        ctx.set("processed_at", getCurrentTimeString());
        res.ok("Processed with context");
    },
    "ContextHandler"
);
```

**内部执行序列**：
1. `FunctionHandler::FunctionHandler(simple_func, name)`
   - `HttpHandler::HttpHandler(name)` - 调用基类构造函数
   - `simple_func_ = simple_func` - 保存简单处理函数
   - `context_func_ = nullptr` - 上下文函数为空
2. `FunctionHandler::FunctionHandler(context_func, name)`
   - `context_func_ = context_func` - 保存上下文处理函数
   - `simple_func_ = nullptr` - 简单函数为空

#### 步骤3：处理器初始化
```cpp
// 调用类：HttpServer或路由管理器
// 使用函数：HttpHandler::initialize()
bool success = handler->initialize();
```

**内部执行序列**：
1. `HttpHandler::initialize()`
2. 记录初始化日志：`LOG_INFO("Initializing handler: " + name_)`
3. 执行处理器特定初始化逻辑（虚函数，子类可重写）
4. 返回初始化结果

**结果**：处理器创建完成并准备处理请求

### 2. 异步处理器初始化流程

**业务场景**：创建支持异步处理的处理器

**执行步骤**：

#### 步骤1：异步处理器创建
```cpp
// 调用类：main() 函数
// 使用函数：AsyncHttpHandler::AsyncHttpHandler()
class DatabaseHandler : public AsyncHttpHandler {
public:
    DatabaseHandler(std::shared_ptr<ThreadPool> pool) 
        : AsyncHttpHandler("DatabaseHandler", pool) {}
    
    void handleAsync(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        // 异步数据库操作
        auto result = database.queryAsync(req.getParam("id"));
        res.setJsonBody(result.serialize());
        res.ok();
    }
};

auto db_handler = std::make_shared<DatabaseHandler>(thread_pool);
```

**内部执行序列**：
1. `AsyncHttpHandler::AsyncHttpHandler(name, thread_pool)`
   - `HttpHandler::HttpHandler(name)` - 调用基类构造函数
   - `thread_pool_ = thread_pool` - 保存线程池引用
2. `DatabaseHandler::DatabaseHandler(pool)` - 派生类构造函数

#### 步骤2：线程池验证
**执行序列**：
1. `AsyncHttpHandler::initialize()`
2. 检查线程池：`if (!thread_pool_)`
   - 记录错误：`LOG_ERROR("Thread pool not available for async handler")`
   - 返回 `false`
3. 调用基类初始化：`HttpHandler::initialize()`

**结果**：异步处理器初始化完成，准备异步处理请求

## 同步处理器执行流程

### 1. 标准同步处理流程

**业务场景**：处理HTTP请求的标准同步流程

**执行步骤**：

#### 步骤1：处理器调用入口
```cpp
// 调用类：HttpServer或路由器
// 使用函数：HttpHandler::handle()
handler->handle(request, response);
```

**内部执行序列**：
1. `HttpHandler::handle(request, response)` - 简化版本
2. `HttpContext context` - 创建临时上下文
3. `handle(request, response, context)` - 调用完整版本

#### 步骤2：处理前检查
**执行序列**：
1. `HttpHandler::handle(request, response, context)` - 完整版本
2. 检查是否应该处理：`shouldHandle(request)`
   - `return enabled_` - 检查启用状态
   - 子类可重写此方法添加额外检查
3. 如果不应该处理：直接返回

#### 步骤3：异常保护执行
**执行序列**：
1. `try { ... }` - 异常保护块
2. 记录开始时间：`auto start_time = std::chrono::steady_clock::now()`
3. 调用具体处理逻辑：`handleImpl(request, response, context)` - 纯虚函数
4. 记录结束时间和性能统计

#### 步骤4：异常处理
**执行序列**：
1. `catch (const std::exception& e)` - 捕获异常
2. `handleException(e, request, response)` - 调用异常处理
   - 记录错误日志：`LOG_ERROR("Handler error: " + name_ + " - " + e.what())`
   - 设置错误响应：`response.internalServerError("Internal server error")`

**结果**：请求被同步处理完成或异常被正确处理

### 2. 自定义处理器执行流程

**业务场景**：执行自定义业务逻辑处理器

**执行步骤**：

#### 步骤1：用户处理器实现
```cpp
class UserHandler : public HttpHandler {
public:
    void handle(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        std::string path = req.getPath();
        std::string method = req.getMethodString();
        
        if (method == "GET" && path == "/api/users") {
            handleGetUsers(req, res, ctx);
        } else if (method == "POST" && path == "/api/users") {
            handleCreateUser(req, res, ctx);
        } else {
            res.notFound("Endpoint not found");
        }
    }
    
private:
    void handleGetUsers(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        // 获取用户列表业务逻辑
    }
    
    void handleCreateUser(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        // 创建用户业务逻辑
    }
};
```

#### 步骤2：业务逻辑执行
**GET /api/users 请求处理序列**：
1. `UserHandler::handle(request, response, context)`
2. `std::string path = req.getPath()` - 获取请求路径
3. `std::string method = req.getMethodString()` - 获取请求方法
4. 路径和方法匹配：`method == "GET" && path == "/api/users"`
5. `handleGetUsers(req, res, ctx)` - 调用具体业务方法

**handleGetUsers 内部执行**：
1. 验证请求参数：`validateGetUsersParams(req)`
2. 从数据库获取用户：`auto users = userService.getAllUsers()`
3. 序列化响应：`std::string json = serializeUsers(users)`
4. 设置响应：`res.setJsonBody(json)`
5. 设置成功状态：`res.ok()`

**结果**：用户列表被成功返回

## 异步处理器执行流程

### 1. 异步处理器执行流程

**业务场景**：在线程池中异步处理耗时的HTTP请求

**执行步骤**：

#### 步骤1：异步处理入口
```cpp
// 调用类：HttpServer
// 使用函数：AsyncHttpHandler::handle()
async_handler->handle(request, response, context);
```

**内部执行序列**：
1. `AsyncHttpHandler::handle(request, response, context)`
2. 检查线程池可用性：`if (!thread_pool_)`
   - 记录错误：`LOG_ERROR("Thread pool not available")`
   - 设置错误响应：`response.internalServerError("Service unavailable")`
   - 返回

#### 步骤2：提交异步任务
**执行序列**：
1. 创建异步任务：
```cpp
auto task = [this, &request, &response, &context]() {
    try {
        handleAsync(request, response, context);
    } catch (const std::exception& e) {
        handleException(e, request, response);
    }
};
```
2. `auto future = thread_pool_->submit(task)` - 提交任务到线程池
3. `future.wait()` - 等待任务完成（同步等待异步执行）

#### 步骤3：异步业务逻辑执行
**在工作线程中执行**：
1. `AsyncHttpHandler::handleAsync(request, response, context)` - 纯虚函数
2. 执行具体的异步业务逻辑（子类实现）
3. 设置响应结果

#### 步骤4：异步任务完成
**执行序列**：
1. 工作线程完成任务执行
2. `future.wait()` 返回 - 主线程继续执行
3. 检查响应状态和结果
4. 记录异步处理统计信息

**结果**：请求在后台线程中被异步处理完成

### 2. 数据库异步处理示例

**业务场景**：异步查询数据库并返回结果

**执行步骤**：

#### 步骤1：数据库处理器实现
```cpp
class DatabaseHandler : public AsyncHttpHandler {
public:
    void handleAsync(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        std::string user_id = req.getParam("id");
        
        // 异步数据库查询
        auto user_future = database.findUserAsync(user_id);
        auto orders_future = database.findUserOrdersAsync(user_id);
        
        // 等待所有异步操作完成
        auto user = user_future.get();
        auto orders = orders_future.get();
        
        // 构建响应
        json response_data;
        response_data["user"] = user.toJson();
        response_data["orders"] = orders.toJson();
        
        res.setJsonBody(response_data.dump());
        res.ok();
    }
};
```

#### 步骤2：并发数据库操作
**执行序列**：
1. `database.findUserAsync(user_id)` - 启动用户查询
2. `database.findUserOrdersAsync(user_id)` - 启动订单查询
3. `user_future.get()` - 等待用户查询完成
4. `orders_future.get()` - 等待订单查询完成
5. 合并结果并构建响应

**结果**：多个数据库操作并发执行，提高处理效率

## 处理器链执行流程

### 1. 处理器链创建和执行流程

**业务场景**：多个处理器按顺序处理同一个请求

**执行步骤**：

#### 步骤1：处理器链创建
```cpp
// 调用类：main() 函数
// 使用函数：HandlerChain::HandlerChain()
auto chain = std::make_shared<HandlerChain>("UserProcessingChain");

// 添加处理器到链中
chain->addHandler(std::make_shared<ValidationHandler>());
chain->addHandler(std::make_shared<AuthenticationHandler>());
chain->addHandler(std::make_shared<BusinessLogicHandler>());
chain->addHandler(std::make_shared<ResponseFormatterHandler>());
```

**内部执行序列**：
1. `HandlerChain::HandlerChain(name)` - 创建处理器链
   - `HttpHandler::HttpHandler(name)` - 调用基类构造函数
   - `handlers_.clear()` - 初始化处理器列表
2. `HandlerChain::addHandler(handler)` - 添加处理器
   - `handlers_.push_back(handler)` - 添加到列表末尾
   - `LOG_INFO("Added handler to chain: " + handler->getName())`

#### 步骤2：处理器链执行
```cpp
// 调用类：HttpServer
// 使用函数：HandlerChain::handle()
chain->handle(request, response, context);
```

**内部执行序列**：
1. `HandlerChain::handle(request, response, context)`
2. 遍历处理器列表：`for (auto& handler : handlers_)`
3. 检查是否应该执行：`handler->shouldHandle(request)`
4. 执行处理器：`handler->handle(request, response, context)`
5. 检查响应状态：
   - 如果响应已完成（状态码已设置）：停止链执行
   - 否则：继续执行下一个处理器

#### 步骤3：链式处理示例
**请求处理序列**：
1. `ValidationHandler::handle()` - 验证请求参数
   - 验证必需参数
   - 验证参数格式
   - 如果验证失败：设置400错误响应，停止链执行
2. `AuthenticationHandler::handle()` - 验证用户身份
   - 检查认证令牌
   - 验证用户权限
   - 如果认证失败：设置401错误响应，停止链执行
3. `BusinessLogicHandler::handle()` - 执行业务逻辑
   - 处理核心业务逻辑
   - 设置响应数据
4. `ResponseFormatterHandler::handle()` - 格式化响应
   - 格式化响应数据
   - 设置响应头
   - 设置最终状态码

**结果**：请求通过处理器链被逐步处理完成

### 2. 处理器链管理流程

**业务场景**：动态管理处理器链中的处理器

**执行步骤**：

#### 步骤1：动态添加处理器
```cpp
// 调用类：运行时配置管理
// 使用函数：HandlerChain::addHandler()
auto new_handler = std::make_shared<LoggingHandler>();
chain->addHandler(new_handler);
```

#### 步骤2：移除处理器
```cpp
// 调用类：运行时配置管理
// 使用函数：HandlerChain::removeHandler()
chain->removeHandler("LoggingHandler");
```

**内部执行序列**：
1. `HandlerChain::removeHandler(name)`
2. 查找处理器：
```cpp
auto it = std::find_if(handlers_.begin(), handlers_.end(),
    [&name](const auto& handler) {
        return handler->getName() == name;
    });
```
3. 如果找到：`handlers_.erase(it)` - 从列表中移除
4. 记录日志：`LOG_INFO("Removed handler from chain: " + name)`

#### 步骤3：清空处理器链
```cpp
// 调用类：系统关闭或重置
// 使用函数：HandlerChain::clear()
chain->clear();
```

**内部执行序列**：
1. `HandlerChain::clear()`
2. `handlers_.clear()` - 清空处理器列表
3. 记录日志：`LOG_INFO("Cleared handler chain")`

**结果**：处理器链被动态管理，支持运行时配置

## 路由处理器匹配流程

### 1. 路由处理器创建和匹配流程

**业务场景**：根据请求路径匹配相应的处理器

**执行步骤**：

#### 步骤1：路由处理器创建
```cpp
// 调用类：路由配置
// 使用函数：RouteHandler::RouteHandler()
auto user_handler = std::make_shared<UserHandler>();
auto route_handler = std::make_shared<RouteHandler>("/api/users", user_handler);
```

**内部执行序列**：
1. `RouteHandler::RouteHandler(pattern, handler)`
   - `HttpHandler::HttpHandler("RouteHandler_" + pattern)` - 调用基类构造函数
   - `pattern_ = pattern` - 保存路径模式
   - `handler_ = handler` - 保存实际处理器

#### 步骤2：路径匹配检查
**请求示例**：`GET /api/users/123`

**执行序列**：
1. `RouteHandler::shouldHandle(request)`
2. `std::string path = request.getPath()` - 获取请求路径
3. 执行路径匹配：`path.starts_with(pattern_)` - 前缀匹配
4. 返回匹配结果：`true` 或 `false`

#### 步骤3：委托处理
**执行序列**：
1. 如果路径匹配：`RouteHandler::handle(request, response, context)`
2. 委托给实际处理器：`handler_->handle(request, response, context)`
3. 返回实际处理器的处理结果

**结果**：请求被路由到正确的处理器

### 2. 复杂路由匹配流程

**业务场景**：支持参数化路由和正则表达式匹配

**执行步骤**：

#### 步骤1：参数化路由处理器
```cpp
class ParameterizedRouteHandler : public RouteHandler {
public:
    ParameterizedRouteHandler(const std::string& pattern, std::shared_ptr<HttpHandler> handler)
        : RouteHandler(pattern, handler) {
        // 解析路径参数模式
        parsePathParameters(pattern);
    }
    
    bool shouldHandle(const HttpRequest& request) const override {
        return matchPathWithParameters(request.getPath());
    }
    
    void handle(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        // 提取路径参数
        extractPathParameters(req.getPath(), ctx);
        // 委托给实际处理器
        handler_->handle(req, res, ctx);
    }
};
```

#### 步骤2：路径参数提取
**请求示例**：`GET /api/users/123/orders/456`
**路径模式**：`/api/users/:userId/orders/:orderId`

**执行序列**：
1. `matchPathWithParameters("/api/users/123/orders/456")`
2. 分割路径段：`split(path, '/')` 和 `split(pattern, '/')`
3. 逐段匹配：
   - `/api` 匹配 `/api` ✓
   - `users` 匹配 `users` ✓
   - `123` 匹配 `:userId` ✓ (参数)
   - `orders` 匹配 `orders` ✓
   - `456` 匹配 `:orderId` ✓ (参数)
4. `extractPathParameters()` - 提取参数值
   - `ctx.set("userId", "123")`
   - `ctx.set("orderId", "456")`

**结果**：路径参数被正确提取并存储在上下文中

## 函数式处理器执行流程

### 1. 简单函数处理器执行流程

**业务场景**：使用简单函数作为请求处理器

**执行步骤**：

#### 步骤1：简单函数处理器创建
```cpp
// 调用类：路由配置
// 使用函数：FunctionHandler::FunctionHandler()
auto simple_handler = std::make_shared<FunctionHandler>(
    [](const HttpRequest& req, HttpResponse& res) {
        std::string name = req.getParam("name", "World");
        res.ok("Hello " + name + "!");
    },
    "GreetingHandler"
);
```

#### 步骤2：简单函数执行
**执行序列**：
1. `FunctionHandler::handle(request, response, context)`
2. 检查函数类型：`if (simple_func_)` - 有简单函数
3. 调用简单函数：`simple_func_(request, response)`
4. 异常处理：`try { ... } catch { handleException(...) }`

**结果**：简单函数被执行，响应被设置

### 2. 上下文函数处理器执行流程

**业务场景**：使用带上下文的函数作为请求处理器

**执行步骤**：

#### 步骤1：上下文函数处理器创建
```cpp
// 调用类：路由配置
// 使用函数：FunctionHandler::FunctionHandler()
auto context_handler = std::make_shared<FunctionHandler>(
    [](const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        // 记录处理开始时间
        ctx.set("start_time", getCurrentTimeString());
        
        // 处理业务逻辑
        std::string result = processBusinessLogic(req);
        
        // 记录处理结束时间
        ctx.set("end_time", getCurrentTimeString());
        ctx.set("processing_duration", std::to_string(ctx.getElapsedMs()));
        
        res.setJsonBody(result);
        res.ok();
    },
    "BusinessHandler"
);
```

#### 步骤2：上下文函数执行
**执行序列**：
1. `FunctionHandler::handle(request, response, context)`
2. 检查函数类型：`if (context_func_)` - 有上下文函数
3. 调用上下文函数：`context_func_(request, response, context)`
4. 上下文数据在函数中被设置和使用

**结果**：带上下文的函数被执行，上下文数据被正确传递

## 错误处理和异常恢复流程

### 1. 处理器异常处理流程

**业务场景**：处理器执行过程中发生异常

**执行步骤**：

#### 步骤1：异常捕获
**执行序列**：
1. `HttpHandler::handle()` 中的异常保护
2. `try { handleImpl(request, response, context); }` - 执行处理逻辑
3. `catch (const std::exception& e)` - 捕获标准异常
4. `catch (...)` - 捕获所有其他异常

#### 步骤2：异常处理
**执行序列**：
1. `HttpHandler::handleException(e, request, response)`
2. 记录错误日志：
```cpp
LOG_ERROR("Handler exception in " + name_ + ": " + e.what() + 
          " for request: " + request.getMethodString() + " " + request.getPath());
```
3. 设置错误响应：
   - `response.internalServerError("Internal server error")`
   - 设置错误详情头：`response.setHeader("X-Error-Type", "HandlerException")`

#### 步骤3：异常统计
**执行序列**：
1. 更新异常统计计数器
2. 记录异常发生时间
3. 如果配置启用：发送异常告警

**结果**：异常被正确处理，系统保持稳定

### 2. 自定义错误处理流程

**业务场景**：实现自定义的错误处理逻辑

**执行步骤**：

#### 步骤1：自定义错误处理器
```cpp
class CustomErrorHandler : public HttpHandler {
public:
    void handle(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        try {
            // 尝试处理请求
            processRequest(req, res, ctx);
        } catch (const ValidationException& e) {
            handleValidationError(e, req, res, ctx);
        } catch (const AuthenticationException& e) {
            handleAuthError(e, req, res, ctx);
        } catch (const BusinessException& e) {
            handleBusinessError(e, req, res, ctx);
        }
    }
    
private:
    void handleValidationError(const ValidationException& e, 
                             const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        res.badRequest(e.what());
        ctx.set("error_type", "validation");
    }
    
    void handleAuthError(const AuthenticationException& e,
                        const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        res.unauthorized(e.what());
        ctx.set("error_type", "authentication");
    }
    
    void handleBusinessError(const BusinessException& e,
                           const HttpRequest& req, HttpResponse& res, HttpContext& ctx) {
        res.internalServerError(e.what());
        ctx.set("error_type", "business");
    }
};
```

#### 步骤2：分类错误处理
**执行序列**：
1. 根据异常类型执行不同的处理逻辑
2. 设置相应的HTTP状态码
3. 在上下文中记录错误类型
4. 记录详细的错误日志

**结果**：不同类型的错误得到适当的处理

## 总结

HttpHandler系统的业务流程体现了现代Web应用的核心处理模式：

1. **灵活的处理器模式**：支持自定义、函数式、异步等多种处理器类型
2. **责任链支持**：支持处理器链式调用和路由匹配
3. **异步处理能力**：集成线程池支持异步处理
4. **完善的错误处理**：多层次的异常处理和错误恢复机制
5. **上下文传递**：在处理过程中传递和共享上下文数据
6. **性能监控**：内置处理时间统计和性能监控
7. **模块化设计**：与日志、线程池、配置等模块深度集成

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。

## 外部模块依赖关系

### 1. 核心依赖模块
- **HttpRequest/HttpResponse**: 请求和响应对象的处理
- **Logger**: 统一的日志记录系统
- **ThreadPool**: 异步处理的线程池支持
- **ConfigManager**: 配置管理和热更新

### 2. 标准库依赖
- **std::functional**: 函数对象和回调机制
- **std::memory**: 智能指针管理
- **std::chrono**: 时间测量和性能统计
- **std::atomic**: 线程安全的状态管理

### 3. 集成特性
- **与HttpServer集成**: 作为请求处理的核心组件
- **与HttpRouter集成**: 支持路由分发到处理器
- **与HttpMiddleware集成**: 支持中间件链式处理
- **与监控系统集成**: 提供处理性能指标
