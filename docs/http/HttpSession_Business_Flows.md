# HttpSession 业务流程详细分析

## 目录
1. [会话创建和初始化流程](#会话创建和初始化流程)
2. [会话状态管理流程](#会话状态管理流程)
3. [HTTP请求读取流程](#HTTP请求读取流程)
4. [HTTP响应发送流程](#HTTP响应发送流程)
5. [Keep-Alive连接管理流程](#Keep-Alive连接管理流程)
6. [会话超时管理流程](#会话超时管理流程)
7. [会话关闭和清理流程](#会话关闭和清理流程)

## 会话创建和初始化流程

### 1. 会话实例创建流程

**业务场景**：为新的HTTP连接创建会话实例

**执行步骤**：

#### 步骤1：会话对象创建
```cpp
// 调用类：HttpServer
// 使用函数：HttpSession::HttpSession()
auto session = std::make_shared<HttpSession>(client_fd, client_addr, event_loop);
```

**内部执行序列**：
1. `HttpSession::HttpSession(fd, addr, loop)` - 构造函数
2. `fd_ = fd` - 保存文件描述符
3. `client_addr_ = addr` - 保存客户端地址
4. `event_loop_ = loop` - 保存事件循环引用
5. `session_id_ = generateSessionId()` - 生成唯一会话ID
6. `state_ = HttpSessionState::CONNECTING` - 设置初始状态
7. `stats_ = HttpSessionStats()` - 初始化统计信息
8. `created_time_ = std::chrono::steady_clock::now()` - 记录创建时间

#### 步骤2：网络通道创建
**执行序列**：
1. `channel_ = std::make_unique<Channel>(event_loop_, fd_)` - 创建网络通道
2. 设置事件回调函数：
   - `channel_->setReadCallback([this] { handleRead(); })` - 读事件回调
   - `channel_->setWriteCallback([this] { handleWrite(); })` - 写事件回调
   - `channel_->setCloseCallback([this] { handleClose(); })` - 关闭事件回调
   - `channel_->setErrorCallback([this] { handleError(); })` - 错误事件回调

#### 步骤3：会话配置初始化
**执行序列**：
1. 设置默认配置：
   - `keep_alive_timeout_ = 60` - Keep-Alive超时时间（秒）
   - `request_timeout_ = 30` - 请求超时时间（秒）
   - `max_request_size_ = 1024 * 1024` - 最大请求大小（1MB）
   - `max_header_size_ = 8192` - 最大头部大小（8KB）
2. 初始化缓冲区：
   - `read_buffer_.clear()` - 清空读缓冲区
   - `write_buffer_.clear()` - 清空写缓冲区
3. 初始化解析器：`request_parser_.reset()` - 重置请求解析器

**结果**：HTTP会话实例创建完成，准备开始处理连接

### 2. 会话启动流程

**业务场景**：启动会话开始处理HTTP连接

**执行步骤**：

#### 步骤1：会话启动
```cpp
// 调用类：HttpServer
// 使用函数：HttpSession::start()
session->start();
```

**内部执行序列**：
1. `HttpSession::start()`
2. 状态转换：`setState(HttpSessionState::READING)` - 设置为读取状态
3. 启用读事件：`channel_->enableReading()` - 开始监听读事件
4. 设置超时定时器：`setTimer(request_timeout_)` - 设置请求超时
5. 记录启动日志：`LOG_INFO("HttpSession started: " + session_id_)`
6. 更新统计信息：`stats_.updateActivity()` - 更新活动时间

#### 步骤2：Socket配置优化
**执行序列**：
1. 设置Socket选项：
   - `setNonBlocking(fd_)` - 设置非阻塞模式
   - `setTcpNoDelay(fd_)` - 禁用Nagle算法
   - `setKeepAlive(fd_)` - 启用TCP Keep-Alive
   - `setReuseAddr(fd_)` - 启用地址重用
2. 设置缓冲区大小：
   - `setSendBufferSize(fd_, 64 * 1024)` - 设置发送缓冲区64KB
   - `setReceiveBufferSize(fd_, 64 * 1024)` - 设置接收缓冲区64KB

#### 步骤3：会话注册
**执行序列**：
1. 注册到会话管理器：`session_manager_->addSession(session_id_, shared_from_this())`
2. 更新连接计数：`connection_count_++`
3. 记录连接日志：`LOG_INFO("New connection from " + client_addr_.toString())`

**结果**：会话启动完成，开始监听客户端请求

## 会话状态管理流程

### 1. 状态转换流程

**业务场景**：管理HTTP会话的状态转换

**执行步骤**：

#### 步骤1：状态转换定义
**状态转换图**：
```
CONNECTING → READING → PROCESSING → WRITING → KEEP_ALIVE → READING
                                           ↓
                                      CLOSING → CLOSED
```

#### 步骤2：状态转换实现
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::setState()
void HttpSession::setState(HttpSessionState new_state) {
    // 状态转换逻辑
}
```

**内部执行序列**：
1. `HttpSession::setState(new_state)`
2. 获取当前状态：`HttpSessionState old_state = state_`
3. 验证状态转换：`if (!isValidTransition(old_state, new_state))`
   - 如果无效：记录错误日志并返回
4. 更新状态：`state_ = new_state`
5. 记录状态变化：`LOG_DEBUG("Session state: " + stateToString(old_state) + " -> " + stateToString(new_state))`
6. 执行状态特定操作：`onStateChanged(old_state, new_state)`

#### 步骤3：状态特定操作
**执行序列**：
1. `onStateChanged(old_state, new_state)` - 状态变化处理
2. 根据新状态执行操作：
   - `READING`：启用读事件，设置读超时
   - `PROCESSING`：禁用读事件，启动处理定时器
   - `WRITING`：启用写事件，设置写超时
   - `KEEP_ALIVE`：设置Keep-Alive定时器
   - `CLOSING`：开始关闭流程
   - `CLOSED`：清理资源

**结果**：会话状态被正确管理和转换

### 2. 状态验证流程

**业务场景**：验证状态转换的合法性

**执行步骤**：

#### 步骤1：状态转换验证
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::isValidTransition()
bool isValidTransition(HttpSessionState from, HttpSessionState to) {
    // 验证状态转换合法性
}
```

**内部执行序列**：
1. `isValidTransition(from, to)`
2. 定义合法转换表：
```cpp
static const std::map<HttpSessionState, std::set<HttpSessionState>> valid_transitions = {
    {CONNECTING, {READING, CLOSING}},
    {READING, {PROCESSING, CLOSING}},
    {PROCESSING, {WRITING, CLOSING}},
    {WRITING, {KEEP_ALIVE, CLOSING}},
    {KEEP_ALIVE, {READING, CLOSING}},
    {CLOSING, {CLOSED}},
    {CLOSED, {}}
};
```
3. 查找合法转换：`auto it = valid_transitions.find(from)`
4. 检查目标状态：`return it->second.count(to) > 0`

**结果**：状态转换的合法性得到验证

## HTTP请求读取流程

### 1. 请求数据读取流程

**业务场景**：从客户端Socket读取HTTP请求数据

**执行步骤**：

#### 步骤1：读事件触发
**执行序列**：
1. 客户端发送HTTP请求数据
2. Socket变为可读状态
3. 事件循环检测到读事件
4. 调用读事件回调：`handleRead()`

#### 步骤2：数据读取处理
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::handleRead()
void HttpSession::handleRead() {
    // 处理读事件
}
```

**内部执行序列**：
1. `HttpSession::handleRead()`
2. 检查会话状态：`if (state_ != HttpSessionState::READING)`
3. 从Socket读取数据：
```cpp
char buffer[4096];
ssize_t n = ::read(fd_, buffer, sizeof(buffer));
```
4. 处理读取结果：
   - 如果 `n > 0`：数据读取成功
   - 如果 `n == 0`：连接关闭
   - 如果 `n < 0`：读取错误

#### 步骤3：数据缓冲处理
**执行序列**：
1. 将数据追加到读缓冲区：`read_buffer_.append(buffer, n)`
2. 更新统计信息：`stats_.bytes_received += n`
3. 更新活动时间：`stats_.updateActivity()`
4. 检查缓冲区大小：`if (read_buffer_.size() > max_request_size_)`
   - 如果超过限制：发送413错误响应

#### 步骤4：请求完整性检查
**执行序列**：
1. 检查请求是否完整：`if (isRequestComplete(read_buffer_))`
2. 如果完整：开始解析请求
3. 如果不完整：继续等待更多数据
4. 重置读超时定时器：`resetTimer(request_timeout_)`

**结果**：HTTP请求数据被成功读取到缓冲区

### 2. HTTP请求解析流程

**业务场景**：解析读取到的HTTP请求数据

**执行步骤**：

#### 步骤1：请求解析启动
**执行序列**：
1. 状态转换：`setState(HttpSessionState::PROCESSING)`
2. 创建请求对象：`current_request_ = std::make_shared<HttpRequest>()`
3. 开始解析：`parseRequest(read_buffer_)`

#### 步骤2：请求行解析
**执行序列**：
1. `parseRequestLine(read_buffer_)` - 解析请求行
2. 提取HTTP方法：`parseMethod("GET /api/users HTTP/1.1")`
3. 提取请求路径：`parsePath("/api/users")`
4. 提取HTTP版本：`parseVersion("HTTP/1.1")`
5. 验证请求行格式：`validateRequestLine()`

#### 步骤3：请求头解析
**执行序列**：
1. `parseHeaders(read_buffer_)` - 解析请求头
2. 逐行解析头部：
```cpp
while (getline(read_buffer_, line) && !line.empty()) {
    auto pos = line.find(':');
    std::string name = line.substr(0, pos);
    std::string value = line.substr(pos + 1);
    current_request_->setHeader(trim(name), trim(value));
}
```
3. 验证必需头部：`validateRequiredHeaders()`

#### 步骤4：请求体解析
**执行序列**：
1. 检查Content-Length头：`int content_length = current_request_->getContentLength()`
2. 如果有请求体：
   - 检查数据完整性：`if (body_data.size() >= content_length)`
   - 设置请求体：`current_request_->setBody(body_data)`
3. 解析完成：`onRequestParsed(current_request_)`

**结果**：HTTP请求被成功解析为HttpRequest对象

## HTTP响应发送流程

### 1. 响应数据准备流程

**业务场景**：准备HTTP响应数据并发送给客户端

**执行步骤**：

#### 步骤1：响应处理启动
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::sendResponse()
session->sendResponse(response);
```

**内部执行序列**：
1. `HttpSession::sendResponse(response)`
2. 状态转换：`setState(HttpSessionState::WRITING)`
3. 序列化响应：`std::string response_data = response->toString()`
4. 添加到写缓冲区：`write_buffer_.append(response_data)`

#### 步骤2：响应头部处理
**执行序列**：
1. 设置默认头部：
   - `response->setHeader("Server", "GameMicroservices/1.0")`
   - `response->setHeader("Date", getCurrentTimeString())`
   - `response->setHeader("Connection", getConnectionHeader())`
2. 处理Keep-Alive头部：
```cpp
if (shouldKeepAlive()) {
    response->setHeader("Connection", "keep-alive");
    response->setHeader("Keep-Alive", "timeout=" + std::to_string(keep_alive_timeout_));
} else {
    response->setHeader("Connection", "close");
}
```

#### 步骤3：响应体处理
**执行序列**：
1. 设置Content-Length：`response->setHeader("Content-Length", std::to_string(body.length()))`
2. 处理分块传输：`if (use_chunked_encoding)`
3. 处理压缩：`if (should_compress)`

**结果**：HTTP响应数据准备完成

### 2. 响应数据发送流程

**业务场景**：将响应数据发送到客户端Socket

**执行步骤**：

#### 步骤1：写事件处理
**执行序列**：
1. 启用写事件：`channel_->enableWriting()`
2. 事件循环检测到写事件
3. 调用写事件回调：`handleWrite()`

#### 步骤2：数据发送处理
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::handleWrite()
void HttpSession::handleWrite() {
    // 处理写事件
}
```

**内部执行序列**：
1. `HttpSession::handleWrite()`
2. 检查写缓冲区：`if (write_buffer_.empty())`
3. 发送数据：
```cpp
ssize_t n = ::write(fd_, write_buffer_.data(), write_buffer_.size());
```
4. 处理发送结果：
   - 如果 `n > 0`：部分或全部数据发送成功
   - 如果 `n == 0`：Socket缓冲区满
   - 如果 `n < 0`：发送错误

#### 步骤3：发送完成处理
**执行序列**：
1. 更新写缓冲区：`write_buffer_.erase(0, n)` - 移除已发送数据
2. 更新统计信息：`stats_.bytes_sent += n`
3. 检查发送完成：`if (write_buffer_.empty())`
   - 禁用写事件：`channel_->disableWriting()`
   - 调用发送完成回调：`onResponseSent()`

#### 步骤4：连接状态处理
**执行序列**：
1. 检查连接类型：`if (shouldKeepAlive())`
2. 如果Keep-Alive：
   - 状态转换：`setState(HttpSessionState::KEEP_ALIVE)`
   - 重置会话：`resetForNextRequest()`
3. 如果关闭连接：
   - 状态转换：`setState(HttpSessionState::CLOSING)`
   - 开始关闭流程：`startClose()`

**结果**：HTTP响应被成功发送给客户端

## Keep-Alive连接管理流程

### 1. Keep-Alive检测流程

**业务场景**：检测和管理HTTP Keep-Alive长连接

**执行步骤**：

#### 步骤1：Keep-Alive支持检测
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::shouldKeepAlive()
bool shouldKeepAlive() const {
    // 检测是否应该保持连接
}
```

**内部执行序列**：
1. `shouldKeepAlive()`
2. 检查HTTP版本：`if (current_request_->getVersion() == "HTTP/1.0")`
   - HTTP/1.0：检查 `Connection: keep-alive` 头部
3. 检查HTTP/1.1：`if (current_request_->getVersion() == "HTTP/1.1")`
   - HTTP/1.1：默认Keep-Alive，除非 `Connection: close`
4. 检查服务器配置：`if (!config_.enable_keep_alive)`
5. 检查连接状态：`if (error_count_ > max_errors_)`

#### 步骤2：Keep-Alive状态设置
**执行序列**：
1. 响应发送完成后：`onResponseSent()`
2. 如果支持Keep-Alive：
   - 状态转换：`setState(HttpSessionState::KEEP_ALIVE)`
   - 设置Keep-Alive定时器：`setKeepAliveTimer(keep_alive_timeout_)`
   - 重置请求解析器：`request_parser_.reset()`
   - 清空读缓冲区：`read_buffer_.clear()`

#### 步骤3：Keep-Alive等待
**执行序列**：
1. 启用读事件：`channel_->enableReading()` - 等待下一个请求
2. 监听定时器事件：等待Keep-Alive超时
3. 如果收到新请求：
   - 取消定时器：`cancelKeepAliveTimer()`
   - 状态转换：`setState(HttpSessionState::READING)`
   - 开始处理新请求

**结果**：Keep-Alive连接被正确管理

### 2. Keep-Alive超时处理流程

**业务场景**：处理Keep-Alive连接超时

**执行步骤**：

#### 步骤1：超时定时器设置
**执行序列**：
1. 创建定时器：`keep_alive_timer_ = std::make_unique<Timer>(event_loop_)`
2. 设置超时时间：`keep_alive_timer_->setTimeout(keep_alive_timeout_)`
3. 设置超时回调：`keep_alive_timer_->setCallback([this] { onKeepAliveTimeout(); })`
4. 启动定时器：`keep_alive_timer_->start()`

#### 步骤2：超时处理
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::onKeepAliveTimeout()
void HttpSession::onKeepAliveTimeout() {
    // 处理Keep-Alive超时
}
```

**内部执行序列**：
1. `onKeepAliveTimeout()`
2. 检查会话状态：`if (state_ != HttpSessionState::KEEP_ALIVE)`
3. 记录超时日志：`LOG_INFO("Keep-Alive timeout for session: " + session_id_)`
4. 状态转换：`setState(HttpSessionState::CLOSING)`
5. 开始关闭连接：`startClose()`

**结果**：Keep-Alive超时被正确处理

## 会话超时管理流程

### 1. 请求超时管理流程

**业务场景**：管理HTTP请求处理超时

**执行步骤**：

#### 步骤1：请求超时设置
**执行序列**：
1. 请求开始时设置定时器：`setRequestTimer(request_timeout_)`
2. 创建请求定时器：`request_timer_ = std::make_unique<Timer>(event_loop_)`
3. 设置超时回调：`request_timer_->setCallback([this] { onRequestTimeout(); })`

#### 步骤2：超时重置
**执行序列**：
1. 收到数据时重置定时器：`resetRequestTimer()`
2. 请求处理完成时取消定时器：`cancelRequestTimer()`

#### 步骤3：请求超时处理
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::onRequestTimeout()
void HttpSession::onRequestTimeout() {
    // 处理请求超时
}
```

**内部执行序列**：
1. `onRequestTimeout()`
2. 记录超时日志：`LOG_WARN("Request timeout for session: " + session_id_)`
3. 发送超时响应：`sendTimeoutResponse()`
4. 状态转换：`setState(HttpSessionState::CLOSING)`
5. 开始关闭连接：`startClose()`

**结果**：请求超时被正确处理

## 会话关闭和清理流程

### 1. 会话关闭流程

**业务场景**：安全地关闭HTTP会话连接

**执行步骤**：

#### 步骤1：关闭启动
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::close()
session->close();
```

**内部执行序列**：
1. `HttpSession::close()`
2. 状态转换：`setState(HttpSessionState::CLOSING)`
3. 取消所有定时器：`cancelAllTimers()`
4. 禁用所有事件：`channel_->disableAll()`

#### 步骤2：数据发送完成
**执行序列**：
1. 检查写缓冲区：`if (!write_buffer_.empty())`
2. 如果有待发送数据：等待发送完成
3. 发送完成后：继续关闭流程

#### 步骤3：连接关闭
**执行序列**：
1. 关闭Socket：`::close(fd_)`
2. 状态转换：`setState(HttpSessionState::CLOSED)`
3. 记录关闭日志：`LOG_INFO("Session closed: " + session_id_)`

#### 步骤4：资源清理
**执行序列**：
1. 清理缓冲区：
   - `read_buffer_.clear()`
   - `write_buffer_.clear()`
2. 清理定时器：
   - `request_timer_.reset()`
   - `keep_alive_timer_.reset()`
3. 清理网络通道：`channel_.reset()`
4. 从会话管理器移除：`session_manager_->removeSession(session_id_)`

**结果**：会话被安全关闭，所有资源被正确清理

### 2. 异常关闭处理流程

**业务场景**：处理异常情况下的会话关闭

**执行步骤**：

#### 步骤1：错误检测
**执行序列**：
1. 网络错误：`handleError()` 被调用
2. 解析错误：请求解析失败
3. 超时错误：各种超时情况

#### 步骤2：错误处理
```cpp
// 调用类：HttpSession
// 使用函数：HttpSession::handleError()
void HttpSession::handleError() {
    // 处理错误
}
```

**内部执行序列**：
1. `handleError()`
2. 记录错误日志：`LOG_ERROR("Session error: " + getLastError())`
3. 更新错误统计：`stats_.errors++`
4. 发送错误响应（如果可能）：`sendErrorResponse()`
5. 强制关闭连接：`forceClose()`

#### 步骤3：强制清理
**执行序列**：
1. 立即关闭Socket：`::close(fd_)`
2. 清理所有资源
3. 通知会话管理器：`session_manager_->onSessionError(session_id_)`

**结果**：异常会话被正确处理和清理

## 总结

HttpSession系统的业务流程体现了高效HTTP连接管理的核心特性：

1. **完整的生命周期管理**：从连接建立到关闭的完整流程
2. **状态机驱动**：基于状态机的会话状态管理
3. **Keep-Alive支持**：高效的长连接管理
4. **超时管理**：完善的超时检测和处理机制
5. **异步IO**：基于事件驱动的异步IO处理
6. **错误处理**：健壮的错误检测和恢复机制
7. **性能监控**：详细的会话统计和性能指标
8. **资源管理**：安全的资源分配和清理

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。

## 外部模块依赖关系

### 1. 核心依赖模块
- **Channel/EventLoop**: 网络事件处理和IO多路复用
- **HttpRequest/HttpResponse**: HTTP协议数据结构
- **Timer**: 超时管理和定时任务
- **Logger**: 统一的日志记录系统

### 2. 标准库依赖
- **std::chrono**: 时间测量和超时管理
- **std::atomic**: 线程安全的状态管理
- **std::mutex**: 线程同步和资源保护
- **std::queue**: 请求队列和缓冲管理

### 3. 集成特性
- **与HttpServer集成**: 作为连接管理的核心组件
- **与网络模块集成**: 使用统一的网络事件处理
- **与监控系统集成**: 提供详细的连接和性能指标
- **与配置系统集成**: 支持超时和连接参数配置