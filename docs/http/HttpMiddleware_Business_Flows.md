# HttpMiddleware 业务流程详细分析

## 目录
1. [中间件系统初始化流程](#中间件系统初始化流程)
2. [中间件注册流程](#中间件注册流程)
3. [中间件执行流程](#中间件执行流程)
4. [内置中间件业务流程](#内置中间件业务流程)
5. [路径匹配中间件流程](#路径匹配中间件流程)
6. [异步中间件处理流程](#异步中间件处理流程)
7. [中间件错误处理流程](#中间件错误处理流程)

## 中间件系统初始化流程

### 1. 中间件管理器创建流程

**业务场景**：创建中间件管理器，准备中间件执行环境

**执行步骤**：

#### 步骤1：创建管理器实例
```cpp
// 调用类：main() 函数或HttpServer
// 使用函数：MiddlewareManager::MiddlewareManager()
MiddlewareManager manager;
```

**内部执行**：
- `MiddlewareManager::MiddlewareManager()` 构造函数
- `middlewares_.clear()` - 初始化中间件列表
- `execution_stats_.clear()` - 初始化统计信息
- `initialized_ = false` - 设置初始化状态

#### 步骤2：系统初始化
```cpp
// 调用类：MiddlewareManager
// 使用函数：MiddlewareManager::initialize()
bool success = manager.initialize();
```

**内部执行序列**：
1. `MiddlewareManager::initialize()`
2. 遍历所有中间件：`for (auto& middleware : middlewares_)`
3. `HttpMiddleware::initialize()` - 初始化每个中间件
   - 记录初始化日志：`LOG_INFO("Initializing middleware: " + name_)`
   - 执行中间件特定初始化逻辑
4. `sortMiddlewares()` - 按优先级排序中间件
   - 使用 `std::sort()` 按 `priority_` 降序排列
5. `initialized_ = true` - 标记初始化完成

**结果**：中间件管理器初始化完成，准备执行中间件链

### 2. 中间件清理流程

**业务场景**：系统关闭时清理中间件资源

**执行步骤**：

#### 步骤1：停止中间件执行
```cpp
// 调用类：MiddlewareManager
// 使用函数：MiddlewareManager::cleanup()
manager.cleanup();
```

**内部执行序列**：
1. `MiddlewareManager::cleanup()`
2. 遍历所有中间件：`for (auto& middleware : middlewares_)`
3. `HttpMiddleware::cleanup()` - 清理每个中间件
   - 记录清理日志：`LOG_INFO("Cleaning up middleware: " + name_)`
   - 释放中间件特定资源
4. `middlewares_.clear()` - 清空中间件列表
5. `execution_stats_.clear()` - 清空统计信息

**结果**：所有中间件资源被正确释放

## 中间件注册流程

### 1. 全局中间件注册流程

**业务场景**：注册应用于所有请求的全局中间件

**执行步骤**：

#### 步骤1：创建中间件实例
```cpp
// 调用类：main() 函数
// 使用函数：各种中间件构造函数
auto logging = std::make_shared<LoggingMiddleware>();
auto cors = std::make_shared<CorsMiddleware>();
auto auth = std::make_shared<AuthMiddleware>("secret_key");
```

**内部执行**：
- `LoggingMiddleware::LoggingMiddleware()` - 创建日志中间件
- `CorsMiddleware::CorsMiddleware()` - 创建CORS中间件
- `AuthMiddleware::AuthMiddleware(secret_key)` - 创建认证中间件

#### 步骤2：注册中间件
```cpp
// 调用类：MiddlewareManager
// 使用函数：MiddlewareManager::use()
manager.use(logging);
manager.use(cors);
manager.use(auth);
```

**内部执行序列**：
1. `MiddlewareManager::use(middleware)`
2. `std::lock_guard<std::mutex> lock(middlewares_mutex_)` - 加锁保护
3. `middlewares_.push_back(middleware)` - 添加到中间件列表
4. `sortMiddlewares()` - 重新排序（按优先级）
5. 如果已初始化：`middleware->initialize()` - 立即初始化新中间件

#### 步骤3：函数式中间件注册
```cpp
// 调用类：MiddlewareManager
// 使用函数：MiddlewareManager::use() 重载版本
manager.use([](const HttpRequest& req, HttpResponse& res, NextFunction next) {
    res.setHeader("X-Custom-Header", "MyValue");
    return next(); // 继续执行下一个中间件
});
```

**内部执行序列**：
1. `MiddlewareManager::use(MiddlewareFunction func)`
2. `auto wrapper = std::make_shared<FunctionMiddleware>(func)` - 包装为中间件对象
3. `use(wrapper)` - 调用标准注册流程

**结果**：中间件成功注册到管理器中

### 2. 路径特定中间件注册流程

**业务场景**：注册只对特定路径生效的中间件

**执行步骤**：

#### 步骤1：创建路径中间件
```cpp
// 调用类：main() 函数
// 使用函数：PathMiddleware::PathMiddleware()
auto api_auth = std::make_shared<AuthMiddleware>("api_secret");
auto path_middleware = std::make_shared<PathMiddleware>("/api/*", api_auth);
```

**内部执行序列**：
1. `PathMiddleware::PathMiddleware(path_pattern, middleware)`
2. `path_pattern_ = path_pattern` - 保存路径模式
3. `middleware_ = middleware` - 保存实际中间件
4. 尝试编译正则表达式：
   - `path_regex_ = std::regex(path_pattern)` - 编译正则表达式
   - `use_regex_ = true` - 标记使用正则匹配
   - 如果失败：`use_regex_ = false` - 使用字符串前缀匹配

#### 步骤2：注册路径中间件
```cpp
// 调用类：MiddlewareManager
// 使用函数：MiddlewareManager::use()
manager.use(path_middleware);
```

**内部执行**：与全局中间件注册流程相同

**结果**：路径特定中间件注册完成

## 中间件执行流程

### 1. 中间件链执行流程

**业务场景**：处理HTTP请求时执行中间件链

**执行步骤**：

#### 步骤1：开始执行中间件链
```cpp
// 调用类：HttpServer
// 使用函数：MiddlewareManager::execute()
manager.execute(request, response, [&](const HttpRequest& req, HttpResponse& res) {
    // 最终的请求处理逻辑
    handleBusinessLogic(req, res);
});
```

**内部执行序列**：
1. `MiddlewareManager::execute(request, response, final_handler)`
2. `MiddlewareContext context` - 创建执行上下文
3. `executeMiddlewares(request, response, context, 0, final_handler)` - 开始递归执行

#### 步骤2：递归执行中间件
**执行序列**：
1. `executeMiddlewares(request, response, context, index, final_handler)`
2. 检查是否到达链末尾：
   - 如果 `index >= middlewares_.size()`：调用 `final_handler(request, response)`
3. 获取当前中间件：`auto& middleware = middlewares_[index]`
4. 检查是否应该执行：`middleware->shouldExecute(request)`

#### 步骤3：单个中间件执行
**执行序列**：
1. 记录开始时间：`auto start_time = std::chrono::steady_clock::now()`
2. 创建下一个函数：
```cpp
NextFunction next = [&]() -> bool {
    return executeMiddlewares(request, response, context, index + 1, final_handler);
};
```
3. `middleware->execute(request, response, context, next)` - 执行中间件
4. 更新执行统计：`updateExecutionStats(middleware->getName(), duration)`

#### 步骤4：处理执行结果
**执行序列**：
1. 根据 `MiddlewareResult` 处理结果：
   - `CONTINUE`：继续执行下一个中间件
   - `STOP`：正常停止，不执行后续中间件
   - `ERROR`：错误停止，记录错误日志

**结果**：中间件链执行完成或在某个中间件处停止

### 2. 中间件上下文传递流程

**业务场景**：在中间件之间传递上下文数据

**执行步骤**：

#### 步骤1：上下文创建
**执行序列**：
1. `MiddlewareContext context` - 创建上下文对象
2. `start_time_ = std::chrono::steady_clock::now()` - 记录开始时间
3. `attributes_.clear()` - 初始化属性映射

#### 步骤2：上下文数据设置
**中间件内部操作**：
```cpp
// 在中间件中设置上下文数据
context.set("user_id", "12345");
context.set("request_id", generateRequestId());
context.set("start_time", getCurrentTimeString());
```

**内部执行**：
- `MiddlewareContext::set(key, value)` - 设置属性
- `attributes_[key] = value` - 存储到内部映射

#### 步骤3：上下文数据获取
**后续中间件操作**：
```cpp
// 在后续中间件中获取上下文数据
std::string user_id = context.get("user_id");
std::string request_id = context.get("request_id", "unknown");
bool has_user = context.has("user_id");
```

**内部执行**：
- `MiddlewareContext::get(key, default_value)` - 获取属性
- `attributes_.find(key)` - 查找属性
- 返回找到的值或默认值

**结果**：上下文数据在中间件链中成功传递

## 内置中间件业务流程

### 1. CORS中间件处理流程

**业务场景**：处理跨域资源共享请求

**执行步骤**：

#### 步骤1：CORS中间件初始化
```cpp
// 调用类：main() 函数
// 使用函数：CorsMiddleware::CorsMiddleware()
CorsMiddleware::Config config;
config.allow_origin = "*";
config.allow_methods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
config.allow_headers = {"Content-Type", "Authorization"};
config.allow_credentials = true;
config.max_age = 86400;

auto cors = std::make_shared<CorsMiddleware>(config);
```

**内部执行**：
- `CorsMiddleware::CorsMiddleware(config)` - 保存配置
- `config_ = config` - 存储CORS配置

#### 步骤2：预检请求处理
**请求示例**：`OPTIONS /api/users`

**执行序列**：
1. `CorsMiddleware::execute(request, response, context, next)`
2. 检查请求方法：`request.getMethod() == HttpMethod::OPTIONS`
3. 设置CORS响应头：
   - `response.setHeader("Access-Control-Allow-Origin", config_.allow_origin)`
   - `response.setHeader("Access-Control-Allow-Methods", join(config_.allow_methods))`
   - `response.setHeader("Access-Control-Allow-Headers", join(config_.allow_headers))`
   - `response.setHeader("Access-Control-Max-Age", std::to_string(config_.max_age))`
4. `response.setStatus(200)` - 设置成功状态
5. 返回 `MiddlewareResult::STOP` - 停止执行，直接返回

#### 步骤3：实际请求处理
**请求示例**：`POST /api/users`

**执行序列**：
1. `CorsMiddleware::execute(request, response, context, next)`
2. 设置CORS响应头：
   - `response.setHeader("Access-Control-Allow-Origin", config_.allow_origin)`
   - 如果 `allow_credentials`：`response.setHeader("Access-Control-Allow-Credentials", "true")`
3. `next()` - 继续执行下一个中间件
4. 返回 `MiddlewareResult::CONTINUE`

**结果**：CORS头部正确设置，跨域请求得到支持

### 2. 认证中间件处理流程

**业务场景**：验证JWT Token进行用户认证

**执行步骤**：

#### 步骤1：认证中间件初始化
```cpp
// 调用类：main() 函数
// 使用函数：AuthMiddleware::AuthMiddleware()
AuthMiddleware::Config config;
config.secret_key = "my_secret_key";
config.token_header = "Authorization";
config.token_prefix = "Bearer ";
config.token_expiry_hours = 24;
config.exclude_paths = {"/login", "/register", "/public"};

auto auth = std::make_shared<AuthMiddleware>(config);
```

#### 步骤2：路径排除检查
**执行序列**：
1. `AuthMiddleware::shouldExecute(request)`
2. `std::string path = request.getPath()`
3. 遍历排除路径：`for (const auto& exclude_path : config_.exclude_paths)`
4. 检查路径匹配：`path.starts_with(exclude_path)`
5. 如果匹配：返回 `false`（跳过认证）

#### 步骤3：Token提取和验证
**执行序列**：
1. `AuthMiddleware::execute(request, response, context, next)`
2. 提取Authorization头：`std::string auth_header = request.getHeader(config_.token_header)`
3. 检查前缀：`auth_header.starts_with(config_.token_prefix)`
4. 提取Token：`std::string token = auth_header.substr(config_.token_prefix.length())`
5. 验证Token：`bool valid = validateJwtToken(token, config_.secret_key)`

#### 步骤4：认证结果处理
**执行序列**：
1. 如果Token有效：
   - 解析用户信息：`UserInfo user = parseTokenClaims(token)`
   - 设置上下文：`context.set("user_id", user.id)`
   - `next()` - 继续执行
   - 返回 `MiddlewareResult::CONTINUE`
2. 如果Token无效：
   - `response.unauthorized("Invalid or expired token")`
   - 返回 `MiddlewareResult::STOP`

**结果**：用户身份验证完成，用户信息存储在上下文中

### 3. 日志中间件处理流程

**业务场景**：记录HTTP请求和响应日志

**执行步骤**：

#### 步骤1：日志中间件初始化
```cpp
// 调用类：main() 函数
// 使用函数：LoggingMiddleware::LoggingMiddleware()
LoggingMiddleware::Config config;
config.log_request_headers = true;
config.log_request_body = false;
config.log_response_headers = false;
config.log_response_body = false;
config.exclude_paths = {"/health", "/metrics"};
config.max_body_log_size = 1024;

auto logging = std::make_shared<LoggingMiddleware>(config);
```

#### 步骤2：请求日志记录
**执行序列**：
1. `LoggingMiddleware::execute(request, response, context, next)`
2. 记录请求开始时间：`auto start_time = std::chrono::steady_clock::now()`
3. 生成请求ID：`std::string request_id = generateRequestId()`
4. 记录请求信息：
```cpp
LOG_INFO("Request started: " + request_id + 
         " " + request.getMethodString() + 
         " " + request.getPath() + 
         " from " + request.getClientIP());
```
5. 如果配置启用：记录请求头和请求体

#### 步骤3：执行下一个中间件
**执行序列**：
1. `context.set("request_id", request_id)` - 设置请求ID到上下文
2. `next()` - 执行下一个中间件
3. 记录结束时间：`auto end_time = std::chrono::steady_clock::now()`

#### 步骤4：响应日志记录
**执行序列**：
1. 计算处理时间：`auto duration = end_time - start_time`
2. 记录响应信息：
```cpp
LOG_INFO("Request completed: " + request_id + 
         " Status: " + std::to_string(response.getStatus()) + 
         " Duration: " + std::to_string(duration.count()) + "ms");
```
3. 如果配置启用：记录响应头和响应体
4. 返回 `MiddlewareResult::CONTINUE`

**结果**：完整的请求处理日志被记录

### 4. 限流中间件处理流程

**业务场景**：限制客户端请求频率

**执行步骤**：

#### 步骤1：限流中间件初始化
```cpp
// 调用类：main() 函数
// 使用函数：RateLimitMiddleware::RateLimitMiddleware()
RateLimitMiddleware::Config config;
config.requests_per_minute = 60;
config.burst_size = 10;
config.key_generator = [](const HttpRequest& req) {
    return req.getClientIP(); // 基于IP限流
};

auto rate_limit = std::make_shared<RateLimitMiddleware>(config);
```

#### 步骤2：限流键生成
**执行序列**：
1. `RateLimitMiddleware::execute(request, response, context, next)`
2. 生成限流键：`std::string key = config_.key_generator(request)`
3. 获取当前时间：`auto now = std::chrono::steady_clock::now()`

#### 步骤3：限流检查
**执行序列**：
1. `std::lock_guard<std::mutex> lock(rate_limit_mutex_)` - 加锁保护
2. 查找限流记录：`auto it = rate_limits_.find(key)`
3. 如果记录不存在：创建新记录
4. 检查时间窗口：
   - 如果超过1分钟：重置计数器
   - 否则：增加请求计数
5. 检查是否超过限制：`count > config_.requests_per_minute`

#### 步骤4：限流结果处理
**执行序列**：
1. 如果未超过限制：
   - 更新限流记录：`rate_limits_[key] = {count + 1, now}`
   - `next()` - 继续执行
   - 返回 `MiddlewareResult::CONTINUE`
2. 如果超过限制：
   - `response.setStatus(429)` - Too Many Requests
   - `response.setHeader("Retry-After", "60")` - 重试时间
   - `response.setBody("Rate limit exceeded")`
   - 返回 `MiddlewareResult::STOP`

**结果**：请求频率得到有效控制

## 路径匹配中间件流程

### 1. 路径模式匹配流程

**业务场景**：根据请求路径决定是否执行中间件

**执行步骤**：

#### 步骤1：路径中间件创建
```cpp
// 调用类：main() 函数
// 使用函数：PathMiddleware::PathMiddleware()
auto api_middleware = std::make_shared<AuthMiddleware>("api_secret");
auto path_middleware = std::make_shared<PathMiddleware>("/api/.*", api_middleware);
```

**内部执行序列**：
1. `PathMiddleware::PathMiddleware(path_pattern, middleware)`
2. 尝试编译正则表达式：
   - `path_regex_ = std::regex(path_pattern)` - 编译成功
   - `use_regex_ = true` - 使用正则匹配
3. 如果编译失败：
   - `use_regex_ = false` - 使用字符串前缀匹配

#### 步骤2：路径匹配检查
**请求示例**：`GET /api/users/123`

**执行序列**：
1. `PathMiddleware::shouldExecute(request)`
2. `std::string path = request.getPath()` - 获取请求路径
3. 如果使用正则表达式：
   - `std::regex_match(path, path_regex_)` - 正则匹配
4. 如果使用字符串匹配：
   - `path.starts_with(path_pattern_)` - 前缀匹配
5. 返回匹配结果

#### 步骤3：委托执行
**执行序列**：
1. 如果路径匹配：`PathMiddleware::execute(request, response, context, next)`
2. `middleware_->execute(request, response, context, next)` - 委托给实际中间件
3. 返回实际中间件的执行结果

**结果**：只有匹配路径的请求才会执行特定中间件

## 异步中间件处理流程

### 1. 异步中间件执行流程

**业务场景**：在线程池中异步执行耗时的中间件操作

**执行步骤**：

#### 步骤1：异步中间件创建
```cpp
// 调用类：main() 函数
// 使用函数：AsyncMiddleware::AsyncMiddleware()
class DatabaseMiddleware : public AsyncMiddleware {
public:
    MiddlewareResult executeAsync(const HttpRequest& request, HttpResponse& response,
                                MiddlewareContext& context, NextFunction next) override {
        // 异步数据库操作
        auto user = database.findUserAsync(request.getHeader("User-ID"));
        context.set("user_data", user.serialize());
        return next() ? MiddlewareResult::CONTINUE : MiddlewareResult::STOP;
    }
};
```

#### 步骤2：提交异步任务
**执行序列**：
1. `AsyncMiddleware::execute(request, response, context, next)`
2. `auto future = thread_pool_->submit([&]() { ... })` - 提交异步任务
3. 在工作线程中执行：`executeAsync(request, response, context, next)`
4. `future.wait()` - 等待任务完成
5. 返回异步执行结果

**结果**：中间件在后台线程中执行，不阻塞主线程

## 中间件错误处理流程

### 1. 中间件异常处理流程

**业务场景**：处理中间件执行过程中的异常

**执行步骤**：

#### 步骤1：异常捕获
**执行序列**：
1. `MiddlewareManager::executeMiddlewares()` 中的异常处理
2. `try { middleware->execute(...) }` - 执行中间件
3. `catch (const std::exception& e)` - 捕获异常

#### 步骤2：异常处理
**执行序列**：
1. 记录错误日志：`LOG_ERROR("Middleware error: " + middleware->getName() + " - " + e.what())`
2. 更新错误统计：`execution_stats_[middleware->getName() + "_errors"]++`
3. 设置错误响应：`response.internalServerError("Middleware error")`
4. 返回 `MiddlewareResult::ERROR`

#### 步骤3：错误恢复
**执行序列**：
1. 检查是否有错误处理中间件
2. 如果有：执行错误处理逻辑
3. 如果没有：直接返回错误响应

**结果**：中间件异常被正确处理，系统保持稳定

## 总结

HttpMiddleware系统的业务流程体现了现代Web框架的核心特性：

1. **责任链模式**：中间件按顺序执行，每个中间件决定是否继续
2. **灵活配置**：支持全局和路径特定中间件
3. **异步支持**：支持异步中间件执行
4. **错误处理**：完善的异常处理和错误恢复机制
5. **性能监控**：内置执行统计和性能监控
6. **上下文传递**：在中间件之间传递上下文数据
7. **内置中间件**：提供常用的CORS、认证、日志、限流等中间件

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。
