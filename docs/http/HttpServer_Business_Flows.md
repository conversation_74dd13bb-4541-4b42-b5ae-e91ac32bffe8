# HttpServer 业务流程详细分析

## 目录
1. [服务器生命周期管理](#服务器生命周期管理)
2. [HTTP请求处理流程](#HTTP请求处理流程)
3. [静态文件服务流程](#静态文件服务流程)
4. [路由管理流程](#路由管理流程)
5. [中间件执行流程](#中间件执行流程)
6. [错误处理流程](#错误处理流程)
7. [会话管理流程](#会话管理流程)

## 服务器生命周期管理

### 1. 服务器初始化流程

**业务场景**：启动HTTP服务器，准备接受客户端连接

**执行步骤**：

#### 步骤1：创建服务器实例
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::HttpServer(const HttpServerConfig& config)
HttpServerConfig config;
config.host = "127.0.0.1";
config.port = 8080;
config.enable_thread_pool = true;
config.thread_pool_size = 4;

HttpServer server(config);  // 创建服务器实例
```

**内部执行**：
- `HttpServer::HttpServer()` 构造函数
- `config_ = config` - 保存配置
- `router_ = std::make_unique<HttpRouter>()` - 创建路由器
- `running_ = false` - 初始化运行状态

#### 步骤2：注册路由和中间件
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::get(), HttpServer::post(), HttpServer::use()

// 注册GET路由
server.get("/api/users", [](const HttpRequest& req, HttpResponse& res) {
    res.json("{\"users\": []}");
});

// 注册POST路由
server.post("/api/users", userCreateHandler);

// 添加中间件
server.use(std::make_shared<LoggingMiddleware>());
```

**内部执行**：
- `HttpServer::get()` → `HttpRouter::addRoute(HttpMethod::GET, "/api/users", handler)`
- `HttpRouter::addRoute()` → `routes_.push_back({pattern, method, handler})`
- `HttpServer::use()` → `middlewares_.push_back(middleware)`

#### 步骤3：初始化网络组件
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::initialize()
bool success = server.initialize();
```

**内部执行序列**：
1. `HttpServer::initialize()`
2. `createListenSocket()` - 创建监听套接字
   - `Socket::create(AF_INET, SOCK_STREAM)` - 创建TCP套接字
   - `Socket::setReuseAddr(true)` - 设置地址复用
   - `Socket::bind(config_.host, config_.port)` - 绑定地址端口
3. `EventLoop::initialize()` - 初始化事件循环
   - `epoll_create1(EPOLL_CLOEXEC)` - 创建epoll实例
   - `EventLoop::addEvent(listen_fd, EPOLLIN)` - 添加监听事件
4. `ThreadPool::start(config_.thread_pool_size)` - 启动线程池
   - 创建工作线程
   - 初始化任务队列

#### 步骤4：启动服务器
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::start()
server.start();  // 阻塞运行
```

**内部执行序列**：
1. `HttpServer::start()`
2. `Socket::listen(config_.backlog)` - 开始监听连接
3. `running_ = true` - 设置运行状态
4. `EventLoop::run()` - 进入事件循环
   - 无限循环：`epoll_wait()` 等待事件
   - 处理新连接：`handleNewConnection()`
   - 处理数据读写：`handleClientData()`

**结果**：服务器开始监听指定端口，准备处理客户端请求

### 2. 服务器停止流程

**业务场景**：优雅关闭服务器，清理资源

**执行步骤**：

#### 步骤1：接收停止信号
```cpp
// 调用类：信号处理器或主程序
// 使用函数：HttpServer::stop()
server.stop();
```

#### 步骤2：停止接受新连接
**内部执行**：
1. `HttpServer::stop()`
2. `running_ = false` - 设置停止标志
3. `EventLoop::stop()` - 停止事件循环
4. `Socket::close(listen_fd)` - 关闭监听套接字

#### 步骤3：处理现有连接
**内部执行**：
1. `closeAllConnections()` - 关闭所有活跃连接
2. 遍历 `connections_` 映射：
   - `HttpSession::close()` - 关闭会话
   - `Socket::close(client_fd)` - 关闭客户端套接字
3. `connections_.clear()` - 清空连接映射

#### 步骤4：清理资源
**内部执行**：
1. `ThreadPool::stop()` - 停止线程池
   - 等待所有任务完成
   - 终止工作线程
2. `EventLoop::cleanup()` - 清理事件循环
3. 析构函数自动清理其他资源

**结果**：服务器完全停止，所有资源被释放

## HTTP请求处理流程

### 1. 新连接建立流程

**业务场景**：客户端建立TCP连接

**执行步骤**：

#### 步骤1：检测新连接
**触发条件**：`epoll_wait()` 检测到监听套接字可读

**执行序列**：
1. `EventLoop::poll()` - epoll等待事件
2. `EventLoop::onReadable(listen_fd)` - 监听套接字可读事件
3. `HttpServer::handleNewConnection()` - 处理新连接

#### 步骤2：接受连接
**内部执行**：
1. `Socket::accept()` - 接受客户端连接
   - 返回新的客户端套接字文件描述符
   - 获取客户端地址信息
2. `Socket::setNonBlocking(client_fd)` - 设置非阻塞模式
3. `Socket::setTcpNoDelay(client_fd)` - 禁用Nagle算法

#### 步骤3：创建会话
**内部执行**：
1. `HttpSession::HttpSession(client_fd, client_addr)` - 创建会话对象
   - `socket_fd_ = client_fd` - 保存套接字
   - `client_address_ = client_addr` - 保存客户端地址
   - `session_id_ = generateSessionId()` - 生成会话ID
   - `last_activity_ = now()` - 记录活动时间
2. `connections_[session_id] = session` - 存储会话
3. `EventLoop::addEvent(client_fd, EPOLLIN)` - 添加读事件监听

**结果**：新连接建立完成，等待接收HTTP请求

### 2. HTTP请求接收流程

**业务场景**：接收并解析客户端HTTP请求

**执行步骤**：

#### 步骤1：数据接收
**触发条件**：`epoll_wait()` 检测到客户端套接字可读

**执行序列**：
1. `EventLoop::onReadable(client_fd)` - 客户端可读事件
2. `HttpSession::readData()` - 读取数据
   - `Socket::recv(buffer, sizeof(buffer))` - 接收数据
   - `buffer_.append(data, bytes_read)` - 追加到接收缓冲区
   - 检查是否接收完整HTTP请求

#### 步骤2：请求解析
**执行序列**：
1. `HttpSession::processBuffer()` - 处理缓冲区数据
2. `HttpRequest::parseFromBytes(buffer_.data(), buffer_.size())` - 解析HTTP请求

**详细解析过程**：
1. `parseRequestLine()` - 解析请求行
   - 提取HTTP方法：`method_ = parseMethod("GET")`
   - 提取请求路径：`path_ = "/api/users"`
   - 提取HTTP版本：`version_ = parseVersion("HTTP/1.1")`
2. `parseHeaders()` - 解析HTTP头部
   - 逐行解析：`"Content-Type: application/json"`
   - 存储到映射：`headers_["Content-Type"] = "application/json"`
3. `parseQueryParams()` - 解析查询参数
   - 从路径提取查询字符串：`"?id=123&name=test"`
   - 解析参数：`params_["id"] = "123", params_["name"] = "test"`
4. `parseBody()` - 解析请求体（如果有）
   - 根据Content-Length读取指定长度
   - 存储请求体：`body_ = request_body_data`
5. `parseCookies()` - 解析Cookie
   - 从Cookie头部解析：`"sessionid=abc123; userid=456"`
   - 存储到映射：`cookies_["sessionid"] = "abc123"`

#### 步骤3：设置请求信息
**执行序列**：
1. `HttpRequest::setClientIP(session->getClientIP())` - 设置客户端IP
2. `HttpRequest::setParsed(true)` - 标记解析完成
3. `HttpSession::setState(SessionState::REQUEST_READY)` - 设置会话状态

**结果**：HTTP请求解析完成，准备进入业务处理流程

### 3. 业务逻辑处理流程

**业务场景**：执行具体的业务逻辑处理

**执行步骤**：

#### 步骤1：启动请求处理
**执行序列**：
1. `HttpSession::processBuffer()` 检测到完整请求
2. `HttpServer::handleRequest(session, request)` - 开始处理请求
3. `HttpResponse response` - 创建响应对象

#### 步骤2：中间件预处理
**执行序列**：
1. 遍历中间件列表：`for (auto& middleware : middlewares_)`
2. `HttpMiddleware::beforeRequest(request, response)` - 执行前置中间件
   - **日志中间件**：记录请求信息
   - **认证中间件**：验证用户身份
   - **CORS中间件**：设置跨域头部
   - **限流中间件**：检查请求频率
3. 如果任何中间件返回false，中断处理链

#### 步骤3：路由匹配
**执行序列**：
1. `HttpRouter::match(request.getMethod(), request.getPath())` - 路由匹配
2. `findMatchingRoute()` - 查找匹配的路由
   - 遍历路由表：`for (const auto& route : routes_)`
   - 比较HTTP方法：`route.method == request.getMethod()`
   - 匹配路径模式：`matchPattern(route.pattern, request.getPath())`
3. `extractPathParams()` - 提取路径参数
   - 模式：`"/api/users/:id"` 路径：`"/api/users/123"`
   - 提取参数：`params["id"] = "123"`
4. 返回匹配的处理函数

#### 步骤4：执行业务处理函数
**执行序列**：
1. `HandlerFunction(request, response)` - 调用业务处理函数
2. **业务逻辑示例**：
```cpp
// GET /api/users/:id 处理函数
void handleGetUser(const HttpRequest& req, HttpResponse& res) {
    // 1. 获取路径参数
    std::string user_id = req.getParam("id");

    // 2. 验证参数
    if (user_id.empty()) {
        res.badRequest("User ID is required");
        return;
    }

    // 3. 查询数据库
    User user = database.findUserById(user_id);
    if (!user.exists()) {
        res.notFound("User not found");
        return;
    }

    // 4. 构建响应
    std::string json = user.toJson();
    res.json(json);
}
```

#### 步骤5：中间件后处理
**执行序列**：
1. 遍历中间件列表（逆序）：`for (auto it = middlewares_.rbegin(); it != middlewares_.rend(); ++it)`
2. `HttpMiddleware::afterResponse(request, response)` - 执行后置中间件
   - **性能监控中间件**：记录响应时间
   - **压缩中间件**：压缩响应内容
   - **安全头中间件**：添加安全相关头部

**结果**：业务逻辑处理完成，响应对象构建完毕

### 4. HTTP响应发送流程

**业务场景**：将处理结果发送给客户端

**执行步骤**：

#### 步骤1：响应序列化
**执行序列**：
1. `HttpResponse::toString()` - 序列化HTTP响应
2. `buildStatusLine()` - 构建状态行
   - 格式：`"HTTP/1.1 200 OK\r\n"`
3. `buildHeaders()` - 构建响应头
   - 自动添加：`"Content-Length: 1234\r\n"`
   - 用户设置：`"Content-Type: application/json\r\n"`
   - 结束标记：`"\r\n"`
4. `buildBody()` - 添加响应体
   - 直接追加响应内容

#### 步骤2：数据发送
**执行序列**：
1. `HttpSession::writeData(response.toString())` - 发送响应
2. `Socket::send(response_data.data(), response_data.size())` - 发送数据
3. `HttpSession::updateActivity()` - 更新活动时间

#### 步骤3：连接管理
**执行序列**：
1. 检查Connection头部：
   - `"Connection: keep-alive"` → 保持连接
   - `"Connection: close"` → 关闭连接
2. 如果保持连接：
   - `HttpSession::reset()` - 重置会话状态
   - 继续监听下一个请求
3. 如果关闭连接：
   - `HttpSession::close()` - 关闭会话
   - `connections_.erase(session_id)` - 移除会话

**结果**：HTTP响应发送完成，连接状态更新

## 静态文件服务流程

### 1. 静态路由配置流程

**业务场景**：配置静态文件服务路径

**执行步骤**：

#### 步骤1：注册静态路由
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::serveStatic()
server.serveStatic("/static", "./public");
server.serveStatic("/assets", "./assets");
```

**内部执行序列**：
1. `HttpServer::serveStatic(url_path, directory)`
2. `normalizeDirectoryPath(directory)` - 标准化目录路径
3. `directoryExists(directory)` - 检查目录是否存在
4. `hasDirectoryReadPermission(directory)` - 检查读取权限
5. `HttpRouter::addRoute(HttpMethod::GET, url_path + "/*", staticFileHandler)` - 注册通配符路由
6. `static_routes_[url_path] = directory` - 存储路径映射

**结果**：静态文件路由配置完成

### 2. 静态文件请求处理流程

**业务场景**：客户端请求静态资源文件

**执行步骤**：

#### 步骤1：请求路由匹配
**请求示例**：`GET /static/css/style.css`

**执行序列**：
1. `HttpRouter::match(GET, "/static/css/style.css")` - 路由匹配
2. 匹配到静态文件处理器：`handleStaticFileRequest`
3. 提取路径参数：`base_path = "/static"`, `file_path = "css/style.css"`

#### 步骤2：文件路径解析
**执行序列**：
1. `HttpServer::handleStaticFileRequest(request, response, "./public", "/static")`
2. `extractFilePath("/static/css/style.css", "/static")` - 提取相对路径
   - 结果：`"css/style.css"`
3. `full_path = "./public/css/style.css"` - 构建完整文件路径

#### 步骤3：安全检查
**执行序列**：
1. `isPathSafe(full_path, base_directory)` - 路径安全检查
   - `getCanonicalPath(full_path)` - 获取规范化绝对路径
   - `getCanonicalPath(base_directory)` - 获取基础目录绝对路径
   - 检查文件路径是否在基础目录内（防止目录遍历攻击）
2. `normalizeFilePath(full_path)` - 标准化文件路径
   - 处理 `../` 和 `./` 路径组件
   - 移除多余的斜杠

#### 步骤4：文件系统检查
**执行序列**：
1. `fileExists(full_path)` - 检查文件是否存在
   - `stat(full_path.c_str(), &buffer)` - 获取文件状态
2. `isDirectory(full_path)` - 检查是否为目录
   - 如果是目录，转到目录处理流程
3. `hasFileReadPermission(full_path)` - 检查文件读取权限
   - `access(full_path.c_str(), R_OK)` - 检查访问权限
4. `getFileInfo(full_path)` - 获取文件信息
   - 文件大小：`file_stat.st_size`
   - 修改时间：`file_stat.st_mtime`
   - 生成ETag：`"size-mtime"`

#### 步骤5：缓存处理
**执行序列**：
1. `handleConditionalRequest(request, response, file_info)` - 处理条件请求
2. 检查 `If-Modified-Since` 头部：
   - `parseHttpDate(if_modified_since)` - 解析客户端时间
   - 比较文件修改时间
   - 如果文件未修改：`response.setStatus(304)` 返回304 Not Modified
3. 检查 `If-None-Match` 头部：
   - 比较ETag值
   - 如果ETag匹配：返回304 Not Modified

#### 步骤6：响应头设置
**执行序列**：
1. `setStaticFileHeaders(response, file_info, full_path)` - 设置响应头
2. `getMimeType(full_path)` - 确定MIME类型
   - `getFileExtension(full_path)` - 获取文件扩展名
   - 查找MIME类型映射表
   - 设置 `Content-Type` 头部
3. 设置其他头部：
   - `Content-Length: file_size`
   - `Last-Modified: formatted_date`
   - `ETag: "size-mtime"`
   - `Cache-Control: public, max-age=3600`
   - `Accept-Ranges: bytes`

#### 步骤7：文件内容处理
**执行序列**：
1. 检查请求方法：
   - 如果是HEAD请求：只发送头部，跳过内容
2. 检查Range头部：
   - 如果有Range头：`handleRangeRequest()` - 处理范围请求
   - 否则：`handleNormalFileRequest()` - 处理普通请求
3. `readFileContent(full_path)` - 读取文件内容
   - `std::ifstream file(full_path, std::ios::binary)` - 打开文件
   - `file.read(buffer, file_size)` - 读取内容
4. `response.setBody(file_content)` - 设置响应体

**结果**：静态文件成功发送给客户端

### 3. 目录浏览处理流程

**业务场景**：客户端请求目录，显示目录内容列表

**执行步骤**：

#### 步骤1：目录请求检测
**请求示例**：`GET /static/images/`

**执行序列**：
1. `isDirectory(full_path)` - 检测到请求的是目录
2. `findIndexFile(dir_path)` - 查找默认索引文件
   - 查找顺序：`index.html`, `index.htm`, `default.html`
   - 如果找到索引文件，按文件处理流程处理

#### 步骤2：目录权限和配置检查
**执行序列**：
1. `directoryExists(dir_path)` - 确认目录存在
2. `hasDirectoryReadPermission(dir_path)` - 检查目录读取权限
3. 检查配置：`config_.enable_directory_listing`
   - 如果禁用目录浏览：`generateForbiddenPage()` - 生成403页面

#### 步骤3：目录内容扫描
**执行序列**：
1. `isDirectoryEmpty(dir_path)` - 检查目录是否为空
2. 如果为空：`generateEmptyDirectoryListing()` - 生成空目录页面
3. 如果非空：`generateDirectoryListing()` - 生成目录列表页面
4. `listDirectoryContents()` - 扫描目录内容
   - `opendir(dir_path.c_str())` - 打开目录
   - `readdir(dir)` - 读取目录项
   - 过滤隐藏文件（以.开头的文件）
   - 获取每个文件的信息：`getFileInfo(full_path)`

#### 步骤4：HTML页面生成
**执行序列**：
1. `generateDirectoryStats()` - 生成目录统计信息
   - 统计文件数量、目录数量、总大小
2. 生成HTML结构：
   - 页面头部：CSS样式、标题
   - 统计信息：文件数量、总大小
   - 文件列表表格：名称、大小、修改时间、类型
3. 为每个文件/目录生成表格行：
   - `getFileIcon(filename)` - 获取文件类型图标
   - `formatFileSize(size)` - 格式化文件大小
   - `getFileTypeDescription(filename)` - 获取文件类型描述
   - `formatHttpDate(last_modified)` - 格式化修改时间

**结果**：生成美观的目录浏览页面

### 4. 范围请求处理流程

**业务场景**：客户端请求文件的部分内容（断点续传、视频流）

**执行步骤**：

#### 步骤1：Range头解析
**请求示例**：`Range: bytes=0-1023`

**执行序列**：
1. `handleRangeRequest(request, response, file_info, file_path)`
2. `parseRangeHeader(range_header, file_size)` - 解析Range头
   - 支持格式：`bytes=start-end`, `bytes=start-`, `bytes=-suffix`
   - 验证范围有效性：`start <= end < file_size`

#### 步骤2：范围验证
**执行序列**：
1. 如果范围无效：
   - `response.setStatus(416)` - Range Not Satisfiable
   - `response.setHeader("Content-Range", "bytes */file_size")`
2. 如果范围有效：
   - `response.setStatus(206)` - Partial Content
   - 计算范围大小：`range_size = end - start + 1`

#### 步骤3：范围内容读取
**执行序列**：
1. `readFileRange(file_path, start, length)` - 读取文件范围
   - `std::ifstream file(file_path, std::ios::binary)` - 打开文件
   - `file.seekg(start, std::ios::beg)` - 定位到起始位置
   - `file.read(buffer, length)` - 读取指定长度
2. 设置响应头：
   - `Content-Range: bytes start-end/total_size`
   - `Content-Length: range_size`
3. `response.setBody(range_content)` - 设置部分内容

**结果**：发送文件的指定范围内容

## 路由管理流程

### 1. 路由注册流程

**业务场景**：注册各种HTTP路由

**执行步骤**：

#### 步骤1：基本路由注册
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::get(), post(), put(), delete_()
server.get("/api/users", getUsersHandler);
server.post("/api/users", createUserHandler);
server.put("/api/users/:id", updateUserHandler);
server.delete_("/api/users/:id", deleteUserHandler);
```

**内部执行序列**：
1. `HttpServer::get(pattern, handler)` - 注册GET路由
2. `HttpRouter::addRoute(HttpMethod::GET, pattern, handler)` - 添加路由
3. `parseRoutePattern(pattern)` - 解析路由模式
   - 识别参数：`:id` → 参数名 `id`
   - 识别通配符：`*` → 通配符匹配
4. `routes_.push_back({pattern, method, handler, param_names})` - 存储路由
5. `method_index_[method_string].push_back(route_index)` - 建立方法索引

#### 步骤2：参数化路由处理
**路由模式**：`/api/users/:id/posts/:post_id`

**执行序列**：
1. `parseRoutePattern()` 解析模式：
   - 分割路径段：`["api", "users", ":id", "posts", ":post_id"]`
   - 提取参数名：`["id", "post_id"]`
   - 生成匹配正则表达式

#### 步骤3：中间件路由注册
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::use()
server.use("/api", authMiddleware);  // 路径特定中间件
server.use(loggingMiddleware);       // 全局中间件
```

**内部执行序列**：
1. `HttpServer::use(path, middleware)` - 注册路径中间件
2. `path_middlewares_[path].push_back(middleware)` - 存储路径中间件
3. `HttpServer::use(middleware)` - 注册全局中间件
4. `middlewares_.push_back(middleware)` - 存储全局中间件

### 2. 路由匹配流程

**业务场景**：根据请求找到对应的处理函数

**执行步骤**：

#### 步骤1：快速方法过滤
**请求示例**：`GET /api/users/123`

**执行序列**：
1. `HttpRouter::match(HttpMethod::GET, "/api/users/123")`
2. `method_index_[GET]` - 获取GET方法的路由索引列表
3. 只在GET路由中查找，提高匹配效率

#### 步骤2：路径模式匹配
**执行序列**：
1. 遍历GET路由：`for (auto index : get_routes)`
2. `matchPattern(route.pattern, request_path)` - 模式匹配
   - 静态路径：直接字符串比较
   - 参数路径：正则表达式匹配
   - 通配符路径：前缀匹配

#### 步骤3：参数提取
**路由模式**：`/api/users/:id` **请求路径**：`/api/users/123`

**执行序列**：
1. `extractPathParams(pattern, path)` - 提取路径参数
2. 分割路径：`["api", "users", "123"]`
3. 匹配参数位置：`params["id"] = "123"`
4. `request.setPathParams(params)` - 设置路径参数

**结果**：找到匹配的处理函数和提取的参数

## 中间件执行流程

### 1. 中间件注册流程

**业务场景**：注册各种功能中间件

**执行步骤**：

#### 步骤1：创建中间件实例
```cpp
// 自定义中间件实现
class LoggingMiddleware : public HttpMiddleware {
public:
    bool beforeRequest(HttpRequest& req, HttpResponse& res) override {
        start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("Request: " + req.getMethodString() + " " + req.getPath());
        return true;  // 继续处理
    }

    void afterResponse(const HttpRequest& req, HttpResponse& res) override {
        auto duration = std::chrono::steady_clock::now() - start_time_;
        LOG_INFO("Response: " + std::to_string(res.getStatus()) +
                " (" + std::to_string(duration.count()) + "ms)");
    }
};
```

#### 步骤2：注册中间件
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::use()
auto logging = std::make_shared<LoggingMiddleware>();
auto auth = std::make_shared<AuthMiddleware>();
auto cors = std::make_shared<CorsMiddleware>();

server.use(logging);  // 全局中间件
server.use("/api", auth);  // 路径特定中间件
server.use(cors);     // 全局中间件
```

**内部执行序列**：
1. `HttpServer::use(middleware)` - 注册全局中间件
2. `middlewares_.push_back(middleware)` - 添加到全局中间件列表
3. `HttpServer::use(path, middleware)` - 注册路径中间件
4. `path_middlewares_[path].push_back(middleware)` - 添加到路径中间件映射

### 2. 中间件执行流程

**业务场景**：在请求处理前后执行中间件逻辑

**执行步骤**：

#### 步骤1：前置中间件执行
**执行序列**：
1. `HttpServer::handleRequest()` 开始处理请求
2. 执行全局前置中间件：
```cpp
for (auto& middleware : middlewares_) {
    if (!middleware->beforeRequest(request, response)) {
        return;  // 中间件中断处理
    }
}
```
3. 执行路径特定前置中间件：
```cpp
for (auto& [path, middleware_list] : path_middlewares_) {
    if (request.getPath().starts_with(path)) {
        for (auto& middleware : middleware_list) {
            if (!middleware->beforeRequest(request, response)) {
                return;  // 中间件中断处理
            }
        }
    }
}
```

#### 步骤2：业务逻辑执行
**执行序列**：
1. 所有前置中间件执行成功
2. `HandlerFunction(request, response)` - 执行业务处理函数
3. 如果发生异常：`middleware->onError(request, response, exception)`

#### 步骤3：后置中间件执行
**执行序列**：
1. 业务逻辑执行完成
2. 执行路径特定后置中间件（逆序）：
```cpp
for (auto it = path_middlewares_.rbegin(); it != path_middlewares_.rend(); ++it) {
    if (request.getPath().starts_with(it->first)) {
        for (auto mid_it = it->second.rbegin(); mid_it != it->second.rend(); ++mid_it) {
            (*mid_it)->afterResponse(request, response);
        }
    }
}
```
3. 执行全局后置中间件（逆序）：
```cpp
for (auto it = middlewares_.rbegin(); it != middlewares_.rend(); ++it) {
    (*it)->afterResponse(request, response);
}
```

**结果**：中间件链完整执行，请求处理完成

## 错误处理流程

### 1. 404 Not Found 处理

**业务场景**：请求的路由不存在

**执行步骤**：
1. `HttpRouter::match()` 返回空结果
2. `HttpServer::handleRequest()` 检测到无匹配路由
3. `response.notFound("Page not found")` - 设置404响应
4. `HttpResponse::notFound()` 内部执行：
   - `setStatus(404)`
   - `setHeader("Content-Type", "text/html")`
   - `setBody(generate404Page())`

### 2. 500 Internal Server Error 处理

**业务场景**：处理函数抛出异常

**执行步骤**：
1. `HandlerFunction()` 抛出异常
2. `HttpServer::handleRequest()` 捕获异常：
```cpp
try {
    handler(request, response);
} catch (const std::exception& e) {
    // 执行错误中间件
    for (auto& middleware : middlewares_) {
        middleware->onError(request, response, e);
    }
    // 设置500错误响应
    response.internalServerError("Internal server error");
}
```

### 3. 自定义错误处理

**业务场景**：业务逻辑中的自定义错误

**执行步骤**：
1. 业务函数中检测错误条件
2. 调用相应的响应方法：
   - `response.badRequest("Invalid input")`
   - `response.unauthorized("Authentication required")`
   - `response.forbidden("Access denied")`
   - `response.conflict("Resource already exists")`

**结果**：错误被正确处理并返回给客户端

## 总结

HttpServer的业务流程体现了现代Web服务器的核心特性：

1. **事件驱动架构**：基于epoll的高效I/O处理
2. **模块化设计**：清晰的职责分离和接口定义
3. **中间件系统**：灵活的功能扩展机制
4. **路由系统**：强大的URL匹配和参数提取
5. **静态文件服务**：完整的文件服务功能
6. **错误处理**：完善的异常处理和错误响应
7. **性能优化**：连接复用、缓存控制、范围请求

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。
