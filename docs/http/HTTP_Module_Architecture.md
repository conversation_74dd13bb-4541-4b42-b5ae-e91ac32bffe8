# HTTP模块架构详细分析文档

## 目录
1. [模块概述](#模块概述)
2. [文件结构](#文件结构)
3. [核心类分析](#核心类分析)
4. [类之间的关系](#类之间的关系)
5. [业务流程分析](#业务流程分析)
6. [实际应用示例](#实际应用示例)

## 模块概述

HTTP模块是一个完整的HTTP服务器框架，采用事件驱动架构，支持高并发处理。主要功能包括：

- **HTTP协议处理**：完整的HTTP/1.0、HTTP/1.1协议支持
- **路由管理**：灵活的路由注册和匹配机制
- **中间件系统**：可插拔的中间件架构
- **静态文件服务**：高效的静态资源服务
- **会话管理**：用户会话和状态管理
- **性能监控**：实时性能统计和监控

## 文件结构

```
include/common/http/
├── http_request.h          # HTTP请求类
├── http_response.h         # HTTP响应类
├── http_server.h           # HTTP服务器主类
├── http_handler.h          # 请求处理器接口
├── http_router.h           # 路由管理器
├── http_session.h          # 会话管理
└── http_middleware.h       # 中间件接口

src/common/http/
├── http_request.cpp        # HTTP请求实现
├── http_response.cpp       # HTTP响应实现
├── http_server.cpp         # HTTP服务器实现
├── http_handler.cpp        # 请求处理器实现
├── http_router.cpp         # 路由管理器实现
├── http_session.cpp        # 会话管理实现
└── http_middleware.cpp     # 中间件实现
```

## 核心类分析

### 1. HttpRequest 类

**职责**：解析和管理HTTP请求数据

#### 核心属性
```cpp
class HttpRequest {
private:
    bool parsed_;                           // 解析状态
    HttpMethod method_;                     // HTTP方法（GET、POST等）
    std::string path_;                      // 请求路径
    std::string query_;                     // 查询字符串
    HttpVersion version_;                   // HTTP版本
    Headers headers_;                       // HTTP头部映射
    Parameters params_;                     // 查询参数映射
    Cookies cookies_;                       // Cookie映射
    std::string body_;                      // 请求体
    std::string client_ip_;                 // 客户端IP地址
    std::chrono::steady_clock::time_point parse_time_; // 解析时间
};
```

#### 核心方法
- **解析方法**：
  - `parseFromRawData(const std::string& raw_data)` - 从原始HTTP数据解析
  - `parseFromBytes(const char* data, size_t size)` - 从字节流解析
  - `isParsed()` - 检查是否解析完成

- **访问方法**：
  - `getMethod()` - 获取HTTP方法枚举
  - `getMethodString()` - 获取HTTP方法字符串
  - `getPath()` - 获取请求路径
  - `getQuery()` / `getQueryString()` - 获取查询字符串
  - `getHeader(name)` - 获取指定头部
  - `getParam(name)` - 获取查询参数
  - `getClientIP()` - 获取客户端IP

- **内容处理**：
  - `getJsonBody()` - 解析JSON请求体
  - `getFormData()` - 解析表单数据
  - `getCookie(name)` - 获取Cookie值

#### 实现特点
1. **智能解析**：支持分块解析，处理不完整的HTTP数据
2. **编码处理**：自动处理URL编码/解码
3. **安全验证**：请求大小限制、格式验证
4. **性能优化**：使用unordered_map提高查找效率

### 2. HttpResponse 类

**职责**：构建和管理HTTP响应数据

#### 核心属性
```cpp
class HttpResponse {
private:
    HttpStatus status_;                     // HTTP状态码
    std::string status_message_;            // 状态消息
    Headers headers_;                       // 响应头部
    std::string body_;                      // 响应体
    std::vector<Cookie> cookies_;           // Cookie列表
    bool headers_sent_;                     // 头部是否已发送
    std::chrono::steady_clock::time_point create_time_; // 创建时间
};
```

#### 核心方法
- **状态设置**：
  - `setStatus(HttpStatus status)` - 设置状态码
  - `ok(content)` - 200 OK响应
  - `created(content)` - 201 Created响应
  - `notFound(message)` - 404 Not Found响应
  - `internalServerError(message)` - 500错误响应

- **内容设置**：
  - `setBody(content)` - 设置响应体
  - `json(json_object)` - JSON响应
  - `html(html_content)` - HTML响应
  - `text(text_content)` - 文本响应

- **头部管理**：
  - `setHeader(name, value)` - 设置响应头
  - `setCookie(name, value, max_age)` - 设置Cookie
  - `redirect(url)` - 重定向响应

- **输出方法**：
  - `toString()` - 生成完整HTTP响应字符串
  - `toBytes()` - 生成字节数组

#### 实现特点
1. **自动计算**：自动计算Content-Length
2. **编码支持**：支持多种字符编码
3. **缓存控制**：智能缓存头部设置
4. **安全特性**：XSS防护、CSRF保护头部

### 3. HttpServer 类

**职责**：HTTP服务器主控制器，管理整个服务器生命周期

#### 核心属性
```cpp
class HttpServer {
private:
    HttpServerConfig config_;               // 服务器配置
    std::unique_ptr<EventLoop> event_loop_; // 事件循环
    std::unique_ptr<Socket> listen_socket_; // 监听套接字
    std::unique_ptr<ThreadPool> thread_pool_; // 线程池
    std::unique_ptr<HttpRouter> router_;    // 路由管理器
    std::vector<std::shared_ptr<HttpMiddleware>> middlewares_; // 中间件列表
    std::atomic<bool> running_;             // 运行状态
    std::mutex connections_mutex_;          // 连接管理锁
    std::unordered_map<int, std::shared_ptr<HttpSession>> connections_; // 活跃连接
};
```

#### 核心方法
- **生命周期管理**：
  - `initialize()` - 初始化服务器
  - `start()` - 启动服务器
  - `stop()` - 停止服务器
  - `restart()` - 重启服务器

- **路由注册**：
  - `get(path, handler)` - 注册GET路由
  - `post(path, handler)` - 注册POST路由
  - `put(path, handler)` - 注册PUT路由
  - `delete_(path, handler)` - 注册DELETE路由

- **中间件管理**：
  - `use(middleware)` - 添加中间件
  - `use(path, middleware)` - 添加路径特定中间件

- **静态文件服务**：
  - `serveStatic(url_path, directory)` - 配置静态文件服务

- **连接管理**：
  - `handleNewConnection(socket_fd)` - 处理新连接
  - `handleRequest(session, request)` - 处理HTTP请求
  - `closeConnection(session_id)` - 关闭连接

#### 实现特点
1. **事件驱动**：基于EventLoop的异步处理
2. **线程安全**：使用锁保护共享资源
3. **资源管理**：智能指针管理内存
4. **错误恢复**：完善的错误处理和恢复机制

### 4. HttpRouter 类

**职责**：管理路由注册、匹配和分发

#### 核心属性
```cpp
class HttpRouter {
private:
    struct RouteNode {
        std::string pattern;                // 路由模式
        HttpMethod method;                  // HTTP方法
        HandlerFunction handler;            // 处理函数
        std::vector<std::string> param_names; // 参数名列表
        bool is_wildcard;                   // 是否通配符路由
    };

    std::vector<RouteNode> routes_;         // 路由列表
    std::unordered_map<std::string, std::vector<size_t>> method_index_; // 方法索引
    std::mutex routes_mutex_;               // 路由表锁
};
```

#### 核心方法
- **路由注册**：
  - `addRoute(method, pattern, handler)` - 添加路由
  - `removeRoute(method, pattern)` - 移除路由
  - `hasRoute(method, pattern)` - 检查路由是否存在

- **路由匹配**：
  - `match(method, path)` - 匹配路由
  - `extractParams(pattern, path)` - 提取路径参数

- **路由管理**：
  - `listRoutes()` - 列出所有路由
  - `clear()` - 清空路由表

### 5. HttpSession 类

**职责**：管理单个HTTP连接的会话状态

#### 核心属性
```cpp
class HttpSession {
private:
    int socket_fd_;                         // 套接字文件描述符
    std::string session_id_;                // 会话ID
    InetAddress client_address_;            // 客户端地址
    std::string buffer_;                    // 接收缓冲区
    HttpRequest current_request_;           // 当前请求
    bool keep_alive_;                       // 是否保持连接
    std::chrono::steady_clock::time_point last_activity_; // 最后活动时间
    SessionState state_;                    // 会话状态
};
```

#### 核心方法
- **数据处理**：
  - `readData()` - 读取数据
  - `writeData(data)` - 写入数据
  - `processBuffer()` - 处理缓冲区数据

- **会话管理**：
  - `isKeepAlive()` - 检查是否保持连接
  - `updateActivity()` - 更新活动时间
  - `isExpired()` - 检查是否过期

### 6. HttpMiddleware 接口

**职责**：定义中间件接口，支持请求预处理和响应后处理

#### 核心接口
```cpp
class HttpMiddleware {
public:
    virtual ~HttpMiddleware() = default;

    // 请求预处理
    virtual bool beforeRequest(HttpRequest& request, HttpResponse& response) = 0;

    // 响应后处理
    virtual void afterResponse(const HttpRequest& request, HttpResponse& response) = 0;

    // 错误处理
    virtual void onError(const HttpRequest& request, HttpResponse& response,
                        const std::exception& error) = 0;
};
```

## 类之间的关系

### 依赖关系图
```
HttpServer (主控制器)
    ├── HttpRouter (路由管理)
    ├── HttpSession (会话管理)
    │   ├── HttpRequest (请求解析)
    │   └── HttpResponse (响应构建)
    ├── HttpMiddleware (中间件)
    ├── EventLoop (事件循环)
    ├── ThreadPool (线程池)
    └── Socket (网络通信)
```

### 关键依赖关系

1. **HttpServer → HttpRouter**
   - HttpServer使用HttpRouter进行路由匹配
   - 一对一关系，HttpServer拥有一个HttpRouter实例

2. **HttpServer → HttpSession**
   - HttpServer管理多个HttpSession
   - 一对多关系，每个连接对应一个Session

3. **HttpSession → HttpRequest/HttpResponse**
   - 每个Session包含当前的Request和Response
   - 一对一关系，Session生命周期内复用

4. **HttpServer → HttpMiddleware**
   - HttpServer管理中间件链
   - 一对多关系，支持多个中间件

5. **HttpServer → EventLoop/ThreadPool**
   - HttpServer依赖EventLoop进行事件处理
   - HttpServer使用ThreadPool进行并发处理

### 数据流向
```
客户端请求 → Socket → EventLoop → HttpServer → HttpSession → HttpRequest
                                      ↓
HttpResponse ← HttpSession ← HttpServer ← HttpRouter ← HandlerFunction
```

## 业务流程分析

### 1. 服务器启动流程

**时序图**：
```
主程序 → HttpServer → EventLoop → Socket → ThreadPool
  |         |           |          |         |
创建实例   初始化     创建事件循环  创建监听   启动线程池
  |         |           |          |         |
配置路由   注册路由     注册事件    绑定端口   准备工作线程
  |         |           |          |         |
启动服务   开始监听     运行循环    监听连接   等待任务
```

**详细步骤**：
1. **初始化阶段**：
   - `main()` → `HttpServer::HttpServer(config)` - 创建服务器实例
   - `HttpServer::HttpServer()` → `HttpRouter::HttpRouter()` - 创建路由器
   - `HttpServer::HttpServer()` → `EventLoop::EventLoop()` - 创建事件循环

2. **配置阶段**：
   - `main()` → `HttpServer::get("/api/users", handler)` - 注册GET路由
   - `HttpServer::get()` → `HttpRouter::addRoute(GET, "/api/users", handler)` - 添加路由
   - `main()` → `HttpServer::use(middleware)` - 添加中间件
   - `HttpServer::use()` → `middlewares_.push_back(middleware)` - 存储中间件

3. **启动阶段**：
   - `main()` → `HttpServer::initialize()` - 初始化服务器
   - `HttpServer::initialize()` → `Socket::create()` - 创建监听套接字
   - `HttpServer::initialize()` → `Socket::bind(host, port)` - 绑定地址端口
   - `HttpServer::initialize()` → `ThreadPool::start(thread_count)` - 启动线程池
   - `main()` → `HttpServer::start()` - 启动服务器
   - `HttpServer::start()` → `Socket::listen(backlog)` - 开始监听
   - `HttpServer::start()` → `EventLoop::run()` - 运行事件循环

### 2. 请求处理流程

**时序图**：
```
客户端 → Socket → EventLoop → HttpServer → HttpSession → HttpRequest → HttpRouter → Handler → HttpResponse
  |        |        |           |            |             |             |           |          |
发送请求  接收数据  触发事件    处理连接     解析请求      创建请求对象   路由匹配    执行处理   构建响应
  |        |        |           |            |             |             |           |          |
等待响应  发送数据  通知完成    发送响应     写入数据      序列化响应     返回结果    设置内容   发送给客户端
```

**详细步骤**：

#### 2.1 连接建立
1. `EventLoop::poll()` - 检测到新连接
2. `EventLoop::onReadable(listen_fd)` - 触发可读事件
3. `HttpServer::handleNewConnection()` - 处理新连接
   - `Socket::accept()` - 接受连接
   - `HttpSession::HttpSession(socket_fd, client_addr)` - 创建会话
   - `connections_[session_id] = session` - 存储会话

#### 2.2 数据接收
1. `EventLoop::poll()` - 检测到数据可读
2. `EventLoop::onReadable(client_fd)` - 触发客户端可读事件
3. `HttpSession::readData()` - 读取数据
   - `Socket::recv(buffer, size)` - 接收数据
   - `buffer_.append(data, bytes_read)` - 追加到缓冲区
4. `HttpSession::processBuffer()` - 处理缓冲区
   - 检查是否接收完整HTTP请求
   - 如果完整，触发请求处理

#### 2.3 请求解析
1. `HttpRequest::parseFromBytes(buffer.data(), buffer.size())` - 解析HTTP请求
   - `parseRequestLine()` - 解析请求行（方法、路径、版本）
   - `parseHeaders()` - 解析HTTP头部
   - `parseBody()` - 解析请求体（如果有）
   - `parseQueryParams()` - 解析查询参数
   - `parseCookies()` - 解析Cookie
2. `HttpRequest::setClientIP(session->getClientIP())` - 设置客户端IP
3. `HttpRequest::setParsed(true)` - 标记解析完成

#### 2.4 中间件预处理
1. `HttpServer::handleRequest(session, request)` - 开始处理请求
2. 遍历中间件列表：
   - `HttpMiddleware::beforeRequest(request, response)` - 执行前置中间件
   - 如果返回false，中断处理链
   - 如果返回true，继续下一个中间件

#### 2.5 路由匹配
1. `HttpRouter::match(request.getMethod(), request.getPath())` - 路由匹配
   - `findMatchingRoute()` - 查找匹配的路由
   - `extractPathParams()` - 提取路径参数
   - 返回匹配的处理函数

#### 2.6 处理函数执行
1. `HandlerFunction(request, response)` - 执行业务逻辑
   - 处理函数可以：
     - 读取请求参数：`request.getParam("id")`
     - 读取请求体：`request.getBody()`
     - 设置响应：`response.json(data)`
     - 设置状态码：`response.setStatus(200)`

#### 2.7 中间件后处理
1. 遍历中间件列表（逆序）：
   - `HttpMiddleware::afterResponse(request, response)` - 执行后置中间件
   - 可以修改响应头、记录日志等

#### 2.8 响应发送
1. `HttpResponse::toString()` - 序列化HTTP响应
   - `buildStatusLine()` - 构建状态行
   - `buildHeaders()` - 构建响应头
   - `buildBody()` - 构建响应体
2. `HttpSession::writeData(response_string)` - 发送响应
   - `Socket::send(data, size)` - 发送数据
3. `HttpSession::updateActivity()` - 更新活动时间
4. 根据Connection头决定是否保持连接

### 3. 静态文件服务流程

**业务场景**：客户端请求静态资源（HTML、CSS、JS、图片等）

**详细步骤**：

#### 3.1 静态路由配置
1. `main()` → `HttpServer::serveStatic("/static", "./public")` - 配置静态文件服务
2. `HttpServer::serveStatic()` → `HttpRouter::addRoute(GET, "/static/*", staticHandler)` - 注册通配符路由
3. `HttpServer::serveStatic()` → `static_routes_["/static"] = "./public"` - 存储路径映射

#### 3.2 静态文件请求处理
1. **请求接收**（同上述2.1-2.3步骤）
2. **路由匹配**：
   - `HttpRouter::match(GET, "/static/css/style.css")` - 匹配静态路由
   - 返回`handleStaticFileRequest`处理函数

3. **文件路径解析**：
   - `HttpServer::handleStaticFileRequest(request, response, "./public", "/static")` - 处理静态文件
   - `extractFilePath("/static/css/style.css", "/static")` → "css/style.css"
   - `full_path = "./public/css/style.css"` - 构建完整路径

4. **安全检查**：
   - `isPathSafe(full_path, base_directory)` - 防止目录遍历攻击
   - `getCanonicalPath(full_path)` - 获取规范化路径
   - 检查路径是否在允许的目录内

5. **文件系统检查**：
   - `fileExists(full_path)` - 检查文件是否存在
   - `hasFileReadPermission(full_path)` - 检查读取权限
   - `getFileInfo(full_path)` - 获取文件信息（大小、修改时间等）

6. **缓存处理**：
   - `handleConditionalRequest(request, response, file_info)` - 处理条件请求
   - 检查`If-Modified-Since`、`If-None-Match`头部
   - 如果文件未修改，返回304 Not Modified

7. **响应头设置**：
   - `setStaticFileHeaders(response, file_info, full_path)` - 设置响应头
   - `getMimeType(full_path)` - 根据扩展名确定MIME类型
   - 设置`Content-Type`、`Content-Length`、`Last-Modified`、`ETag`等

8. **文件内容处理**：
   - 如果是HEAD请求：只发送头部，不发送内容
   - 如果有Range头：`handleRangeRequest()` - 处理范围请求（断点续传）
   - 否则：`handleNormalFileRequest()` - 处理普通文件请求
   - `readFileContent(full_path)` - 读取文件内容
   - `response.setBody(file_content)` - 设置响应体

### 4. 目录浏览流程

**业务场景**：客户端请求目录，显示目录列表

**详细步骤**：

1. **目录请求检测**：
   - `isDirectory(full_path)` - 检查是否为目录
   - `findIndexFile(dir_path)` - 查找默认索引文件

2. **目录权限检查**：
   - `directoryExists(dir_path)` - 检查目录是否存在
   - `hasDirectoryReadPermission(dir_path)` - 检查目录读取权限

3. **目录内容生成**：
   - 如果启用目录浏览：`generateDirectoryListing(response, dir_path, url_path)`
   - `isDirectoryEmpty(dir_path)` - 检查目录是否为空
   - 如果为空：`generateEmptyDirectoryListing()` - 生成空目录页面
   - 如果非空：`listDirectoryContents()` - 列出目录内容

4. **HTML页面构建**：
   - `generateDirectoryStats()` - 生成目录统计信息
   - 遍历目录项，为每个文件/目录生成HTML行
   - `getFileIcon(filename)` - 获取文件类型图标
   - `formatFileSize(size)` - 格式化文件大小
   - `getFileTypeDescription(filename)` - 获取文件类型描述

### 5. 错误处理流程

**业务场景**：处理各种错误情况

**详细步骤**：

1. **404 Not Found**：
   - `HttpRouter::match()` 返回空 → 路由未找到
   - `HttpServer::handleRequest()` → `response.notFound("Page not found")`
   - `HttpResponse::notFound()` → 设置404状态码和错误页面

2. **500 Internal Server Error**：
   - 处理函数抛出异常 → `catch(std::exception& e)`
   - `HttpMiddleware::onError(request, response, error)` - 中间件错误处理
   - `response.internalServerError("Internal server error")` - 设置500错误

3. **403 Forbidden**：
   - `hasFileReadPermission()` 返回false → 权限不足
   - `response.forbidden("Access denied")` - 设置403错误

4. **413 Request Entity Too Large**：
   - `file_info.size > config_.max_static_file_size` → 文件过大
   - `response.requestEntityTooLarge("File too large")` - 设置413错误

## 实际应用示例

### 示例1：RESTful API服务

```cpp
// 创建服务器
HttpServerConfig config;
config.host = "127.0.0.1";
config.port = 8080;
HttpServer server(config);

// 注册API路由
server.get("/api/users", [](const HttpRequest& req, HttpResponse& res) {
    // 业务逻辑：获取用户列表
    std::string users_json = getUsersFromDatabase();
    res.json(users_json);
});

server.post("/api/users", [](const HttpRequest& req, HttpResponse& res) {
    // 业务逻辑：创建新用户
    std::string user_data = req.getBody();
    int user_id = createUser(user_data);
    res.created("{\"id\":" + std::to_string(user_id) + "}");
});

// 启动服务器
server.initialize();
server.start();
```

**执行流程**：
1. 客户端发送 `GET /api/users`
2. `EventLoop` 检测到数据 → `HttpSession::readData()`
3. `HttpRequest::parseFromBytes()` 解析请求
4. `HttpRouter::match(GET, "/api/users")` 匹配路由
5. 执行Lambda处理函数 → `getUsersFromDatabase()`
6. `HttpResponse::json()` 设置JSON响应
7. `HttpSession::writeData()` 发送响应

### 示例2：静态文件服务器

```cpp
HttpServer server(config);

// 配置静态文件服务
server.serveStatic("/", "./public");
server.serveStatic("/assets", "./assets");

// 添加日志中间件
server.use(std::make_shared<LoggingMiddleware>());

server.start();
```

**执行流程**：
1. 客户端请求 `GET /assets/css/style.css`
2. 路由匹配到静态文件处理器
3. `extractFilePath()` → "css/style.css"
4. `isPathSafe()` 安全检查
5. `fileExists()` 检查文件存在
6. `getMimeType()` → "text/css"
7. `readFileContent()` 读取文件
8. 发送CSS文件给客户端

### 示例3：完整的Web应用

```cpp
class WebApplication {
public:
    void run() {
        HttpServer server(config_);

        // 配置中间件
        server.use(std::make_shared<CorsMiddleware>());
        server.use(std::make_shared<AuthMiddleware>());

        // 配置路由
        setupRoutes(server);

        // 配置静态文件
        server.serveStatic("/static", "./public");

        // 启动服务器
        server.initialize();
        server.start();
    }

private:
    void setupRoutes(HttpServer& server) {
        // 用户管理
        server.get("/api/users/:id", handleGetUser);
        server.put("/api/users/:id", handleUpdateUser);
        server.delete_("/api/users/:id", handleDeleteUser);

        // 文件上传
        server.post("/api/upload", handleFileUpload);

        // 健康检查
        server.get("/health", [](const HttpRequest& req, HttpResponse& res) {
            res.ok("Server is healthy");
        });
    }
};
```

## HttpServer业务流程总结

### 核心业务流程图

```
[客户端请求]
    ↓
[Socket接收] → EventLoop::onReadable()
    ↓
[会话管理] → HttpSession::readData() → HttpSession::processBuffer()
    ↓
[请求解析] → HttpRequest::parseFromBytes() → HttpRequest::setParsed()
    ↓
[中间件预处理] → HttpMiddleware::beforeRequest() (循环执行)
    ↓
[路由匹配] → HttpRouter::match() → HttpRouter::extractParams()
    ↓
[业务处理] → HandlerFunction(request, response)
    ↓
[中间件后处理] → HttpMiddleware::afterResponse() (循环执行)
    ↓
[响应发送] → HttpResponse::toString() → HttpSession::writeData()
    ↓
[连接管理] → 保持连接或关闭连接
```

### 关键性能优化点

1. **事件驱动架构**：使用EventLoop避免线程阻塞
2. **连接复用**：HTTP Keep-Alive减少连接开销
3. **缓存机制**：静态文件缓存和条件请求
4. **线程池**：并发处理多个请求
5. **内存管理**：智能指针和对象池
6. **零拷贝**：直接内存映射大文件

### 扩展性设计

1. **中间件系统**：可插拔的功能模块
2. **路由系统**：支持参数化路由和通配符
3. **会话管理**：支持多种会话存储后端
4. **配置系统**：运行时配置热更新
5. **监控系统**：实时性能指标收集

这个HTTP模块设计体现了现代Web服务器的核心特性：高性能、高并发、可扩展性和易用性。通过清晰的类职责分离和完善的业务流程，能够支撑各种规模的Web应用需求。
