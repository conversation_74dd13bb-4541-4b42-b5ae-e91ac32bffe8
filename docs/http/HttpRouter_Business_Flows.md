# HttpRouter 业务流程详细分析

## 目录
1. [路由系统初始化流程](#路由系统初始化流程)
2. [路由注册流程](#路由注册流程)
3. [路由匹配流程](#路由匹配流程)
4. [参数路由处理流程](#参数路由处理流程)
5. [通配符路由处理流程](#通配符路由处理流程)
6. [路由树构建流程](#路由树构建流程)
7. [路由优先级管理流程](#路由优先级管理流程)

## 路由系统初始化流程

### 1. 路由器创建和初始化流程

**业务场景**：创建HTTP路由器并初始化路由树结构

**执行步骤**：

#### 步骤1：路由器实例创建
```cpp
// 调用类：main() 函数或HttpServer
// 使用函数：HttpRouter::HttpRouter()
HttpRouter router;
```

**内部执行序列**：
1. `HttpRouter::HttpRouter()` - 构造函数
2. `root_ = std::make_shared<RouteNode>()` - 创建根节点
   - `root_->type = RouteNodeType::STATIC` - 设置为静态节点
   - `root_->segment = "/"` - 设置根路径段
   - `root_->priority = 0` - 设置默认优先级
3. `routes_mutex_ = std::mutex()` - 初始化路由锁
4. `route_count_ = 0` - 初始化路由计数器

#### 步骤2：路由器系统初始化
```cpp
// 调用类：HttpRouter
// 使用函数：HttpRouter::initialize()
bool success = router.initialize();
```

**内部执行序列**：
1. `HttpRouter::initialize()`
2. 记录初始化日志：`LOG_INFO("HttpRouter initialized")`
3. 验证根节点：`if (!root_)` - 检查根节点是否存在
4. 初始化路由统计：`route_stats_.clear()`
5. 设置初始化标志：`initialized_ = true`

**结果**：路由器初始化完成，准备接受路由注册

### 2. 路由树结构初始化流程

**业务场景**：初始化基于前缀树的路由匹配结构

**执行步骤**：

#### 步骤1：根节点结构初始化
**内部执行序列**：
1. 根节点属性设置：
   - `type = RouteNodeType::STATIC` - 静态节点类型
   - `segment = "/"` - 根路径段
   - `full_pattern = "/"` - 完整路径模式
   - `priority = 0` - 默认优先级
2. 子节点容器初始化：
   - `children.clear()` - 静态子节点映射
   - `param_child = nullptr` - 参数子节点
   - `wildcard_child = nullptr` - 通配符子节点
3. 处理器初始化：`handler = nullptr` - 无默认处理器

#### 步骤2：路由方法映射初始化
**执行序列**：
1. 为每个HTTP方法创建根节点：
```cpp
for (const auto& method : {"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}) {
    method_roots_[method] = std::make_shared<RouteNode>();
    method_roots_[method]->type = RouteNodeType::STATIC;
    method_roots_[method]->segment = "/";
}
```

**结果**：路由树结构初始化完成，支持多种HTTP方法

## 路由注册流程

### 1. 静态路由注册流程

**业务场景**：注册固定路径的静态路由

**执行步骤**：

#### 步骤1：GET路由注册
```cpp
// 调用类：main() 函数或路由配置
// 使用函数：HttpRouter::get()
router.get("/api/users", userListHandler);
```

**内部执行序列**：
1. `HttpRouter::get(path, handler)`
2. `addRoute("GET", path, handler, 0)` - 调用通用路由添加方法
3. 参数验证：
   - 检查路径格式：`if (path.empty() || path[0] != '/')`
   - 检查处理器：`if (!handler)`
   - 记录错误并返回 `false`

#### 步骤2：路由添加到树结构
**执行序列**：
1. `HttpRouter::addRoute("GET", "/api/users", handler, priority)`
2. `std::lock_guard<std::mutex> lock(routes_mutex_)` - 加锁保护
3. 获取方法根节点：`auto root = getMethodRoot("GET")`
4. 分割路径：`auto segments = splitPath("/api/users")` 
   - 结果：`["api", "users"]`
5. `insertRoute(root, segments, handler, "/api/users", priority)` - 插入路由

#### 步骤3：路由节点插入
**执行序列**：
1. `insertRoute(current_node, segments, handler, full_pattern, priority)`
2. 遍历路径段：`for (const auto& segment : segments)`
3. 对于段 "api"：
   - 检查子节点：`auto it = current_node->children.find("api")`
   - 如果不存在：创建新节点
   ```cpp
   auto new_node = std::make_shared<RouteNode>();
   new_node->type = RouteNodeType::STATIC;
   new_node->segment = "api";
   current_node->children["api"] = new_node;
   ```
   - 移动到子节点：`current_node = new_node`
4. 对于段 "users"：重复相同过程
5. 在最终节点设置处理器：
   - `current_node->handler = handler`
   - `current_node->full_pattern = "/api/users"`
   - `current_node->priority = priority`

**结果**：静态路由成功注册到路由树

### 2. 参数路由注册流程

**业务场景**：注册包含路径参数的动态路由

**执行步骤**：

#### 步骤1：参数路由注册
```cpp
// 调用类：路由配置
// 使用函数：HttpRouter::get()
router.get("/api/users/:id", userDetailHandler);
```

**内部执行序列**：
1. `HttpRouter::get("/api/users/:id", handler)`
2. `addRoute("GET", "/api/users/:id", handler, 0)`
3. 路径分割：`splitPath("/api/users/:id")` 
   - 结果：`["api", "users", ":id"]`

#### 步骤2：参数节点创建
**执行序列**：
1. 遍历到段 ":id"：
2. 检测参数标识：`if (segment.starts_with(":"))`
3. 提取参数名：`std::string param_name = segment.substr(1)` - "id"
4. 创建参数节点：
```cpp
if (!current_node->param_child) {
    current_node->param_child = std::make_shared<RouteNode>();
    current_node->param_child->type = RouteNodeType::PARAM;
    current_node->param_child->segment = segment;
    current_node->param_child->param_name = param_name;
}
```
5. 移动到参数节点：`current_node = current_node->param_child`
6. 设置处理器和模式

**结果**：参数路由成功注册，支持动态路径匹配

### 3. 通配符路由注册流程

**业务场景**：注册通配符路由处理静态文件等场景

**执行步骤**：

#### 步骤1：通配符路由注册
```cpp
// 调用类：路由配置
// 使用函数：HttpRouter::get()
router.get("/static/*", staticFileHandler);
```

**内部执行序列**：
1. `HttpRouter::get("/static/*", handler)`
2. 路径分割：`splitPath("/static/*")` 
   - 结果：`["static", "*"]`

#### 步骤2：通配符节点创建
**执行序列**：
1. 遍历到段 "*"：
2. 检测通配符：`if (segment == "*")`
3. 创建通配符节点：
```cpp
if (!current_node->wildcard_child) {
    current_node->wildcard_child = std::make_shared<RouteNode>();
    current_node->wildcard_child->type = RouteNodeType::WILDCARD;
    current_node->wildcard_child->segment = "*";
}
```
4. 设置处理器：`current_node->wildcard_child->handler = handler`

**结果**：通配符路由成功注册，支持路径前缀匹配

### 4. 批量路由注册流程

**业务场景**：批量注册多个相关路由

**执行步骤**：

#### 步骤1：路由组注册
```cpp
// 调用类：路由配置
// 使用函数：HttpRouter的多个方法
router.get("/api/users", userListHandler);
router.post("/api/users", createUserHandler);
router.get("/api/users/:id", userDetailHandler);
router.put("/api/users/:id", updateUserHandler);
router.delete("/api/users/:id", deleteUserHandler);
```

#### 步骤2：路由优先级自动分配
**执行序列**：
1. 每个路由注册时自动分配优先级：
   - 静态路由：`priority = 100`
   - 参数路由：`priority = 50`
   - 通配符路由：`priority = 10`
2. 相同优先级按注册顺序排序

**结果**：相关路由被批量注册，优先级自动管理

## 路由匹配流程

### 1. 基本路由匹配流程

**业务场景**：根据HTTP请求匹配相应的路由处理器

**执行步骤**：

#### 步骤1：路由匹配入口
```cpp
// 调用类：HttpServer
// 使用函数：HttpRouter::match()
auto match_result = router.match("GET", "/api/users/123");
```

**内部执行序列**：
1. `HttpRouter::match(method, path)`
2. 获取方法根节点：`auto root = getMethodRoot(method)`
3. 如果方法不支持：返回 `RouteMatch{false, nullptr, {}}`
4. 分割请求路径：`auto segments = splitPath(path)`
5. `searchRoute(root, segments, 0, params)` - 开始路由搜索

#### 步骤2：路由树搜索
**请求示例**：`GET /api/users/123`

**执行序列**：
1. `searchRoute(current_node, segments, index, params)`
2. 检查是否到达路径末尾：`if (index >= segments.size())`
   - 如果有处理器：返回匹配结果
   - 否则：继续搜索
3. 获取当前段：`std::string segment = segments[index]` - "api"
4. 按优先级搜索子节点：
   - **静态匹配**：`auto it = current_node->children.find(segment)`
   - **参数匹配**：`if (current_node->param_child)`
   - **通配符匹配**：`if (current_node->wildcard_child)`

#### 步骤3：静态节点匹配
**执行序列**：
1. 查找静态子节点：`current_node->children.find("api")`
2. 如果找到：递归搜索 `searchRoute(child_node, segments, index + 1, params)`
3. 移动到下一个段："users"
4. 重复匹配过程

#### 步骤4：参数节点匹配
**执行序列**：
1. 当到达段 "123" 时，静态匹配失败
2. 检查参数子节点：`if (current_node->param_child)`
3. 提取参数值：`params[param_child->param_name] = segment`
   - `params["id"] = "123"`
4. 递归搜索：`searchRoute(param_child, segments, index + 1, params)`

#### 步骤5：匹配结果返回
**执行序列**：
1. 到达路径末尾且找到处理器
2. 构建匹配结果：
```cpp
RouteMatch result;
result.found = true;
result.handler = current_node->handler;
result.params = params; // {"id": "123"}
result.pattern = current_node->full_pattern; // "/api/users/:id"
```
3. 更新匹配统计：`updateMatchStats(result.pattern)`

**结果**：路由匹配成功，返回处理器和参数

### 2. 复杂路由匹配流程

**业务场景**：匹配包含多个参数的复杂路由

**执行步骤**：

#### 步骤1：复杂路由注册
```cpp
// 预先注册的路由
router.get("/api/users/:userId/orders/:orderId/items/:itemId", orderItemHandler);
```

#### 步骤2：复杂路径匹配
**请求示例**：`GET /api/users/123/orders/456/items/789`

**执行序列**：
1. 路径分割：`["api", "users", "123", "orders", "456", "items", "789"]`
2. 匹配过程：
   - "api" → 静态匹配 ✓
   - "users" → 静态匹配 ✓
   - "123" → 参数匹配 ✓ `params["userId"] = "123"`
   - "orders" → 静态匹配 ✓
   - "456" → 参数匹配 ✓ `params["orderId"] = "456"`
   - "items" → 静态匹配 ✓
   - "789" → 参数匹配 ✓ `params["itemId"] = "789"`

**结果**：复杂路由匹配成功，所有参数被正确提取

## 参数路由处理流程

### 1. 路径参数提取流程

**业务场景**：从URL路径中提取参数值

**执行步骤**：

#### 步骤1：参数路由定义
```cpp
// 路由定义示例
router.get("/api/users/:id", userHandler);
router.get("/api/users/:id/posts/:postId", postHandler);
router.get("/api/categories/:category/products/:productId", productHandler);
```

#### 步骤2：参数提取算法
**请求示例**：`GET /api/users/123/posts/456`

**执行序列**：
1. `extractParamName(":id")` - 提取参数名
   - `return segment.substr(1)` - 去掉冒号前缀
   - 结果："id"
2. 在匹配过程中保存参数：
```cpp
if (current_node->type == RouteNodeType::PARAM) {
    params[current_node->param_name] = segments[index];
    // params["id"] = "123"
    // params["postId"] = "456"
}
```

#### 步骤3：参数验证
**执行序列**：
1. 检查参数格式（可选）：
```cpp
bool validateParam(const std::string& param_name, const std::string& value) {
    if (param_name == "id" && !isNumeric(value)) {
        return false;
    }
    return true;
}
```
2. 如果验证失败：返回匹配失败

**结果**：路径参数被正确提取和验证

### 2. 参数类型转换流程

**业务场景**：将字符串参数转换为适当的数据类型

**执行步骤**：

#### 步骤1：参数类型定义
```cpp
// 在处理器中进行类型转换
class UserHandler : public HttpHandler {
public:
    void handle(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        // 获取路径参数
        std::string user_id_str = req.getParam("id");
        
        // 类型转换和验证
        try {
            int user_id = std::stoi(user_id_str);
            if (user_id <= 0) {
                res.badRequest("Invalid user ID");
                return;
            }
            
            // 处理业务逻辑
            processUser(user_id, res);
        } catch (const std::exception& e) {
            res.badRequest("Invalid user ID format");
        }
    }
};
```

#### 步骤2：参数转换工具函数
**执行序列**：
1. 提供类型转换辅助函数：
```cpp
template<typename T>
std::optional<T> convertParam(const std::string& value) {
    try {
        if constexpr (std::is_same_v<T, int>) {
            return std::stoi(value);
        } else if constexpr (std::is_same_v<T, long>) {
            return std::stol(value);
        } else if constexpr (std::is_same_v<T, double>) {
            return std::stod(value);
        }
    } catch (...) {
        return std::nullopt;
    }
}
```

**结果**：参数类型转换安全可靠

## 通配符路由处理流程

### 1. 通配符路由匹配流程

**业务场景**：处理静态文件服务等需要路径前缀匹配的场景

**执行步骤**：

#### 步骤1：通配符路由注册
```cpp
// 注册静态文件路由
router.get("/static/*", staticFileHandler);
router.get("/assets/*", assetHandler);
router.get("/uploads/*", uploadHandler);
```

#### 步骤2：通配符匹配算法
**请求示例**：`GET /static/css/style.css`

**执行序列**：
1. 路径分割：`["static", "css", "style.css"]`
2. 匹配过程：
   - "static" → 静态匹配 ✓
   - "css" → 检查通配符子节点
   - 找到通配符节点：`current_node->wildcard_child`
3. 通配符匹配：
```cpp
if (current_node->wildcard_child) {
    // 将剩余路径作为通配符参数
    std::string remaining_path = joinPath(segments, index);
    params["*"] = remaining_path; // "css/style.css"
    return RouteMatch{true, wildcard_child->handler, params};
}
```

#### 步骤3：通配符参数处理
**执行序列**：
1. 在处理器中获取通配符参数：
```cpp
class StaticFileHandler : public HttpHandler {
public:
    void handle(const HttpRequest& req, HttpResponse& res, HttpContext& ctx) override {
        std::string file_path = req.getParam("*"); // "css/style.css"
        std::string full_path = "/var/www/static/" + file_path;
        
        // 安全检查：防止路径遍历攻击
        if (file_path.find("..") != std::string::npos) {
            res.forbidden("Access denied");
            return;
        }
        
        // 服务静态文件
        serveStaticFile(full_path, res);
    }
};
```

**结果**：通配符路由成功匹配，剩余路径被正确处理

### 2. 多级通配符处理流程

**业务场景**：处理复杂的文件目录结构

**执行步骤**：

#### 步骤1：多级通配符路由
```cpp
// 支持多级目录的静态文件服务
router.get("/files/*", fileHandler);
```

#### 步骤2：深层路径处理
**请求示例**：`GET /files/documents/2024/reports/annual.pdf`

**执行序列**：
1. 通配符匹配：`params["*"] = "documents/2024/reports/annual.pdf"`
2. 路径重构：
```cpp
std::string wildcard_path = req.getParam("*");
std::vector<std::string> path_parts = splitPath(wildcard_path);
// ["documents", "2024", "reports", "annual.pdf"]
```
3. 安全验证和文件服务

**结果**：深层目录结构被正确处理

## 路由树构建流程

### 1. 前缀树构建算法

**业务场景**：构建高效的路由匹配树结构

**执行步骤**：

#### 步骤1：路由树节点结构
```cpp
struct RouteNode {
    RouteNodeType type;                    // 节点类型
    std::string segment;                   // 路径段
    std::string param_name;                // 参数名
    std::string full_pattern;              // 完整模式
    int priority;                          // 优先级
    
    std::shared_ptr<HttpHandler> handler;  // 处理器
    std::unordered_map<std::string, std::shared_ptr<RouteNode>> children; // 静态子节点
    std::shared_ptr<RouteNode> param_child;     // 参数子节点
    std::shared_ptr<RouteNode> wildcard_child;  // 通配符子节点
};
```

#### 步骤2：树构建算法
**执行序列**：
1. `buildRouteTree()` - 构建路由树
2. 对每个注册的路由：
   - 分割路径为段
   - 从根节点开始插入
   - 根据段类型创建相应节点
   - 建立父子关系

#### 步骤3：节点优化
**执行序列**：
1. 压缩单子节点路径：
```cpp
void compressPath(std::shared_ptr<RouteNode> node) {
    if (node->children.size() == 1 && !node->handler) {
        // 合并单子节点
        auto child = node->children.begin()->second;
        node->segment += "/" + child->segment;
        node->children = child->children;
        node->handler = child->handler;
    }
}
```

**结果**：高效的前缀树结构构建完成

### 2. 路由树优化流程

**业务场景**：优化路由树结构提高匹配性能

**执行步骤**：

#### 步骤1：树结构分析
**执行序列**：
1. 统计节点数量和深度
2. 分析匹配热点路径
3. 识别优化机会

#### 步骤2：性能优化
**执行序列**：
1. **路径压缩**：合并连续的单子节点
2. **优先级排序**：按匹配频率调整优先级
3. **缓存优化**：缓存热点路由匹配结果

**结果**：路由匹配性能显著提升

## 路由优先级管理流程

### 1. 优先级分配流程

**业务场景**：管理不同类型路由的匹配优先级

**执行步骤**：

#### 步骤1：默认优先级规则
**执行序列**：
1. 静态路由：`priority = 100` - 最高优先级
2. 参数路由：`priority = 50` - 中等优先级
3. 通配符路由：`priority = 10` - 最低优先级

#### 步骤2：自定义优先级
```cpp
// 手动指定优先级
router.addRoute("GET", "/api/users", handler, 200); // 超高优先级
router.addRoute("GET", "/api/*", fallbackHandler, 5); // 超低优先级
```

#### 步骤3：优先级冲突解决
**执行序列**：
1. 相同优先级按注册顺序
2. 更具体的路由优先级更高
3. 参数数量少的路由优先级更高

**结果**：路由优先级被正确管理和应用

### 2. 动态优先级调整流程

**业务场景**：根据访问频率动态调整路由优先级

**执行步骤**：

#### 步骤1：访问统计收集
**执行序列**：
1. 记录每个路由的访问次数
2. 统计匹配时间
3. 分析访问模式

#### 步骤2：优先级动态调整
**执行序列**：
1. 定期分析访问统计
2. 提升热点路由优先级
3. 降低冷门路由优先级
4. 重新构建路由树

**结果**：路由匹配性能根据实际使用情况优化

## 总结

HttpRouter系统的业务流程体现了高效路由匹配的核心特性：

1. **前缀树算法**：基于Trie树的高效路由匹配
2. **多种路由类型**：支持静态、参数、通配符路由
3. **参数提取**：自动提取和验证路径参数
4. **优先级管理**：智能的路由优先级分配和管理
5. **性能优化**：路径压缩、缓存等性能优化技术
6. **线程安全**：支持并发路由匹配和注册
7. **扩展性**：支持路由分组、中间件集成等高级特性

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。

## 外部模块依赖关系

### 1. 核心依赖模块
- **HttpHandler**: 路由匹配后的请求处理器
- **HttpRequest/HttpResponse**: 请求和响应对象
- **Logger**: 路由匹配和性能日志记录
- **nlohmann/json**: JSON数据处理支持

### 2. 标准库依赖
- **std::unordered_map**: 高效的路由节点存储
- **std::regex**: 正则表达式路由支持
- **std::mutex**: 线程安全的路由操作
- **std::memory**: 智能指针管理路由树节点

### 3. 集成特性
- **与HttpServer集成**: 作为请求分发的核心组件
- **与HttpMiddleware集成**: 支持路由级中间件
- **与监控系统集成**: 提供路由匹配性能指标
- **与配置系统集成**: 支持动态路由配置
