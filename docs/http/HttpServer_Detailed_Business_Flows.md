# HttpServer 业务流程详细分析

## 目录
1. [服务器启动流程](#服务器启动流程)
2. [连接接受流程](#连接接受流程)
3. [请求处理流程](#请求处理流程)
4. [会话管理流程](#会话管理流程)
5. [静态文件服务流程](#静态文件服务流程)
6. [服务器关闭流程](#服务器关闭流程)
7. [性能监控流程](#性能监控流程)

## 服务器启动流程

### 1. 服务器初始化流程

**业务场景**：创建和配置HTTP服务器实例

**执行步骤**：

#### 步骤1：服务器配置创建
```cpp
// 调用类：main() 函数
// 使用函数：HttpServerConfig结构体初始化
HttpServerConfig config;
config.host = "0.0.0.0";
config.port = 8080;
config.worker_threads = 4;
config.max_connections = 1000;
config.keep_alive_timeout = 60;
config.request_timeout = 30;
config.max_request_size = 1024 * 1024; // 1MB
```

#### 步骤2：服务器实例创建
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::HttpServer()
HttpServer server(config);
```

**内部执行序列**：
1. `HttpServer::HttpServer(config)` - 构造函数
2. `config_ = config` - 保存配置
3. `event_loop_ = std::make_unique<EventLoop>()` - 创建事件循环
4. `thread_pool_ = std::make_shared<ThreadPool>(config.worker_threads)` - 创建线程池
5. `router_ = std::make_unique<HttpRouter>()` - 创建路由器
6. `middleware_manager_ = std::make_unique<MiddlewareManager>()` - 创建中间件管理器
7. `sessions_ = std::make_unique<SessionManager>()` - 创建会话管理器
8. `running_ = false` - 初始化运行状态
9. `connection_count_ = 0` - 初始化连接计数

#### 步骤3：组件初始化
```cpp
// 调用类：HttpServer
// 使用函数：HttpServer::initialize()
bool success = server.initialize();
```

**内部执行序列**：
1. `HttpServer::initialize()`
2. `event_loop_->initialize()` - 初始化事件循环
3. `thread_pool_->start()` - 启动线程池
4. `router_->initialize()` - 初始化路由器
5. `middleware_manager_->initialize()` - 初始化中间件管理器
6. `sessions_->initialize()` - 初始化会话管理器
7. `initializeStaticFileService()` - 初始化静态文件服务
8. `initializePerformanceMonitor()` - 初始化性能监控
9. 记录初始化日志：`LOG_INFO("HttpServer initialized successfully")`

**结果**：HTTP服务器初始化完成，准备启动

### 2. 中间件和路由注册流程

**业务场景**：注册中间件和路由处理器

**执行步骤**：

#### 步骤1：中间件注册
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::use()
server.use(std::make_shared<CorsMiddleware>());
server.use(std::make_shared<LoggingMiddleware>());
server.use(std::make_shared<AuthMiddleware>("secret_key"));
```

**内部执行序列**：
1. `HttpServer::use(middleware)`
2. `middleware_manager_->use(middleware)` - 委托给中间件管理器
3. 中间件按注册顺序添加到执行链

#### 步骤2：路由注册
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer的路由方法
server.get("/api/users", userListHandler);
server.post("/api/users", createUserHandler);
server.get("/api/users/:id", userDetailHandler);
server.put("/api/users/:id", updateUserHandler);
server.delete("/api/users/:id", deleteUserHandler);
```

**内部执行序列**：
1. `HttpServer::get(path, handler)` - GET路由注册
2. `router_->get(path, handler)` - 委托给路由器
3. 路由被添加到路由树结构

#### 步骤3：静态文件路由配置
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::serveStatic()
server.serveStatic("/static", "/var/www/static");
server.serveStatic("/uploads", "/var/uploads");
```

**内部执行序列**：
1. `HttpServer::serveStatic(url_path, file_path)`
2. 创建静态文件处理器：`auto handler = std::make_shared<StaticFileHandler>(file_path)`
3. 注册通配符路由：`router_->get(url_path + "/*", handler)`

**结果**：中间件和路由注册完成

### 3. 服务器启动流程

**业务场景**：启动HTTP服务器开始监听连接

**执行步骤**：

#### 步骤1：服务器启动
```cpp
// 调用类：main() 函数
// 使用函数：HttpServer::start()
server.start();
```

**内部执行序列**：
1. `HttpServer::start()`
2. 检查初始化状态：`if (!initialized_)`
3. 创建监听Socket：`listen_socket_ = std::make_unique<Socket>()`
4. 绑定地址：`listen_socket_->bind(InetAddress(config_.host, config_.port))`
5. 开始监听：`listen_socket_->listen(config_.backlog)`

#### 步骤2：事件循环启动
**执行序列**：
1. 创建监听Channel：`listen_channel_ = std::make_unique<Channel>(event_loop_.get(), listen_socket_->fd())`
2. 设置读事件回调：`listen_channel_->setReadCallback([this] { handleNewConnection(); })`
3. 启用读事件：`listen_channel_->enableReading()`
4. 设置运行状态：`running_ = true`
5. 启动事件循环：`event_loop_->loop()` - 阻塞运行

#### 步骤3：启动后台服务
**执行序列**：
1. 启动性能监控线程：`startPerformanceMonitor()`
2. 启动会话清理线程：`startSessionCleanup()`
3. 启动统计收集线程：`startStatisticsCollection()`
4. 记录启动日志：`LOG_INFO("HttpServer started on " + config_.host + ":" + std::to_string(config_.port))`

**结果**：HTTP服务器成功启动并开始监听连接

## 连接接受流程

### 1. 新连接接受流程

**业务场景**：接受客户端的新连接请求

**执行步骤**：

#### 步骤1：连接事件触发
**执行序列**：
1. 客户端连接到服务器端口
2. 监听Socket变为可读状态
3. 事件循环检测到读事件
4. 调用读事件回调：`handleNewConnection()`

#### 步骤2：连接接受处理
```cpp
// 调用类：HttpServer
// 使用函数：HttpServer::handleNewConnection()
void HttpServer::handleNewConnection() {
    // 接受新连接
}
```

**内部执行序列**：
1. `HttpServer::handleNewConnection()`
2. 接受连接：`int client_fd = listen_socket_->accept(client_addr)`
3. 检查连接限制：`if (connection_count_ >= config_.max_connections)`
   - 如果超过限制：关闭连接并记录日志
   - 返回
4. 创建客户端Socket：`auto client_socket = std::make_unique<Socket>(client_fd)`
5. 设置Socket选项：
   - `client_socket->setNonBlocking()` - 设置非阻塞
   - `client_socket->setTcpNoDelay()` - 禁用Nagle算法
   - `client_socket->setKeepAlive()` - 启用Keep-Alive

#### 步骤3：会话创建
**执行序列**：
1. 生成会话ID：`std::string session_id = generateSessionId()`
2. 创建HTTP会话：`auto session = std::make_shared<HttpSession>(session_id, client_socket)`
3. 设置会话配置：
   - `session->setKeepAliveTimeout(config_.keep_alive_timeout)`
   - `session->setRequestTimeout(config_.request_timeout)`
   - `session->setMaxRequestSize(config_.max_request_size)`
4. 注册会话：`sessions_->addSession(session_id, session)`

#### 步骤4：连接事件注册
**执行序列**：
1. 创建连接Channel：`auto channel = std::make_unique<Channel>(event_loop_.get(), client_fd)`
2. 设置事件回调：
   - `channel->setReadCallback([session] { session->handleRead(); })`
   - `channel->setWriteCallback([session] { session->handleWrite(); })`
   - `channel->setCloseCallback([this, session_id] { handleConnectionClose(session_id); })`
   - `channel->setErrorCallback([this, session_id] { handleConnectionError(session_id); })`
3. 启用读事件：`channel->enableReading()`
4. 更新连接计数：`connection_count_++`

**结果**：新连接被成功接受并创建相应的会话

## 外部模块依赖关系

### 1. 核心依赖模块
- **EventLoop/Socket/Channel**: 网络事件处理和IO操作
- **ThreadPool**: 多线程请求处理
- **HttpRouter**: 路由匹配和分发
- **HttpMiddleware**: 中间件处理链
- **HttpSession**: 会话和连接管理
- **Logger**: 统一的日志记录系统

### 2. 标准库依赖
- **std::thread**: 多线程支持
- **std::atomic**: 线程安全的状态管理
- **std::mutex**: 线程同步和资源保护
- **std::chrono**: 时间测量和性能统计

### 3. 系统集成特性
- **与配置系统集成**: 支持配置热更新
- **与监控系统集成**: 提供详细的性能指标
- **与日志系统集成**: 统一的日志记录和管理
- **与任务调度集成**: 支持定时任务和后台服务
