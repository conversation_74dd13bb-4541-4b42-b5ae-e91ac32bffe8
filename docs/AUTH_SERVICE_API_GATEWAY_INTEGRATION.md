# 认证服务与API网关集成文档

## 概述

本文档详细说明了如何将auth_service认证服务集成到api_gateway中，实现统一的服务入口、负载均衡、服务发现和路由管理。

## 架构设计

### 整体架构
```
客户端请求 → API网关 → 认证服务
    ↓           ↓         ↓
  路由匹配   负载均衡   业务处理
    ↓           ↓         ↓
  服务发现   健康检查   响应返回
```

### 核心组件

1. **API网关 (api_gateway)**
   - 统一入口点 (端口: 8080)
   - 路由管理和请求转发
   - 负载均衡 (轮询、加权轮询、最少连接)
   - 服务发现和健康检查
   - 限流和熔断保护

2. **认证服务 (auth_service)**
   - 用户认证和授权 (端口: 8008)
   - JWT令牌管理
   - 游戏服务器管理
   - 自动注册到API网关

## 配置说明

### API网关配置 (config/api_gateway_config_example.yaml)

```yaml
# 服务发现配置
service_discovery:
  enable: true                         # 启用服务发现
  health_check_interval: 30            # 健康检查间隔（秒）
  service_timeout: 60                  # 服务超时时间（秒）
  health_check_timeout_ms: 5000        # 健康检查超时时间（毫秒）
  max_retry_count: 3                   # 最大重试次数
  auto_deregister_unhealthy: true      # 自动注销不健康服务

# 路由配置
routes:
  # 认证服务路由
  auth_service_route:
    path: "/api/v1/auth/*"
    service: "auth-service"
    target_host: "localhost"
    target_port: 8008
    methods: "GET,POST,PUT,DELETE,OPTIONS"
    timeout: 30
    retry_count: 3
    enable_circuit_breaker: true
    enable_rate_limiting: true
    priority: 120
    enabled: true

  # 游戏认证路由
  game_auth_route:
    path: "/api/v1/game/*"
    service: "auth-service"
    target_host: "localhost"
    target_port: 8008
    methods: "GET,POST,PUT,DELETE,OPTIONS"
    timeout: 30
    retry_count: 3
    priority: 115
    enabled: true
```

### 认证服务配置 (config/auth_service_config.yml)

```yaml
# 服务发现配置
service_discovery:
  enable: true
  api_gateway:
    host: "localhost"
    port: 8080
    register_endpoint: "/api/v1/services/register"
    heartbeat_endpoint: "/api/v1/services/heartbeat"
  heartbeat_interval: 30
  health_check_endpoint: "/health"
```

## API端点

### 服务发现端点 (API网关提供)

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/services/register` | 服务注册 |
| DELETE | `/api/v1/services/deregister` | 服务注销 |
| POST | `/api/v1/services/heartbeat` | 服务心跳 |
| GET | `/api/v1/services` | 服务列表查询 |
| GET | `/api/v1/services/stats` | 服务统计信息 |

### 认证服务端点 (通过API网关访问)

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/auth/register` | 用户注册 |
| POST | `/api/v1/auth/login` | 用户登录 |
| POST | `/api/v1/auth/logout` | 用户登出 |
| POST | `/api/v1/auth/refresh` | 刷新令牌 |
| POST | `/api/v1/auth/validate` | 验证令牌 |
| POST | `/api/v1/game/login` | 游戏登录 |
| GET | `/api/v1/game/servers` | 获取游戏服务器列表 |

## 负载均衡策略

### 1. 轮询 (Round Robin)
- 默认策略
- 依次将请求分发给各个服务实例
- 适用于服务实例性能相近的场景

### 2. 加权轮询 (Weighted Round Robin)
- 根据服务实例权重分发请求
- 权重高的实例获得更多请求
- 适用于服务实例性能差异较大的场景

### 3. 最少连接 (Least Connections)
- 将请求分发给当前连接数最少的实例
- 动态调整负载分配
- 适用于请求处理时间差异较大的场景

## 服务发现流程

### 1. 服务注册
```json
POST /api/v1/services/register
{
  "service_name": "auth-service",
  "service_version": "1.0.0",
  "host": "localhost",
  "port": 8008,
  "health_check_endpoint": "/health",
  "endpoints": [
    "/api/v1/auth/register",
    "/api/v1/auth/login",
    "/api/v1/game/servers"
  ],
  "weight": 1,
  "metadata": {
    "region": "us-east-1",
    "zone": "a"
  }
}
```

### 2. 健康检查
- API网关定期向服务实例发送健康检查请求
- 检查间隔: 30秒 (可配置)
- 超时时间: 5秒 (可配置)
- 不健康的实例自动从负载均衡器中移除

### 3. 心跳机制
```json
POST /api/v1/services/heartbeat
{
  "service_name": "auth-service",
  "host": "localhost",
  "port": 8008
}
```

## 启动和测试

### 1. 编译项目
```bash
mkdir build && cd build
cmake ..
make auth_service api_gateway
```

### 2. 启动服务
```bash
# 使用提供的启动脚本
chmod +x scripts/start_auth_gateway_demo.sh
./scripts/start_auth_gateway_demo.sh

# 或手动启动
# 1. 启动API网关
./build/src/core_services/api_gateway/api_gateway

# 2. 启动认证服务
./build/src/core_services/auth_service/auth_service
```

### 3. 测试API

#### 检查服务状态
```bash
# 网关健康检查
curl http://localhost:8080/health

# 查看注册的服务
curl http://localhost:8080/api/v1/services

# 服务统计信息
curl http://localhost:8080/api/v1/services/stats
```

#### 用户注册和登录
```bash
# 用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "testuser",
    "password": "testpass123",
    "email": "<EMAIL>"
  }'

# 用户登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "testuser",
    "password": "testpass123"
  }'
```

#### 游戏相关API
```bash
# 获取游戏服务器列表
curl http://localhost:8080/api/v1/game/servers

# 游戏登录
curl -X POST http://localhost:8080/api/v1/game/login \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer <JWT_TOKEN>' \
  -d '{
    "game_type": "snake",
    "server_preference": "low_latency"
  }'
```

## 监控和日志

### 日志文件位置
- API网关日志: `logs/api_gateway.log`
- 认证服务日志: `logs/auth_service.log`

### 关键监控指标
- 服务注册数量
- 健康检查成功率
- 请求转发延迟
- 负载均衡分布
- 熔断器状态

## 故障排除

### 常见问题

1. **服务注册失败**
   - 检查API网关是否正常运行
   - 验证网络连接和端口配置
   - 查看认证服务日志中的错误信息

2. **健康检查失败**
   - 确认服务实例的健康检查端点可访问
   - 检查防火墙和网络配置
   - 验证健康检查超时设置

3. **请求转发失败**
   - 检查路由配置是否正确
   - 验证目标服务是否正常运行
   - 查看负载均衡器状态

### 调试命令
```bash
# 查看服务注册状态
curl http://localhost:8080/api/v1/services?service=auth-service

# 查看服务统计
curl http://localhost:8080/api/v1/services/stats

# 测试直接访问认证服务
curl http://localhost:8008/health
```

## 编译和构建

### 1. 编译项目
```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake ..

# 编译项目
make -j$(nproc)

# 或者只编译需要的服务
make auth_service api_gateway
```

### 2. 验证编译结果
```bash
# 检查可执行文件
ls -la bin/auth_service bin/api_gateway

# 或者在构建目录中查找
find . -name "auth_service" -o -name "api_gateway"
```

## 完整的集成验证

### 1. 启动服务
```bash
# 使用提供的启动脚本
chmod +x scripts/start_auth_gateway_demo.sh
./scripts/start_auth_gateway_demo.sh
```

### 2. 验证服务注册
```bash
# 检查API网关健康状态
curl -v http://localhost:8080/health

# 查看已注册的服务
curl -v http://localhost:8080/api/v1/services

# 查看服务统计信息
curl -v http://localhost:8080/api/v1/services/stats
```

### 3. 测试认证功能
```bash
# 用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "testuser",
    "password": "TestPass123!",
    "email": "<EMAIL>"
  }'

# 用户登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "testuser",
    "password": "TestPass123!"
  }'

# 保存返回的JWT token
export JWT_TOKEN="<从登录响应中获取的token>"

# 验证token
curl -X POST http://localhost:8080/api/v1/auth/validate \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{}'
```

### 4. 测试游戏相关API
```bash
# 获取游戏服务器列表（公开接口）
curl -v http://localhost:8080/api/v1/game/servers

# 游戏登录（需要认证）
curl -X POST http://localhost:8080/api/v1/game/login \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "game_type": "snake",
    "server_preference": "low_latency"
  }'
```

## 故障排除和调试

### 1. 常见编译错误

#### 缺少依赖库
```bash
# Ubuntu/Debian
sudo apt-get install build-essential cmake libssl-dev libmysqlclient-dev

# CentOS/RHEL
sudo yum install gcc-c++ cmake openssl-devel mysql-devel
```

#### nlohmann/json库问题
```bash
# 如果系统没有nlohmann/json库
git submodule update --init --recursive
# 或者手动安装
sudo apt-get install nlohmann-json3-dev
```

### 2. 运行时问题

#### 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep -E ':(8080|8008)'
lsof -i :8080
lsof -i :8008

# 修改配置文件中的端口设置
```

#### 数据库连接问题
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查Redis服务状态
systemctl status redis

# 验证数据库连接
mysql -u root -p -e "SHOW DATABASES;"
redis-cli ping
```

#### 日志分析
```bash
# 查看API网关日志
tail -f logs/api_gateway.log

# 查看认证服务日志
tail -f logs/auth_service.log

# 搜索错误信息
grep -i error logs/*.log
grep -i "failed" logs/*.log
```

### 3. 性能调优

#### 连接池配置
```yaml
# config/auth_service_config.yml
database:
  mysql:
    max_connections: 20
    min_connections: 5
  redis:
    max_connections: 10
    min_connections: 2
```

#### 线程池配置
```yaml
# config/api_gateway_config_example.yaml
thread_pool:
  core_threads: 4
  max_threads: 16
  queue_size: 1000
```

## 扩展和优化

### 1. 多实例部署
```bash
# 启动多个认证服务实例
./bin/auth_service --port=8008 --config=config/auth_service_8008.yml &
./bin/auth_service --port=8009 --config=config/auth_service_8009.yml &
./bin/auth_service --port=8010 --config=config/auth_service_8010.yml &

# API网关会自动发现并负载均衡
```

### 2. 服务发现增强
- 集成Consul或Etcd
- 支持跨数据中心服务发现
- 实现服务依赖管理

### 3. 安全增强
- 实现服务间认证
- 添加API密钥验证
- 支持mTLS通信

### 4. 性能优化
- 连接池管理
- 请求缓存
- 异步处理优化

## 总结

本集成方案实现了：

1. **完整的服务发现机制**：认证服务自动注册到API网关
2. **智能负载均衡**：支持多种负载均衡策略
3. **健康检查和故障转移**：自动检测和处理服务故障
4. **统一的API入口**：所有请求通过API网关统一处理
5. **完善的监控和日志**：提供详细的服务状态和性能指标

该方案为微服务架构提供了坚实的基础，支持高可用、高性能的服务部署和管理。
