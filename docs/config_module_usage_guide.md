# Config模块使用指南

## 概述

Config模块是一个功能强大的统一配置管理器，提供线程安全的配置管理功能，支持多种配置源和热重载。该模块采用单例模式设计，确保全局配置的一致性。

## 核心特性

### 🎯 主要功能
- **多格式支持**: YAML、JSON、环境变量
- **线程安全**: 支持多线程并发读写
- **热重载**: 配置文件变更自动重载
- **类型安全**: 自动类型转换和验证
- **变更监听**: 配置变更回调机制
- **批量操作**: 支持前缀查询和批量获取
- **持久化**: 配置保存到文件
- **验证机制**: 配置有效性验证

### 🚀 性能特点
- **高性能**: 读取速度 >100K ops/sec
- **低延迟**: 写入速度 >10K ops/sec
- **高并发**: 并发读取 >50K ops/sec
- **内存高效**: 支持10万+配置项

## 快速开始

### 1. 基本使用

```cpp
#include "common/config/config_manager.h"

using namespace common::config;

// 获取配置管理器实例
auto& config = ConfigManager::getInstance();

// 设置配置值
config.set("database.host", "localhost");
config.set("database.port", 3306);
config.set("debug_mode", true);

// 获取配置值
std::string host = config.get<std::string>("database.host");
int port = config.get<int>("database.port", 3306);  // 带默认值
bool debug = config.get<bool>("debug_mode", false);

// 检查配置是否存在
if (config.has("database.password")) {
    std::string password = config.get<std::string>("database.password");
}
```

### 2. 配置文件加载

#### YAML配置文件
```yaml
# config.yml
database:
  host: "localhost"
  port: 3306
  user: "admin"
  password: "secret"

server:
  bind_address: "0.0.0.0"
  port: 8080
  max_connections: 1000

logging:
  level: "INFO"
  file_path: "/var/log/app.log"
  console_enabled: true
```

```cpp
// 加载YAML配置
bool success = config.loadFromYaml("config.yml");
if (success) {
    std::string db_host = config.get<std::string>("database.host");
    int server_port = config.get<int>("server.port");
}
```

#### JSON配置文件
```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "user": "admin"
  },
  "server": {
    "port": 8080,
    "max_connections": 1000
  }
}
```

```cpp
// 加载JSON配置
config.loadFromJson("config.json");
```

#### 环境变量
```bash
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export LOG_LEVEL=DEBUG
```

```cpp
// 加载环境变量
config.loadFromEnv();
std::string mysql_host = config.get<std::string>("mysql_host");
```

### 3. 自动格式检测
```cpp
// 根据文件扩展名自动选择加载方式
config.loadFromFile("config.yml");   // 自动识别为YAML
config.loadFromFile("config.json");  // 自动识别为JSON
```

## 高级功能

### 1. 配置变更监听

```cpp
// 添加配置变更监听器
config.addChangeListener("database.host", 
    [](const std::string& key, const std::string& old_val, const std::string& new_val) {
        std::cout << "配置 " << key << " 从 " << old_val << " 变更为 " << new_val << std::endl;
    });

// 修改配置会触发监听器
config.set("database.host", "new-host");

// 移除监听器
config.removeChangeListeners("database.host");
```

### 2. 热重载功能

```cpp
// 启用热重载，每5秒检查一次文件变更
config.enableHotReload("config.yml", std::chrono::seconds(5));

// 配置文件变更时会自动重载并触发变更监听器

// 禁用热重载
config.disableHotReload();
```

### 3. 批量操作

```cpp
// 获取所有配置
auto all_configs = config.getAllConfigs();
for (const auto& [key, value] : all_configs) {
    std::cout << key << " = " << value << std::endl;
}

// 按前缀获取配置
auto db_configs = config.getConfigsByPrefix("database.");
// 返回: database.host, database.port, database.user 等
```

### 4. 配置验证

```cpp
// 验证所有配置
bool is_valid = config.validate();
if (!is_valid) {
    auto errors = config.getValidationErrors();
    for (const auto& error : errors) {
        std::cerr << "配置错误: " << error << std::endl;
    }
}
```

### 5. 配置持久化

```cpp
// 保存配置到文件
config.saveToYaml("output.yml");
config.saveToJson("output.json");

// 自动选择格式保存
config.saveToFile("output.yml");  // 根据扩展名选择格式
```

## 配置结构体

Config模块提供了预定义的配置结构体，方便统一管理各模块配置：

### 1. 数据库配置

```cpp
// 使用DatabaseConfig结构体
auto db_config = DatabaseConfig::fromConfigManager();

// 访问配置
std::cout << "MySQL Host: " << db_config.mysql_host << std::endl;
std::cout << "MySQL Port: " << db_config.mysql_port << std::endl;
std::cout << "Pool Size: " << db_config.mysql_pool_size << std::endl;

// 验证配置
try {
    db_config.validate();
    std::cout << "数据库配置有效" << std::endl;
} catch (const std::exception& e) {
    std::cerr << "数据库配置无效: " << e.what() << std::endl;
}
```

### 2. Redis配置

```cpp
auto redis_config = RedisConfig::fromConfigManager();
std::cout << "Redis Host: " << redis_config.redis_host << std::endl;
std::cout << "Redis Port: " << redis_config.redis_port << std::endl;
```

### 3. 服务器配置

```cpp
auto server_config = ServerConfig::fromConfigManager();
std::cout << "Server Port: " << server_config.server_port << std::endl;
std::cout << "Max Connections: " << server_config.max_connections << std::endl;
```

### 4. 日志配置

```cpp
auto log_config = LogConfig::fromConfigManager();
std::cout << "Log Level: " << log_config.level << std::endl;
std::cout << "Console Enabled: " << log_config.console_enabled << std::endl;
```

### 5. Kafka配置

```cpp
// Kafka生产者配置
auto producer_config = KafkaProducerConfig::fromConfigManager();
std::cout << "Brokers: " << producer_config.brokers << std::endl;
std::cout << "Acks: " << producer_config.acks << std::endl;

// Kafka消费者配置
auto consumer_config = KafkaConsumerConfig::fromConfigManager();
std::cout << "Group ID: " << consumer_config.group_id << std::endl;
```

## 类型转换

Config模块支持自动类型转换，支持以下类型：

### 基本类型
```cpp
// 字符串
std::string str_val = config.get<std::string>("key");

// 整数类型
int int_val = config.get<int>("port");
long long_val = config.get<long>("large_number");
size_t size_val = config.get<size_t>("buffer_size");

// 浮点类型
float float_val = config.get<float>("ratio");
double double_val = config.get<double>("precision_value");

// 布尔类型
bool bool_val = config.get<bool>("enabled");
```

### 布尔值识别
支持多种布尔值表示：
- **true**: "true", "TRUE", "1", "yes", "YES", "on", "ON"
- **false**: "false", "FALSE", "0", "no", "NO", "off", "OFF"

### 数值格式
- **整数**: "123", "-456"
- **浮点数**: "3.14", "-2.5", "1.23e-10"
- **科学计数法**: "1.23e10", "4.56E-5"

## 环境变量映射

Config模块支持环境变量到配置键的自动映射：

### 映射规则
```bash
# 系统配置
SYSTEM_VERSION → system_version
SYSTEM_ENVIRONMENT → system_environment

# 数据库配置
MYSQL_HOST → mysql_host
MYSQL_PORT → mysql_port
MYSQL_USER → mysql_user
MYSQL_PASSWORD → mysql_password

# Redis配置
REDIS_HOST → redis_host
REDIS_PORT → redis_port

# 服务器配置
SERVER_HOST → server_host
SERVER_PORT → server_port

# 日志配置
LOG_LEVEL → log_level
LOG_FILE_PATH → log_file_path

# Kafka配置
KAFKA_BROKERS → kafka_producer_brokers
KAFKA_GROUP_ID → kafka_consumer_group_id
```

### 使用示例
```bash
# 设置环境变量
export MYSQL_HOST=production-db
export MYSQL_PORT=3306
export LOG_LEVEL=ERROR
```

```cpp
// 加载环境变量
config.loadFromEnv();

// 访问映射后的配置
std::string db_host = config.get<std::string>("mysql_host");  // "production-db"
int db_port = config.get<int>("mysql_port");                  // 3306
std::string log_level = config.get<std::string>("log_level"); // "ERROR"
```

## 配置文件格式详解

### YAML配置文件格式

YAML是一种人类可读的数据序列化标准，Config模块完全支持YAML格式。

#### 基本语法规则
```yaml
# 注释以 # 开头
key: value                    # 键值对
nested_key:                   # 嵌套结构
  sub_key: sub_value
  another_key: another_value

# 数组/列表
array_key:
  - item1
  - item2
  - item3

# 或者内联数组
inline_array: [item1, item2, item3]

# 多行字符串
multiline_string: |
  这是一个
  多行字符串
  保持换行符

# 折叠字符串
folded_string: >
  这是一个
  折叠字符串
  换行符被替换为空格
```

#### 数据类型支持
```yaml
# 字符串 (可以有引号或无引号)
string_value: "Hello World"
unquoted_string: Hello World
single_quoted: 'Single quotes'

# 数字
integer_value: 42
negative_integer: -123
float_value: 3.14159
scientific_notation: 1.23e-4

# 布尔值
boolean_true: true
boolean_false: false
yes_value: yes
no_value: no
on_value: on
off_value: off

# 空值
null_value: null
tilde_null: ~
empty_null:

# 日期时间 (ISO 8601)
date_value: 2023-12-25
datetime_value: 2023-12-25T10:30:00Z
```

#### 关键配置项说明

##### 数据库配置关键字
```yaml
database:
  mysql:
    host: "数据库主机地址"
    port: 3306                    # 端口号 (1-65535)
    user: "用户名"
    password: "密码"
    database: "数据库名"
    charset: "utf8mb4"            # 字符集
    pool_size: 10                 # 连接池大小
    max_pool_size: 50             # 最大连接池大小
    connection_timeout: 30000     # 连接超时(毫秒)
    read_timeout: 60000           # 读取超时(毫秒)
    write_timeout: 60000          # 写入超时(毫秒)
    ssl_mode: "DISABLED"          # SSL模式: DISABLED, REQUIRED, VERIFY_CA, VERIFY_IDENTITY
    autocommit: true              # 自动提交
    isolation_level: "REPEATABLE-READ"  # 隔离级别
```

##### 服务器配置关键字
```yaml
server:
  host: "0.0.0.0"               # 绑定地址
  port: 8080                    # 监听端口
  max_connections: 1000         # 最大连接数
  keep_alive_timeout: 60        # 保持连接超时(秒)
  request_timeout: 30           # 请求超时(秒)
  thread_pool_size: 8           # 线程池大小
  enable_ssl: false             # 启用SSL
  ssl_cert_file: "证书文件路径"
  ssl_key_file: "私钥文件路径"
```

##### 日志配置关键字
```yaml
logging:
  level: "INFO"                 # 日志级别: TRACE, DEBUG, INFO, WARN, ERROR, FATAL
  console_enabled: true         # 启用控制台输出
  console_colored: true         # 控制台彩色输出
  console_level: "DEBUG"        # 控制台日志级别
  file_enabled: true            # 启用文件输出
  file_path: "/var/log/app.log" # 日志文件路径
  file_level: "INFO"            # 文件日志级别
  file_max_size: 104857600      # 文件最大大小(字节)
  file_max_files: 10            # 最大文件数量
  file_rotation_policy: "size"  # 轮转策略: size, daily, hourly
  async_enabled: true           # 启用异步日志
  async_queue_size: 8192        # 异步队列大小
  async_thread_count: 2         # 异步线程数
```

##### Kafka配置关键字
```yaml
kafka:
  producer:
    brokers: "kafka1:9092,kafka2:9092"  # Broker地址列表
    client_id: "producer-client"         # 客户端ID
    acks: "all"                          # 确认模式: 0, 1, all
    retries: 3                           # 重试次数
    batch_size: 16384                    # 批处理大小
    linger_ms: 5                         # 批处理延迟(毫秒)
    compression_type: "gzip"             # 压缩类型: none, gzip, snappy, lz4
    enable_idempotence: true             # 启用幂等性
    max_in_flight_requests: 5            # 最大飞行请求数

  consumer:
    brokers: "kafka1:9092,kafka2:9092"  # Broker地址列表
    group_id: "consumer-group"           # 消费者组ID
    client_id: "consumer-client"         # 客户端ID
    auto_offset_reset: "earliest"        # 偏移量重置: earliest, latest, none
    enable_auto_commit: false            # 启用自动提交
    max_poll_records: 1000               # 最大轮询记录数
    session_timeout_ms: 30000            # 会话超时(毫秒)
    heartbeat_interval_ms: 3000          # 心跳间隔(毫秒)
    isolation_level: "read_committed"    # 隔离级别: read_uncommitted, read_committed
```

### JSON配置文件格式

JSON是一种轻量级的数据交换格式，Config模块完全支持JSON格式。

#### 基本语法规则
```json
{
  "key": "value",
  "number": 42,
  "boolean": true,
  "null_value": null,
  "array": ["item1", "item2", "item3"],
  "object": {
    "nested_key": "nested_value",
    "nested_number": 3.14
  }
}
```

#### 数据类型支持
```json
{
  "string_types": {
    "simple_string": "Hello World",
    "escaped_string": "Line 1\\nLine 2\\tTabbed",
    "unicode_string": "Unicode: \\u4e2d\\u6587",
    "empty_string": ""
  },

  "number_types": {
    "integer": 42,
    "negative_integer": -123,
    "float": 3.14159,
    "scientific_notation": 1.23e-4,
    "large_number": 9223372036854775807
  },

  "boolean_types": {
    "true_value": true,
    "false_value": false
  },

  "null_type": null,

  "array_types": {
    "string_array": ["apple", "banana", "cherry"],
    "number_array": [1, 2, 3, 4, 5],
    "mixed_array": ["string", 42, true, null],
    "nested_array": [[1, 2], [3, 4], [5, 6]]
  }
}
```

#### 完整的JSON配置示例
```json
{
  "application": {
    "name": "GameMicroservice",
    "version": "2.1.0",
    "environment": "production",
    "debug_mode": false
  },

  "database": {
    "mysql": {
      "host": "prod-mysql-cluster.internal",
      "port": 3306,
      "user": "game_service",
      "password": "${MYSQL_PASSWORD}",
      "database": "game_production",
      "charset": "utf8mb4",
      "pool_size": 20,
      "max_pool_size": 100,
      "connection_timeout": 30000,
      "ssl_mode": "REQUIRED",
      "autocommit": true,
      "isolation_level": "REPEATABLE-READ"
    },
    "redis": {
      "host": "prod-redis-cluster.internal",
      "port": 6379,
      "password": "${REDIS_PASSWORD}",
      "database": 0,
      "pool_size": 15,
      "max_pool_size": 50,
      "connection_timeout": 5000
    }
  },

  "server": {
    "http": {
      "bind_address": "0.0.0.0",
      "port": 8080,
      "max_connections": 2000,
      "keep_alive_timeout": 60,
      "request_timeout": 30
    },
    "grpc": {
      "bind_address": "0.0.0.0",
      "port": 9090,
      "max_connections": 1000
    },
    "ssl": {
      "enabled": true,
      "cert_file": "/etc/ssl/certs/server.crt",
      "key_file": "/etc/ssl/private/server.key",
      "protocols": ["TLSv1.2", "TLSv1.3"]
    }
  },

  "kafka": {
    "producer": {
      "brokers": "kafka1.internal:9092,kafka2.internal:9092",
      "client_id": "game-service-producer",
      "acks": "all",
      "retries": 2147483647,
      "batch_size": 65536,
      "linger_ms": 5,
      "compression_type": "lz4",
      "enable_idempotence": true
    },
    "consumer": {
      "brokers": "kafka1.internal:9092,kafka2.internal:9092",
      "group_id": "game-service-consumer-group",
      "client_id": "game-service-consumer",
      "auto_offset_reset": "earliest",
      "enable_auto_commit": false,
      "max_poll_records": 1000,
      "isolation_level": "read_committed"
    }
  },

  "logging": {
    "level": "INFO",
    "console": {
      "enabled": true,
      "colored": true,
      "level": "DEBUG",
      "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] %v"
    },
    "file": {
      "enabled": true,
      "path": "/var/log/game-service/app.log",
      "level": "INFO",
      "max_size": 104857600,
      "max_files": 10,
      "rotation_policy": "size"
    },
    "async": {
      "enabled": true,
      "queue_size": 8192,
      "thread_count": 2,
      "overflow_policy": "block"
    }
  },

  "thread_pool": {
    "core_size": 8,
    "max_size": 32,
    "keep_alive_ms": 300000,
    "queue_capacity": 2000,
    "rejection_policy": "caller_runs",
    "enable_monitoring": true
  }
}
```

#### JSON配置注意事项
1. **严格的语法**: JSON不允许注释，所有字符串必须用双引号
2. **数据类型限制**: 只支持string, number, boolean, null, array, object
3. **无变量引用**: 不支持环境变量引用，需要在加载后处理
4. **精确的格式**: 不允许尾随逗号，键名必须用双引号

### 环境变量配置格式

环境变量是系统级别的配置方式，适合敏感信息和部署时配置。

#### 基本格式
```bash
# 基本格式
VARIABLE_NAME=value

# 带引号的值
QUOTED_VALUE="value with spaces"
SINGLE_QUOTED='single quoted value'

# 多行值
MULTILINE_VALUE="Line 1
Line 2
Line 3"

# 特殊字符转义
ESCAPED_VALUE="Value with \"quotes\" and \\backslashes"
```

#### 支持的环境变量列表

##### 系统配置
```bash
SYSTEM_VERSION=2.1.0                    # 系统版本
SYSTEM_ENVIRONMENT=production           # 运行环境
SYSTEM_DEBUG_MODE=false                 # 调试模式
```

##### 数据库配置
```bash
# MySQL配置
MYSQL_HOST=prod-mysql-cluster.internal  # MySQL主机
MYSQL_PORT=3306                         # MySQL端口
MYSQL_USER=game_service                 # MySQL用户名
MYSQL_PASSWORD=secure_password          # MySQL密码
MYSQL_DATABASE=game_production          # MySQL数据库名
MYSQL_POOL_SIZE=20                      # 连接池大小
MYSQL_MAX_POOL_SIZE=100                 # 最大连接池大小

# Redis配置
REDIS_HOST=prod-redis-cluster.internal  # Redis主机
REDIS_PORT=6379                         # Redis端口
REDIS_PASSWORD=redis_password           # Redis密码
REDIS_DATABASE=0                        # Redis数据库编号
REDIS_POOL_SIZE=15                      # Redis连接池大小
```

##### 服务器配置
```bash
SERVER_HOST=0.0.0.0                     # 服务器绑定地址
SERVER_PORT=8080                        # 服务器端口
THREAD_POOL_SIZE=8                      # 线程池大小
MAX_CONNECTIONS=1000                    # 最大连接数
ENABLE_SSL=true                         # 启用SSL
SSL_CERT_FILE=/etc/ssl/certs/server.crt # SSL证书文件
SSL_KEY_FILE=/etc/ssl/private/server.key # SSL私钥文件
```

##### 日志配置
```bash
LOG_LEVEL=INFO                          # 日志级别
LOG_CONSOLE_ENABLED=true                # 启用控制台日志
LOG_CONSOLE_COLORED=true                # 控制台彩色输出
LOG_FILE_ENABLED=true                   # 启用文件日志
LOG_FILE_PATH=/var/log/app.log          # 日志文件路径
LOG_FILE_LEVEL=INFO                     # 文件日志级别
LOG_ASYNC_ENABLED=true                  # 启用异步日志
LOG_ASYNC_QUEUE_SIZE=8192               # 异步队列大小
```

##### Kafka配置
```bash
# 生产者配置
KAFKA_PRODUCER_BROKERS=kafka1:9092,kafka2:9092  # Broker列表
KAFKA_PRODUCER_CLIENT_ID=game-producer          # 生产者客户端ID
KAFKA_PRODUCER_ACKS=all                         # 确认模式
KAFKA_PRODUCER_RETRIES=3                        # 重试次数
KAFKA_PRODUCER_COMPRESSION_TYPE=lz4             # 压缩类型

# 消费者配置
KAFKA_CONSUMER_BROKERS=kafka1:9092,kafka2:9092  # Broker列表
KAFKA_CONSUMER_GROUP_ID=game-consumer-group     # 消费者组ID
KAFKA_CONSUMER_CLIENT_ID=game-consumer          # 消费者客户端ID
KAFKA_CONSUMER_AUTO_OFFSET_RESET=earliest       # 偏移量重置策略
```

##### 网络配置
```bash
NETWORK_BIND_ADDRESS=0.0.0.0            # 网络绑定地址
NETWORK_LISTEN_PORT=9090                # 监听端口
NETWORK_WORKER_THREADS=8                # 工作线程数
NETWORK_IO_THREADS=4                    # IO线程数
NETWORK_MAX_CONNECTIONS=10000           # 最大连接数
```

##### 线程池配置
```bash
THREAD_POOL_CORE_SIZE=8                 # 核心线程数
THREAD_POOL_MAX_SIZE=32                 # 最大线程数
THREAD_POOL_KEEP_ALIVE_MS=300000        # 线程保活时间
THREAD_POOL_QUEUE_CAPACITY=2000         # 队列容量
THREAD_POOL_REJECTION_POLICY=caller_runs # 拒绝策略
```

#### 环境变量使用示例

##### Docker Compose配置
```yaml
version: '3.8'
services:
  game-service:
    image: game-service:latest
    environment:
      - SYSTEM_ENVIRONMENT=production
      - MYSQL_HOST=mysql-cluster
      - MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - REDIS_HOST=redis-cluster
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - LOG_LEVEL=INFO
      - KAFKA_PRODUCER_BROKERS=kafka1:9092,kafka2:9092
    env_file:
      - .env.production
```

##### Kubernetes ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-service-config
data:
  SYSTEM_ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  THREAD_POOL_SIZE: "8"
  MAX_CONNECTIONS: "1000"
---
apiVersion: v1
kind: Secret
metadata:
  name: game-service-secrets
type: Opaque
stringData:
  MYSQL_PASSWORD: "secure_mysql_password"
  REDIS_PASSWORD: "secure_redis_password"
  JWT_SECRET_KEY: "jwt_secret_key_here"
```

##### Shell脚本配置
```bash
#!/bin/bash
# 设置生产环境变量

export SYSTEM_ENVIRONMENT=production
export SYSTEM_DEBUG_MODE=false

# 数据库配置
export MYSQL_HOST=prod-mysql-cluster.internal
export MYSQL_PORT=3306
export MYSQL_USER=game_service
export MYSQL_PASSWORD=$(cat /run/secrets/mysql_password)
export MYSQL_DATABASE=game_production

# Redis配置
export REDIS_HOST=prod-redis-cluster.internal
export REDIS_PORT=6379
export REDIS_PASSWORD=$(cat /run/secrets/redis_password)

# 启动应用
./game-service
```

### 配置文件优先级

Config模块支持多种配置源，按以下优先级加载（后加载的覆盖先加载的）：

1. **默认配置** - 代码中的硬编码默认值
2. **YAML/JSON文件** - 配置文件中的值
3. **环境变量** - 系统环境变量
4. **运行时设置** - 程序运行时通过API设置的值

#### 配置覆盖示例
```cpp
// 1. 默认配置
auto& config = ConfigManager::getInstance();
// system.version = "1.0.0" (默认值)

// 2. 加载YAML文件
config.loadFromYaml("config.yml");
// system.version = "2.0.0" (来自YAML文件)

// 3. 加载环境变量
config.loadFromEnv();
// system.version = "2.1.0" (来自环境变量 SYSTEM_VERSION)

// 4. 运行时设置
config.set("system.version", "2.1.1");
// system.version = "2.1.1" (运行时设置)
```

### 配置验证规则

Config模块提供内置的配置验证功能，确保配置值的有效性。

#### 端口号验证
- **范围**: 1-65535
- **示例**: `mysql_port`, `server_port`, `redis_port`

#### 日志级别验证
- **有效值**: TRACE, DEBUG, INFO, WARN, ERROR, FATAL
- **大小写**: 不敏感

#### 布尔值验证
- **true值**: true, TRUE, 1, yes, YES, on, ON
- **false值**: false, FALSE, 0, no, NO, off, OFF

#### 文件路径验证
- **检查**: 文件是否存在，是否有读取权限
- **示例**: `ssl_cert_file`, `log_file_path`

#### 网络地址验证
- **格式**: IPv4地址或主机名
- **示例**: `mysql_host`, `redis_host`

### 配置最佳实践

#### 1. 配置文件组织
```
config/
├── default.yml          # 默认配置
├── development.yml      # 开发环境配置
├── testing.yml          # 测试环境配置
├── staging.yml          # 预发布环境配置
├── production.yml       # 生产环境配置
└── local.yml            # 本地开发者配置 (git ignore)
```

#### 2. 敏感信息处理
```yaml
# 配置文件中使用环境变量引用
database:
  mysql:
    password: "${MYSQL_PASSWORD}"  # 从环境变量获取

# 或者使用占位符
#database:
#  mysql:
#    password: "{{MYSQL_PASSWORD}}"  # 需要后处理替换
```

#### 3. 配置分层加载
```cpp
// 推荐的配置加载顺序
config.loadFromYaml("config/default.yml");           // 基础配置
config.loadFromYaml("config/" + environment + ".yml"); // 环境特定配置
config.loadFromYaml("config/local.yml");             // 本地覆盖配置
config.loadFromEnv();                                // 环境变量覆盖
```

通过遵循这些格式规范和最佳实践，可以确保Config模块的正确使用和高效管理。
