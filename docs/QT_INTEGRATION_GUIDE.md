# Qt/C++集成快速指南

## 概述

本指南专门为使用Qt/C++开发桌面应用的开发者提供与微服务API的集成方案。

## 环境要求

- **Qt版本**: Qt 6.0+
- **C++标准**: C++17或更高
- **网络模块**: Qt Network
- **构建系统**: CMake 3.16+ 或 qmake

## 快速开始

### 1. 项目配置

#### CMakeLists.txt
```cmake
cmake_minimum_required(VERSION 3.16)
project(GameClient VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)
qt6_standard_project_setup()

qt6_add_executable(GameClient
    main.cpp
    mainwindow.cpp
    apiclient.cpp
    authservice.cpp
    gameservice.cpp
)

target_link_libraries(GameClient Qt6::Core Qt6::Widgets Qt6::Network)
```

#### .pro文件
```pro
QT += core widgets network
CONFIG += c++17
TARGET = GameClient
TEMPLATE = app

SOURCES += main.cpp mainwindow.cpp apiclient.cpp authservice.cpp gameservice.cpp
HEADERS += mainwindow.h apiclient.h authservice.h gameservice.h
```

### 2. 核心API客户端

```cpp
// apiclient.h
#include <QObject>
#include <QNetworkAccessManager>
#include <QJsonObject>

class ApiClient : public QObject {
    Q_OBJECT
public:
    struct ApiResponse {
        bool success = false;
        int statusCode = 0;
        QJsonObject data;
        QString errorMessage;
        QString errorCode;
    };

    explicit ApiClient(const QString &baseUrl, QObject *parent = nullptr);
    
    void get(const QString &endpoint, const QJsonObject &params = {});
    void post(const QString &endpoint, const QJsonObject &data);
    void setToken(const QString &token);
    bool isAuthenticated() const;

signals:
    void requestFinished(const ApiResponse &response);
    void authenticationRequired();

private:
    QNetworkAccessManager *m_networkManager;
    QString m_baseUrl;
    QString m_accessToken;
};
```

### 3. 认证服务

```cpp
// authservice.h
class AuthService : public QObject {
    Q_OBJECT
public:
    struct UserInfo {
        int userId = 0;
        QString username;
        QString email;
        QString status;
    };

    explicit AuthService(QObject *parent = nullptr);
    
    void login(const QString &username, const QString &password, bool rememberMe = false);
    void registerUser(const QString &username, const QString &password, const QString &email);
    void logout(bool allDevices = false);
    bool isAuthenticated() const;
    UserInfo getCurrentUser() const;

signals:
    void loginSucceeded(const UserInfo &userInfo);
    void loginFailed(const QString &error);
    void registerSucceeded();
    void registerFailed(const QString &error);
    void logoutCompleted();

private:
    ApiClient *m_apiClient;
    UserInfo m_currentUser;
};
```

### 4. 游戏服务

```cpp
// gameservice.h
class GameService : public QObject {
    Q_OBJECT
public:
    struct GameServer {
        QString serverId;
        QString name;
        QString host;
        int port = 0;
        QString gameType;
        QString region;
        QString status;
        int maxPlayers = 0;
        int currentPlayers = 0;
        int ping = 0;
    };

    explicit GameService(QObject *parent = nullptr);
    
    void getGameServers(const QString &gameType = "", const QString &region = "");
    void joinGame(const QString &gameType, const QString &serverPreference = "balanced");

signals:
    void gameServersReceived(const QList<GameServer> &servers);
    void gameJoinSucceeded(const QJsonObject &result);
    void gameJoinFailed(const QString &error);

private:
    ApiClient *m_apiClient;
};
```

## 使用示例

### 基础认证流程

```cpp
// main.cpp
#include <QApplication>
#include "mainwindow.h"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    app.setApplicationName("GameClient");
    app.setApplicationVersion("1.0.0");
    
    MainWindow window;
    window.show();
    
    return app.exec();
}

// mainwindow.cpp
MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) {
    m_authService = new AuthService(this);
    
    connect(m_authService, &AuthService::loginSucceeded,
            this, &MainWindow::onLoginSucceeded);
    connect(m_authService, &AuthService::loginFailed,
            this, &MainWindow::onLoginFailed);
    
    setupUI();
}

void MainWindow::onLoginClicked() {
    QString username = m_usernameEdit->text();
    QString password = m_passwordEdit->text();
    
    m_authService->login(username, password);
}

void MainWindow::onLoginSucceeded(const AuthService::UserInfo &userInfo) {
    statusBar()->showMessage("欢迎, " + userInfo.username);
    // 切换到主界面
    showGameInterface();
}
```

### 游戏服务器列表

```cpp
void MainWindow::loadGameServers() {
    m_gameService = new GameService(this);
    
    connect(m_gameService, &GameService::gameServersReceived,
            this, &MainWindow::onGameServersReceived);
    
    m_gameService->getGameServers("snake", "asia");
}

void MainWindow::onGameServersReceived(const QList<GameService::GameServer> &servers) {
    m_serverTable->setRowCount(servers.size());
    
    for (int i = 0; i < servers.size(); ++i) {
        const auto &server = servers[i];
        m_serverTable->setItem(i, 0, new QTableWidgetItem(server.name));
        m_serverTable->setItem(i, 1, new QTableWidgetItem(server.gameType));
        m_serverTable->setItem(i, 2, new QTableWidgetItem(QString("%1ms").arg(server.ping)));
        m_serverTable->setItem(i, 3, new QTableWidgetItem(QString("%1/%2").arg(server.currentPlayers).arg(server.maxPlayers)));
    }
}

void MainWindow::onJoinGameClicked() {
    int row = m_serverTable->currentRow();
    if (row >= 0) {
        GameService::GameServer server = m_servers[row];
        m_gameService->joinGame(server.gameType, "low_latency");
    }
}
```

## 错误处理

### 统一错误处理

```cpp
// errorhandler.h
class ErrorHandler : public QObject {
    Q_OBJECT
public:
    static QString getErrorMessage(const QString &errorCode);
    static void showErrorDialog(QWidget *parent, const QString &error);
    static void logError(const QString &context, const QString &error);
};

// 使用示例
void MainWindow::onLoginFailed(const QString &error) {
    ErrorHandler::showErrorDialog(this, error);
    ErrorHandler::logError("Login", error);
}
```

### 网络错误处理

```cpp
void ApiClient::onReplyFinished() {
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    
    if (reply->error() != QNetworkReply::NoError) {
        ApiResponse response;
        response.success = false;
        response.errorMessage = reply->errorString();
        response.statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
        
        emit requestFinished(response);
        return;
    }
    
    // 处理成功响应
    handleSuccessResponse(reply);
}
```

## 最佳实践

### 1. 内存管理
```cpp
// 使用智能指针
std::unique_ptr<AuthService> m_authService;

// 或者使用Qt的父子关系
AuthService *m_authService = new AuthService(this);
```

### 2. 异步操作
```cpp
// 所有网络操作都是异步的
void AuthService::login(const QString &username, const QString &password) {
    QJsonObject data;
    data["username"] = username;
    data["password"] = password;
    
    // 异步发送请求
    m_apiClient->post("/api/v1/auth/login", data);
}

// 通过信号槽处理结果
connect(authService, &AuthService::loginSucceeded, this, &MainWindow::onLoginSucceeded);
```

### 3. 配置管理
```cpp
// config.h
class Config {
public:
    static QString getApiBaseUrl() {
#ifdef QT_DEBUG
        return "http://localhost:8080";
#else
        return "https://api.yourdomain.com";
#endif
    }
};
```

### 4. 状态持久化
```cpp
// 使用QSettings保存用户状态
void AuthService::saveUserInfo(const UserInfo &userInfo) {
    QSettings settings;
    settings.setValue("user_id", userInfo.userId);
    settings.setValue("username", userInfo.username);
    settings.setValue("email", userInfo.email);
}

UserInfo AuthService::loadUserInfo() {
    QSettings settings;
    UserInfo info;
    info.userId = settings.value("user_id").toInt();
    info.username = settings.value("username").toString();
    info.email = settings.value("email").toString();
    return info;
}
```

## 调试技巧

### 1. 网络请求日志
```cpp
void ApiClient::logRequest(const QNetworkRequest &request, const QByteArray &data) {
    qDebug() << "API Request:" << request.url().toString();
    qDebug() << "Headers:" << request.rawHeaderList();
    qDebug() << "Data:" << data;
}
```

### 2. JSON调试
```cpp
void ApiClient::logJsonResponse(const QJsonObject &json) {
    QJsonDocument doc(json);
    qDebug() << "JSON Response:" << doc.toJson(QJsonDocument::Indented);
}
```

### 3. 错误追踪
```cpp
#define LOG_ERROR(msg) qDebug() << __FILE__ << __LINE__ << __FUNCTION__ << msg
```

## 部署注意事项

### 1. 依赖库
确保目标系统安装了Qt运行时库：
- Qt6Core
- Qt6Widgets  
- Qt6Network

### 2. SSL支持
```cpp
// 检查SSL支持
if (!QSslSocket::supportsSsl()) {
    qWarning() << "SSL not supported";
}
```

### 3. 跨平台编译
```cmake
# Windows
if(WIN32)
    set_target_properties(GameClient PROPERTIES WIN32_EXECUTABLE TRUE)
endif()

# macOS
if(APPLE)
    set_target_properties(GameClient PROPERTIES MACOSX_BUNDLE TRUE)
endif()
```

---

**相关文档**:
- [完整API文档](./API_DOCUMENTATION.md)
- [前端集成指南](./FRONTEND_INTEGRATION_GUIDE.md)
- [快速参考](./API_QUICK_REFERENCE.md)

**版本**: 1.0.0  
**更新日期**: 2025-07-25
