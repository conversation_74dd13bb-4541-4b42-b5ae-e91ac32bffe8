sequenceDiagram
    participant CF as 配置文件
    participant CM as ConfigManager
    participant NS as NetworkServer
    participant EL as EventLoop
    participant EP as Epoll
    participant SK as Socket
    participant CH as Channel
    participant TP as ThreadPool
    participant Client as 客户端

    %% 系统初始化阶段
    Note over CF,Client: 系统初始化阶段
    
    CF->>CM: 加载配置文件
    CM->>CM: 解析配置
    NS->>CM: 获取网络配置
    CM->>NS: 返回NetworkConfig
    
    NS->>TP: 创建线程池
    TP->>TP: 初始化工作线程
    
    NS->>EL: 创建EventLoop(config, threadpool)
    EL->>EP: 创建Epoll(参数注入)
    EL->>SK: 创建Socket
    EL->>CH: 创建wakeupChannel
    
    EL->>EL: 启用配置热更新
    EL->>CM: 注册配置监听器
    
    SK->>SK: 应用Socket配置
    SK->>SK: 绑定地址和端口
    SK->>SK: 开始监听
    
    EL->>EP: 注册监听Socket的Channel
    EP->>EP: 添加到epoll
    
    %% 正常运行阶段
    Note over CF,Client: 正常运行阶段
    
    EL->>EL: 启动事件循环
    
    loop 事件循环
        EL->>EP: epoll_wait(timeout)
        
        alt 有新连接
            EP->>EL: 返回监听Socket事件
            EL->>SK: accept()新连接
            SK->>EL: 返回客户端Socket
            EL->>CH: 创建客户端Channel
            EL->>EP: 注册客户端Channel
            
        else 有数据可读
            EP->>EL: 返回客户端Socket事件
            EL->>CH: 触发读回调
            CH->>CH: 读取数据
            
            alt 启用线程池
                CH->>TP: 提交处理任务
                TP->>TP: 异步处理业务逻辑
                TP->>EL: 处理完成回调
            else 同步处理
                CH->>CH: 直接处理业务逻辑
            end
            
        else 超时
            EP->>EL: 返回超时
            EL->>EL: 处理定时任务
        end
        
        EL->>EL: 处理待执行回调
    end
    
    %% 配置热更新阶段
    Note over CF,Client: 配置热更新阶段
    
    CF->>CM: 配置文件变更
    CM->>CM: 重新加载配置
    CM->>EL: 触发配置变更回调
    
    EL->>EL: 在IO线程中处理
    EL->>EL: 分析配置变更
    EL->>EL: 验证配置安全性
    
    alt Socket配置变更
        EL->>SK: 应用新Socket配置
        SK->>SK: 更新TCP选项
        SK->>SK: 更新Keep-Alive参数
        
    else 线程池配置变更
        EL->>TP: 通知线程池变更
        TP->>TP: 调整线程池大小
        
    else Epoll配置变更
        alt EventLoop运行中
            EL->>EL: 记录待更新配置
        else EventLoop停止
            EL->>EP: 保存Channel状态
            EL->>EP: 重建Epoll实例
            EL->>EP: 恢复Channel状态
        end
    end
    
    EL->>EL: 记录配置变更日志
    EL->>NS: 通知配置更新完成
    
    %% 客户端连接处理
    Note over CF,Client: 客户端连接处理
    
    Client->>SK: 发起连接
    SK->>EP: 触发连接事件
    EP->>EL: 通知新连接
    EL->>CH: 创建连接Channel
    CH->>CH: 设置读写回调
    EL->>EP: 注册Channel事件
    
    Client->>CH: 发送数据
    CH->>CH: 读取数据
    CH->>TP: 异步处理请求
    TP->>CH: 返回处理结果
    CH->>Client: 发送响应
    
    %% 系统关闭阶段
    Note over CF,Client: 系统关闭阶段
    
    NS->>EL: 请求停止
    EL->>EL: 设置quit标志
    EL->>EL: 完成当前事件循环
    EL->>EP: 清理所有Channel
    EL->>SK: 关闭监听Socket
    EL->>TP: 停止线程池
    TP->>TP: 等待任务完成
    EL->>CM: 移除配置监听器
    
    Note over CF,Client: 系统完全关闭
