# CMakeLists.txt 测试扩展指南

## 概述

本文档详细说明如何在 `tests/CMakeLists.txt` 中添加新的测试文件和配置。当前配置已简化为只包含 `network_module_comprehensive_test.cpp`，但可以轻松扩展。

## 当前结构

```
tests/
├── CMakeLists.txt                      # 主测试配置文件
├── network_module_comprehensive_test.cpp # 主要测试文件
└── CMAKE_EXTENSION_GUIDE.md           # 本文档
```

## 添加新测试文件

### 1. 添加简单的单文件测试

如果你想添加一个新的测试文件，例如 `my_new_test.cpp`：

```cmake
# ==================== 新测试目标示例 ====================
# 添加新的测试文件
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/my_new_test.cpp")
    add_executable(my_new_test my_new_test.cpp)
    
    # 设置包含目录
    target_include_directories(my_new_test PRIVATE
        ${CMAKE_SOURCE_DIR}/include
    )
    
    # 链接必要的库
    target_link_libraries(my_new_test 
        ${NETWORK_LIB}
        ${LOGGER_LIB}
    )
    
    # 平台特定链接（如果需要）
    if(PLATFORM_WINDOWS)
        target_link_libraries(my_new_test ws2_32 wsock32)
    else()
        target_link_libraries(my_new_test pthread)
    endif()
    
    # 添加到CTest
    add_test(NAME MyNewTest COMMAND my_new_test)
    set_tests_properties(MyNewTest PROPERTIES
        TIMEOUT 60
        PASS_REGULAR_EXPRESSION "测试通过"  # 可选：成功的正则表达式
    )
    
    message(STATUS "Added MyNewTest")
else()
    message(WARNING "my_new_test.cpp not found")
endif()
```

### 2. 添加平台特定的测试

如果测试只在特定平台上运行：

```cmake
# ==================== Linux特定测试 ====================
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/linux_specific_test.cpp")
    if(PLATFORM_LINUX OR PLATFORM_MACOS)
        add_executable(linux_specific_test linux_specific_test.cpp)
        
        target_include_directories(linux_specific_test PRIVATE
            ${CMAKE_SOURCE_DIR}/include
        )
        
        target_link_libraries(linux_specific_test 
            ${NETWORK_LIB}
            ${LOGGER_LIB}
            pthread
        )
        
        add_test(NAME LinuxSpecificTest COMMAND linux_specific_test)
        set_tests_properties(LinuxSpecificTest PROPERTIES TIMEOUT 30)
        
        message(STATUS "Added LinuxSpecificTest")
    else()
        message(STATUS "Skipping LinuxSpecificTest on Windows")
    endif()
endif()

# ==================== Windows特定测试 ====================
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/windows_specific_test.cpp")
    if(PLATFORM_WINDOWS)
        add_executable(windows_specific_test windows_specific_test.cpp)
        
        target_include_directories(windows_specific_test PRIVATE
            ${CMAKE_SOURCE_DIR}/include
        )
        
        target_link_libraries(windows_specific_test 
            ${NETWORK_LIB}
            ${LOGGER_LIB}
            ws2_32
            wsock32
        )
        
        add_test(NAME WindowsSpecificTest COMMAND windows_specific_test)
        set_tests_properties(WindowsSpecificTest PROPERTIES TIMEOUT 30)
        
        message(STATUS "Added WindowsSpecificTest")
    else()
        message(STATUS "Skipping WindowsSpecificTest on Unix")
    endif()
endif()
```

### 3. 添加需要额外依赖的测试

如果测试需要额外的库或源文件：

```cmake
# ==================== 需要额外依赖的测试 ====================
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/advanced_test.cpp")
    # 检查额外的源文件
    set(EXTRA_SOURCES
        "${CMAKE_SOURCE_DIR}/src/common/utils/helper.cpp"
        "${CMAKE_SOURCE_DIR}/src/common/config/config.cpp"
    )
    
    # 验证额外源文件存在
    set(EXTRA_SOURCES_FOUND TRUE)
    foreach(SOURCE ${EXTRA_SOURCES})
        if(NOT EXISTS ${SOURCE})
            message(WARNING "Missing extra source: ${SOURCE}")
            set(EXTRA_SOURCES_FOUND FALSE)
        endif()
    endforeach()
    
    if(EXTRA_SOURCES_FOUND)
        # 创建额外的库
        add_library(extra_lib STATIC ${EXTRA_SOURCES})
        target_include_directories(extra_lib PUBLIC ${CMAKE_SOURCE_DIR}/include)
        
        # 创建测试
        add_executable(advanced_test advanced_test.cpp)
        
        target_include_directories(advanced_test PRIVATE
            ${CMAKE_SOURCE_DIR}/include
        )
        
        target_link_libraries(advanced_test 
            ${NETWORK_LIB}
            ${LOGGER_LIB}
            extra_lib
            pthread
        )
        
        add_test(NAME AdvancedTest COMMAND advanced_test)
        set_tests_properties(AdvancedTest PROPERTIES TIMEOUT 120)
        
        message(STATUS "Added AdvancedTest with extra dependencies")
    else()
        message(WARNING "Skipping AdvancedTest due to missing dependencies")
    endif()
endif()
```

## 自定义测试目标

### 添加新的自定义目标

在 `# ==================== 自定义测试目标 ====================` 部分添加：

```cmake
# 运行特定类型的测试
add_custom_target(run_unit_tests
        COMMAND ${CMAKE_CTEST_COMMAND} --verbose -R "Unit"
        DEPENDS
        $<$<TARGET_EXISTS:my_unit_test>:my_unit_test>
        $<$<TARGET_EXISTS:another_unit_test>:another_unit_test>
        COMMENT "Running unit tests only"
)

# 运行性能测试
add_custom_target(run_performance_tests
        COMMAND ${CMAKE_CTEST_COMMAND} --verbose -R "Performance"
        DEPENDS
        $<$<TARGET_EXISTS:performance_test>:performance_test>
        COMMENT "Running performance tests"
)

# 运行带覆盖率的测试
add_custom_target(run_coverage_tests
        COMMAND ${CMAKE_CTEST_COMMAND} --verbose
        COMMAND lcov --capture --directory ../tests --output-file coverage.info
        COMMAND genhtml coverage.info --output-directory coverage_report
        DEPENDS
        $<$<TARGET_EXISTS:network_comprehensive_test>:network_comprehensive_test>
        COMMENT "Running tests with coverage analysis"
)
```

## 测试配置选项

### 添加编译时选项

在文件开头添加选项：

```cmake
# ==================== 测试选项 ====================
option(BUILD_UNIT_TESTS "Build unit tests" ON)
option(BUILD_INTEGRATION_TESTS "Build integration tests" ON)
option(BUILD_PERFORMANCE_TESTS "Build performance tests" OFF)
option(ENABLE_TEST_COVERAGE "Enable test coverage" OFF)

# 根据选项配置编译标志
if(ENABLE_TEST_COVERAGE AND NOT PLATFORM_WINDOWS)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage -fprofile-arcs -ftest-coverage")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} --coverage")
endif()
```

### 使用选项控制测试编译

```cmake
# 条件编译测试
if(BUILD_UNIT_TESTS)
    # 添加单元测试
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/unit_test.cpp")
        add_executable(unit_test unit_test.cpp)
        # ... 其他配置
    endif()
endif()

if(BUILD_INTEGRATION_TESTS)
    # 添加集成测试
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/integration_test.cpp")
        add_executable(integration_test integration_test.cpp)
        # ... 其他配置
    endif()
endif()
```

## 常用模板

### 基本测试模板

```cmake
# ==================== [测试名称] ====================
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/[测试文件名].cpp")
    # 可选：平台检查
    if(PLATFORM_LINUX OR PLATFORM_MACOS)  # 或者 if(PLATFORM_WINDOWS)
        add_executable([测试目标名] [测试文件名].cpp)
        
        target_include_directories([测试目标名] PRIVATE
            ${CMAKE_SOURCE_DIR}/include
        )
        
        target_link_libraries([测试目标名] 
            ${NETWORK_LIB}
            ${LOGGER_LIB}
            # 平台特定库
            $<$<BOOL:${PLATFORM_WINDOWS}>:ws2_32>
            $<$<BOOL:${PLATFORM_WINDOWS}>:wsock32>
            $<$<NOT:$<BOOL:${PLATFORM_WINDOWS}>>:pthread>
        )
        
        add_test(NAME [测试名称] COMMAND [测试目标名])
        set_tests_properties([测试名称] PROPERTIES
            TIMEOUT [超时秒数]
            PASS_REGULAR_EXPRESSION "[成功标识]"  # 可选
        )
        
        message(STATUS "Added [测试名称]")
    else()
        message(STATUS "Skipping [测试名称] on this platform")
    endif()
else()
    message(WARNING "[测试文件名].cpp not found")
endif()
```

## 使用说明

1. **替换当前CMakeLists.txt**：
   ```bash
   cd tests/
   cp CMakeLists_simplified.txt CMakeLists.txt
   ```

2. **添加新测试**：
   - 将测试文件放在 `tests/` 目录下
   - 按照上述模板在CMakeLists.txt中添加配置
   - 重新生成构建文件：`cmake ..`

3. **运行测试**：
   ```bash
   make test                # 运行所有测试
   make run_all_tests      # 详细输出
   make test_help          # 查看帮助
   ```

## 注意事项

1. **文件存在检查**：始终使用 `if(EXISTS ...)` 检查文件是否存在
2. **平台兼容性**：考虑不同平台的差异，使用条件编译
3. **依赖管理**：确保所有依赖库都正确链接
4. **超时设置**：为长时间运行的测试设置合适的超时
5. **消息输出**：使用 `message(STATUS ...)` 提供清晰的构建信息

这个指南应该能帮助你轻松扩展测试配置！
