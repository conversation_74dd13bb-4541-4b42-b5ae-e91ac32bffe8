# 游戏微服务系统配置文件使用指南

## 概述

本项目提供了三种格式的配置文件，完美适配 `game_microservices` 项目中的所有模块：

- **config.yml** - YAML格式配置文件（推荐用于开发环境）
- **config.json** - JSON格式配置文件（推荐用于API配置）
- **config.env** - 环境变量配置文件（推荐用于生产环境）

## 支持的模块

配置文件覆盖了项目中的所有核心模块：

### 1. 数据库模块 (`common/database/`)
- **MySQL连接池** (`mysql_pool.h`) - 数据库连接管理
- **Redis连接池** (`redis_pool.h`) - 缓存连接管理

### 2. 网络模块 (`common/network/`)
- **网络地址** (`inet_address.h`) - IP地址和端口管理
- **Socket通信** (`socket.h`) - TCP/UDP通信
- **事件通道** (`channel.h`) - 事件处理
- **Epoll机制** (`epoll.h`) - Linux高性能IO
- **事件循环** (`event_loop.h`) - 事件驱动架构

### 3. 消息队列模块 (`common/kafka/`)
- **Kafka生产者** (`kafka_producer.h`) - 消息发送
- **Kafka消费者** (`kafka_consumer.h`) - 消息接收

### 4. 日志模块 (`common/logger/`)
- **日志管理器** (`logger.h`) - 统一日志处理

### 5. 任务调度模块 (`common/scheduler/`)
- **任务调度器** (`task_scheduler.h`) - 定时任务管理

### 6. 线程池模块 (`common/thread_pool/`)
- **线程池** (`thread_pool.h`) - 线程资源管理

### 7. 配置管理模块 (`common/config/`)
- **配置管理器** (`config_manager.h`) - 统一配置管理

## 配置文件集成方法

### 方法1：使用YAML配置文件

```cpp
#include "common/config/config_manager.h"

int main() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 加载YAML配置文件
    if (!config.loadFromYaml("config.yml")) {
        std::cerr << "Failed to load config.yml" << std::endl;
        return -1;
    }
    
    // 使用配置
    std::string mysql_host = config.get<std::string>("database.mysql.host");
    int mysql_port = config.get<int>("database.mysql.port");
    
    return 0;
}
```

### 方法2：使用JSON配置文件

```cpp
#include "common/config/config_manager.h"

int main() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 加载JSON配置文件
    if (!config.loadFromJson("config.json")) {
        std::cerr << "Failed to load config.json" << std::endl;
        return -1;
    }
    
    // 使用配置
    std::string kafka_brokers = config.get<std::string>("kafka.producer.brokers");
    int thread_pool_size = config.get<int>("thread_pool.core_size");
    
    return 0;
}
```

### 方法3：使用环境变量配置

```cpp
#include "common/config/config_manager.h"

int main() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 加载环境变量配置
    config.loadFromEnv();
    
    // 使用配置
    std::string mysql_host = config.get<std::string>("mysql_host");
    int mysql_port = config.get<int>("mysql_port");
    
    return 0;
}
```

### 方法4：分层配置加载（推荐）

```cpp
#include "common/config/config_manager.h"

int main() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 分层加载配置（后加载的覆盖先加载的）
    config.loadFromYaml("config.yml");      // 基础配置
    config.loadFromEnv();                   // 环境变量覆盖
    
    // 验证配置
    if (!config.validate()) {
        auto errors = config.getValidationErrors();
        for (const auto& error : errors) {
            std::cerr << "Config error: " << error << std::endl;
        }
        return -1;
    }
    
    return 0;
}
```

## 各模块配置使用示例

### 1. 数据库模块配置

```cpp
#include "common/database/mysql_pool.h"
#include "common/database/redis_pool.h"
#include "common/config/config_manager.h"

void initializeDatabases() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 获取MySQL配置
    auto mysql_config = DatabaseConfig::fromConfigManager();
    
    // 初始化MySQL连接池
    auto mysql_pool = std::make_shared<MySQLPool>(
        mysql_config.mysql_host,
        mysql_config.mysql_port,
        mysql_config.mysql_user,
        mysql_config.mysql_password,
        mysql_config.mysql_database,
        mysql_config.mysql_pool_size
    );
    
    // 获取Redis配置
    auto redis_config = RedisConfig::fromConfigManager();
    
    // 初始化Redis连接池
    auto redis_pool = std::make_shared<RedisPool>(
        redis_config.redis_host,
        redis_config.redis_port,
        redis_config.redis_password,
        redis_config.redis_database,
        redis_config.redis_pool_size
    );
}
```

### 2. 网络模块配置

```cpp
#include "common/network/event_loop.h"
#include "common/network/socket.h"
#include "common/config/config_manager.h"

void initializeNetwork() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 获取网络配置
    auto network_config = NetworkConfig::fromConfigManager();
    
    // 创建事件循环
    auto event_loop = std::make_shared<EventLoop>(
        network_config.network_worker_threads
    );
    
    // 创建服务器Socket
    auto server_socket = std::make_shared<Socket>();
    server_socket.setReuseAddr(network_config.so_reuseaddr);
    server_socket.setKeepAlive(network_config.so_keepalive);
    server_socket.setTcpNoDelay(network_config.tcp_nodelay);
    
    // 绑定地址
    InetAddress server_addr(network_config.network_bind_address, 
                           network_config.network_listen_port);
    server_socket.bind(server_addr);
    server_socket.listen(network_config.network_backlog);
}
```

### 3. Kafka模块配置

```cpp
#include "common/kafka/kafka_producer.h"
#include "common/kafka/kafka_consumer.h"
#include "common/config/config_manager.h"

void initializeKafka() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 获取Kafka生产者配置
    auto producer_config = KafkaProducerConfig::fromConfigManager();
    
    // 初始化Kafka生产者
    auto producer = std::make_shared<KafkaProducer>(
        producer_config.brokers,
        producer_config.client_id
    );
    producer->setAcks(producer_config.acks);
    producer->setCompressionType(producer_config.compression_type);
    producer->setEnableIdempotence(producer_config.enable_idempotence);
    
    // 获取Kafka消费者配置
    auto consumer_config = KafkaConsumerConfig::fromConfigManager();
    
    // 初始化Kafka消费者
    auto consumer = std::make_shared<KafkaConsumer>(
        consumer_config.brokers,
        consumer_config.group_id,
        consumer_config.client_id
    );
    consumer->setAutoOffsetReset(consumer_config.auto_offset_reset);
    consumer->setIsolationLevel(consumer_config.isolation_level);
}
```

### 4. 日志模块配置

```cpp
#include "common/logger/logger.h"
#include "common/config/config_manager.h"

void initializeLogging() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 获取日志配置
    auto log_config = LogConfig::fromConfigManager();
    
    // 初始化日志系统
    Logger::getInstance().initialize(
        log_config.level,
        log_config.console_enabled,
        log_config.file_enabled,
        log_config.file_path
    );
    
    // 配置异步日志
    if (log_config.async_enabled) {
        Logger::getInstance().enableAsync(
            log_config.async_queue_size,
            log_config.async_thread_count
        );
    }
}
```

### 5. 任务调度器配置

```cpp
#include "common/scheduler/task_scheduler.h"
#include "common/config/config_manager.h"

void initializeScheduler() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 获取调度器配置
    auto scheduler_config = SchedulerConfig::fromConfigManager();
    
    // 初始化任务调度器
    auto scheduler = std::make_shared<TaskScheduler>(
        scheduler_config.scheduler_worker_threads,
        scheduler_config.scheduler_max_pending_tasks
    );
    
    // 配置调度器选项
    scheduler->setTickInterval(
        std::chrono::milliseconds(scheduler_config.scheduler_tick_interval_ms)
    );
    scheduler->enableMetrics(scheduler_config.scheduler_enable_metrics);
    scheduler->enableTaskTimeout(scheduler_config.scheduler_enable_task_timeout);
}
```

### 6. 线程池配置

```cpp
#include "common/thread_pool/thread_pool.h"
#include "common/config/config_manager.h"

void initializeThreadPool() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 获取线程池配置
    auto thread_pool_config = ThreadPoolConfig::fromConfigManager();
    
    // 初始化线程池
    auto thread_pool = std::make_shared<ThreadPool>(
        thread_pool_config.thread_pool_core_size,
        thread_pool_config.thread_pool_max_size,
        std::chrono::milliseconds(thread_pool_config.thread_pool_keep_alive_ms),
        thread_pool_config.thread_pool_queue_capacity
    );
    
    // 配置线程池选项
    thread_pool->allowCoreThreadTimeout(thread_pool_config.thread_pool_allow_core_timeout);
    thread_pool->prestartCoreThreads(thread_pool_config.thread_pool_prestart_core_threads);
    thread_pool->setNamePrefix(thread_pool_config.thread_pool_name_prefix);
}
```

## 部署环境配置

### Docker部署

```dockerfile
# Dockerfile
FROM ubuntu:20.04

# 复制配置文件
COPY ../config/config.yml /app/config.yml
COPY ../config/config.json /app/config.json

# 设置环境变量
ENV MYSQL_HOST=mysql-container
ENV REDIS_HOST=redis-container
ENV KAFKA_PRODUCER_BROKERS=kafka-container:9092

WORKDIR /app
CMD ["./game_microservices"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  game-service:
    build: .
    environment:
      - SYSTEM_ENVIRONMENT=production
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - KAFKA_PRODUCER_BROKERS=kafka:9092
    env_file:
      - config.env
    depends_on:
      - mysql
      - redis
      - kafka
```

### Kubernetes部署

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-service-config
data:
  config.yml: |
    # YAML配置内容
  config.json: |
    # JSON配置内容

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: game-service-secrets
type: Opaque
stringData:
  MYSQL_PASSWORD: "secure_password"
  REDIS_PASSWORD: "redis_password"
  KAFKA_PASSWORD: "kafka_password"

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: game-service
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: game-service
        image: game-service:latest
        envFrom:
        - configMapRef:
            name: game-service-config
        - secretRef:
            name: game-service-secrets
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
      volumes:
      - name: config-volume
        configMap:
          name: game-service-config
```

## 配置验证和监控

```cpp
#include "common/config/config_manager.h"

void validateAndMonitorConfig() {
    using namespace common::config;
    
    auto& config = ConfigManager::getInstance();
    
    // 验证配置
    if (!config.validate()) {
        auto errors = config.getValidationErrors();
        for (const auto& error : errors) {
            LOG_ERROR("Configuration validation failed: " + error);
        }
        throw std::runtime_error("Invalid configuration");
    }
    
    // 启用热重载
    config.enableHotReload("config.yml", std::chrono::seconds(30));
    
    // 添加配置变更监听器
    config.addChangeListener("database.mysql.host", 
        [](const std::string& key, const std::string& old_val, const std::string& new_val) {
            LOG_INFO("Database host changed from " + old_val + " to " + new_val);
            // 重新初始化数据库连接
        });
}
```

通过这些配置文件和使用方法，您可以轻松地将Config模块集成到游戏微服务项目中，实现统一的配置管理。
