# CircuitBreaker 业务流程详细分析

## 目录
1. [熔断器初始化流程](#熔断器初始化流程)
2. [熔断器状态机管理流程](#熔断器状态机管理流程)
3. [请求允许检查流程](#请求允许检查流程)
4. [失败统计和熔断触发流程](#失败统计和熔断触发流程)
5. [半开状态测试流程](#半开状态测试流程)
6. [熔断恢复流程](#熔断恢复流程)
7. [监控和告警流程](#监控和告警流程)

## 熔断器初始化流程

### 1. 熔断器创建和配置流程

**业务场景**：创建熔断器实例并加载配置

**执行步骤**：

#### 步骤1：配置加载
```cpp
// 调用类：ApiGateway
// 使用函数：CircuitBreaker::Config::fromConfigManager()
auto config = CircuitBreaker::Config::fromConfigManager();
```

**内部执行序列**：
1. `Config::fromConfigManager()` - 从配置管理器加载
2. 加载熔断阈值配置：
   - `config.failure_threshold` - 失败阈值（触发熔断的失败次数）
   - `config.success_threshold` - 成功阈值（恢复所需的成功次数）
   - `config.timeout_ms` - 请求超时时间
3. 加载时间配置：
   - `config.recovery_timeout_ms` - 恢复超时时间（熔断器开启后多久尝试恢复）
   - `config.window_size_ms` - 统计窗口大小
4. 加载功能配置：
   - `config.enable_metrics` - 启用指标收集
   - `config.enable_alerts` - 启用告警
5. 验证配置有效性：`config.validate()`

#### 步骤2：熔断器实例化
```cpp
// 调用类：ApiGateway
// 使用函数：CircuitBreaker::CircuitBreaker()
auto circuit_breaker = std::make_unique<CircuitBreaker>(config);
```

**内部执行序列**：
1. `CircuitBreaker::CircuitBreaker(config)` - 构造函数
2. `config_ = config` - 保存配置
3. 初始化服务状态映射：`service_states_.clear()`
4. 初始化统计信息：`service_stats_.clear()`
5. 设置默认状态：所有服务初始状态为CLOSED
6. 启动监控任务：`startMonitoringTask()`
7. 记录初始化日志：`LOG_INFO("CircuitBreaker initialized - Failure threshold: " + std::to_string(config.failure_threshold))`

#### 步骤3：服务状态初始化
**执行序列**：
1. 为每个服务创建状态对象：`ServiceState state{}`
2. 初始化状态属性：
   - `state.current_state = State::CLOSED` - 初始状态为关闭（正常）
   - `state.failure_count = 0` - 失败计数清零
   - `state.success_count = 0` - 成功计数清零
   - `state.last_failure_time = {}` - 最后失败时间
   - `state.state_change_time = std::chrono::steady_clock::now()` - 状态变更时间
3. 注册状态变更监听器：`registerStateChangeListener()`

**结果**：熔断器初始化完成，准备监控服务状态

### 2. 服务注册流程

**业务场景**：向熔断器注册需要监控的服务

**执行步骤**：

#### 步骤1：服务注册
```cpp
// 调用类：ApiGateway或RouteManager
// 使用函数：CircuitBreaker::registerService()
circuit_breaker->registerService("user-service");
```

**内部执行序列**：
1. `CircuitBreaker::registerService(service_name)`
2. 参数验证：`if (service_name.empty()) throw std::invalid_argument("Service name cannot be empty")`
3. 检查重复注册：`if (service_states_.find(service_name) != service_states_.end())`
4. 创建服务状态：
   - 初始化状态对象：`ServiceState state{}`
   - 设置初始状态：`state.current_state = State::CLOSED`
   - 初始化统计信息：`ServiceStats stats{}`
5. 注册服务：
   - `service_states_[service_name] = state`
   - `service_stats_[service_name] = stats`
6. 记录注册日志：`LOG_INFO("Registered service for circuit breaker: " + service_name)`

**结果**：服务成功注册到熔断器监控

## 熔断器状态机管理流程

### 1. 状态机定义和转换流程

**业务场景**：管理熔断器的三种状态转换

**执行步骤**：

#### 步骤1：状态定义
**状态说明**：
- `CLOSED`：熔断器关闭，请求正常通过，统计失败次数
- `OPEN`：熔断器开启，拒绝所有请求，等待恢复超时
- `HALF_OPEN`：熔断器半开，允许少量测试请求，根据结果决定下一步

#### 步骤2：状态转换规则
**转换条件**：
1. `CLOSED → OPEN`：失败次数达到阈值
2. `OPEN → HALF_OPEN`：恢复超时时间到达
3. `HALF_OPEN → CLOSED`：测试请求成功次数达到阈值
4. `HALF_OPEN → OPEN`：测试请求失败

#### 步骤3：状态转换执行
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::changeState()
void CircuitBreaker::changeState(const std::string& service_name, State new_state) {
    // 执行状态转换
}
```

**内部执行序列**：
1. `changeState(service_name, new_state)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(states_mutex_)`
3. 获取当前状态：`State old_state = service_states_[service_name].current_state`
4. 验证状态转换：`if (!isValidTransition(old_state, new_state))`
5. 执行状态转换：
   - 更新状态：`service_states_[service_name].current_state = new_state`
   - 记录转换时间：`service_states_[service_name].state_change_time = std::chrono::steady_clock::now()`
   - 重置相关计数器：根据新状态重置失败/成功计数
6. 触发状态变更事件：`onStateChanged(service_name, old_state, new_state)`
7. 记录状态变更日志：`LOG_INFO("Circuit breaker state changed for " + service_name + ": " + stateToString(old_state) + " -> " + stateToString(new_state))`

**结果**：熔断器状态成功转换

### 2. 状态监控流程

**业务场景**：持续监控熔断器状态并触发必要的转换

**执行步骤**：

#### 步骤1：定时状态检查
**执行序列**：
1. 启动监控定时器：`startStateMonitoringTask()`
2. 设置检查间隔：`auto check_interval = std::chrono::seconds(5)`
3. 定期执行检查：`checkAllServiceStates()`

#### 步骤2：状态检查逻辑
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::checkServiceState()
void CircuitBreaker::checkServiceState(const std::string& service_name) {
    // 检查服务状态
}
```

**内部执行序列**：
1. `checkServiceState(service_name)`
2. 获取当前状态：`State current_state = service_states_[service_name].current_state`
3. 根据状态执行检查：
   - `CLOSED`：检查失败次数是否达到阈值
   - `OPEN`：检查是否到达恢复超时时间
   - `HALF_OPEN`：检查测试结果是否足够做出决定
4. 触发状态转换：`if (should_change_state) changeState(service_name, new_state)`

**结果**：熔断器状态得到及时监控和更新

## 请求允许检查流程

### 1. 请求准入检查流程

**业务场景**：检查请求是否被熔断器允许通过

**执行步骤**：

#### 步骤1：请求检查
```cpp
// 调用类：ApiGateway
// 使用函数：CircuitBreaker::allowRequest()
bool allowed = circuit_breaker->allowRequest(service_name);
```

**内部执行序列**：
1. `CircuitBreaker::allowRequest(service_name)`
2. 参数验证：`if (service_name.empty()) return false`
3. 获取读锁：`std::shared_lock<std::shared_mutex> lock(states_mutex_)`
4. 查找服务状态：`auto it = service_states_.find(service_name)`
5. 如果服务未注册：自动注册并允许请求
6. 获取当前状态：`State current_state = it->second.current_state`
7. 根据状态决定是否允许：
   - `CLOSED`：允许所有请求
   - `OPEN`：拒绝所有请求
   - `HALF_OPEN`：允许有限的测试请求

#### 步骤2：半开状态特殊处理
**执行序列**：
1. 检查半开状态：`if (current_state == State::HALF_OPEN)`
2. 检查并发测试请求数：`if (concurrent_test_requests >= max_test_requests)`
3. 如果超过限制：拒绝请求
4. 如果允许：增加测试请求计数：`concurrent_test_requests++`

#### 步骤3：请求统计更新
**执行序列**：
1. 更新请求统计：`service_stats_[service_name].total_requests++`
2. 如果允许：`service_stats_[service_name].allowed_requests++`
3. 如果拒绝：`service_stats_[service_name].rejected_requests++`
4. 记录请求时间：`service_stats_[service_name].last_request_time = std::chrono::steady_clock::now()`

**结果**：请求的准入检查完成，返回是否允许

### 2. 请求结果记录流程

**业务场景**：记录请求的执行结果用于熔断决策

**执行步骤**：

#### 步骤1：成功结果记录
```cpp
// 调用类：ApiGateway
// 使用函数：CircuitBreaker::recordSuccess()
circuit_breaker->recordSuccess(service_name);
```

**内部执行序列**：
1. `CircuitBreaker::recordSuccess(service_name)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(states_mutex_)`
3. 更新成功统计：
   - `service_stats_[service_name].success_count++`
   - `service_states_[service_name].success_count++`
   - `service_stats_[service_name].last_success_time = std::chrono::steady_clock::now()`
4. 重置失败计数：`service_states_[service_name].failure_count = 0`
5. 检查状态转换：
   - 如果是半开状态：检查成功次数是否达到恢复阈值
   - 如果达到：转换为关闭状态

#### 步骤2：失败结果记录
```cpp
// 调用类：ApiGateway
// 使用函数：CircuitBreaker::recordFailure()
circuit_breaker->recordFailure(service_name);
```

**内部执行序列**：
1. `CircuitBreaker::recordFailure(service_name)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(states_mutex_)`
3. 更新失败统计：
   - `service_stats_[service_name].failure_count++`
   - `service_states_[service_name].failure_count++`
   - `service_states_[service_name].last_failure_time = std::chrono::steady_clock::now()`
4. 检查熔断条件：
   - 如果失败次数达到阈值：触发熔断
   - 如果是半开状态失败：立即转换为开启状态

**结果**：请求结果被正确记录并触发相应的状态转换

## 失败统计和熔断触发流程

### 1. 失败统计收集流程

**业务场景**：收集和分析服务失败统计信息

**执行步骤**：

#### 步骤1：失败类型分类
**执行序列**：
1. 超时失败：`recordTimeout(service_name)`
2. 连接失败：`recordConnectionFailure(service_name)`
3. 服务错误：`recordServiceError(service_name, error_code)`
4. 业务失败：`recordBusinessFailure(service_name, business_error)`

#### 步骤2：失败统计聚合
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::updateFailureStats()
void CircuitBreaker::updateFailureStats(const std::string& service_name, FailureType type) {
    // 更新失败统计
}
```

**内部执行序列**：
1. `updateFailureStats(service_name, type)`
2. 更新失败类型统计：`service_stats_[service_name].failure_types[type]++`
3. 更新时间窗口统计：`updateWindowStats(service_name, type)`
4. 计算失败率：`failure_rate = failures_in_window / total_requests_in_window`
5. 检查失败率阈值：`if (failure_rate > config_.failure_rate_threshold)`

#### 步骤3：失败趋势分析
**执行序列**：
1. 计算失败趋势：`calculateFailureTrend(service_name)`
2. 检测失败激增：`if (recent_failure_rate > historical_average * spike_threshold)`
3. 预测性熔断：在失败率快速上升时提前触发熔断

**结果**：失败统计信息得到准确收集和分析

### 2. 熔断触发流程

**业务场景**：根据失败统计触发熔断保护

**执行步骤**：

#### 步骤1：熔断条件检查
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::checkTripCondition()
bool should_trip = checkTripCondition(service_name);
```

**内部执行序列**：
1. `checkTripCondition(service_name)`
2. 检查失败次数：`if (failure_count >= config_.failure_threshold)`
3. 检查失败率：`if (failure_rate >= config_.failure_rate_threshold)`
4. 检查时间窗口：`if (failures_in_window >= window_threshold)`
5. 综合判断：`return any_condition_met`

#### 步骤2：熔断执行
**执行序列**：
1. 触发熔断：`if (should_trip) tripCircuitBreaker(service_name)`
2. 状态转换：`changeState(service_name, State::OPEN)`
3. 设置恢复定时器：`setRecoveryTimer(service_name, config_.recovery_timeout_ms)`
4. 发送熔断告警：`sendCircuitBreakerAlert(service_name, "TRIPPED")`
5. 记录熔断事件：`LOG_WARN("Circuit breaker tripped for service: " + service_name)`

#### 步骤3：熔断后处理
**执行序列**：
1. 拒绝后续请求：所有新请求直接返回失败
2. 清理资源：释放与该服务相关的连接和资源
3. 通知依赖服务：告知上游服务该服务不可用
4. 启动降级策略：如果配置了降级逻辑

**结果**：熔断保护被正确触发，保护系统免受故障服务影响

## 半开状态测试流程

### 1. 半开状态进入流程

**业务场景**：熔断器从开启状态转换为半开状态进行测试

**执行步骤**：

#### 步骤1：恢复时间检查
**执行序列**：
1. 定时检查恢复条件：`checkRecoveryCondition(service_name)`
2. 计算熔断持续时间：`auto duration = now - trip_time`
3. 检查是否达到恢复超时：`if (duration >= config_.recovery_timeout_ms)`
4. 触发半开转换：`changeState(service_name, State::HALF_OPEN)`

#### 步骤2：半开状态初始化
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::enterHalfOpenState()
void CircuitBreaker::enterHalfOpenState(const std::string& service_name) {
    // 进入半开状态
}
```

**内部执行序列**：
1. `enterHalfOpenState(service_name)`
2. 重置测试计数器：
   - `test_request_count = 0`
   - `test_success_count = 0`
   - `test_failure_count = 0`
3. 设置测试参数：
   - `max_test_requests = config_.success_threshold`
   - `test_timeout = config_.test_timeout_ms`
4. 启动测试监控：`startTestMonitoring(service_name)`
5. 记录半开状态：`LOG_INFO("Circuit breaker entered half-open state for: " + service_name)`

**结果**：熔断器进入半开状态，准备进行恢复测试

### 2. 测试请求处理流程

**业务场景**：在半开状态下处理测试请求

**执行步骤**：

#### 步骤1：测试请求允许
**执行序列**：
1. 检查测试请求配额：`if (test_request_count < max_test_requests)`
2. 允许测试请求：`test_request_count++`
3. 设置测试标记：`request.setTestFlag(true)`
4. 启动测试超时：`startTestTimeout(request_id)`

#### 步骤2：测试结果处理
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::handleTestResult()
void CircuitBreaker::handleTestResult(const std::string& service_name, bool success) {
    // 处理测试结果
}
```

**内部执行序列**：
1. `handleTestResult(service_name, success)`
2. 更新测试统计：
   - 如果成功：`test_success_count++`
   - 如果失败：`test_failure_count++`
3. 检查测试完成条件：
   - 成功次数达到阈值：`if (test_success_count >= config_.success_threshold)`
   - 任何失败：`if (test_failure_count > 0)`
4. 决定下一步状态：
   - 测试成功：转换为关闭状态
   - 测试失败：转换为开启状态

#### 步骤3：测试超时处理
**执行序列**：
1. 监控测试请求超时：`onTestTimeout(request_id)`
2. 将超时视为失败：`handleTestResult(service_name, false)`
3. 立即转换为开启状态：`changeState(service_name, State::OPEN)`

**结果**：测试请求得到正确处理，熔断器状态根据测试结果转换

## 熔断恢复流程

### 1. 恢复条件检查流程

**业务场景**：检查服务是否满足恢复条件

**执行步骤**：

#### 步骤1：恢复条件评估
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::evaluateRecoveryCondition()
bool can_recover = evaluateRecoveryCondition(service_name);
```

**内部执行序列**：
1. `evaluateRecoveryCondition(service_name)`
2. 检查测试成功率：`success_rate = test_success_count / test_request_count`
3. 检查成功次数：`if (test_success_count >= config_.success_threshold)`
4. 检查连续成功：`if (consecutive_successes >= required_consecutive)`
5. 检查服务健康指标：`if (service_health_score > recovery_threshold)`

#### 步骤2：渐进式恢复
**执行序列**：
1. 启动渐进恢复：`startGradualRecovery(service_name)`
2. 设置恢复阶段：
   - 阶段1：允许10%流量
   - 阶段2：允许50%流量
   - 阶段3：允许100%流量
3. 监控每个阶段的表现：`monitorRecoveryStage(service_name, stage)`

**结果**：恢复条件得到正确评估

### 2. 完全恢复流程

**业务场景**：将熔断器完全恢复到正常状态

**执行步骤**：

#### 步骤1：恢复执行
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::recoverCircuitBreaker()
void CircuitBreaker::recoverCircuitBreaker(const std::string& service_name) {
    // 恢复熔断器
}
```

**内部执行序列**：
1. `recoverCircuitBreaker(service_name)`
2. 状态转换：`changeState(service_name, State::CLOSED)`
3. 重置所有计数器：
   - `failure_count = 0`
   - `success_count = 0`
   - `test_request_count = 0`
4. 清理恢复定时器：`clearRecoveryTimer(service_name)`
5. 发送恢复通知：`sendRecoveryNotification(service_name)`

#### 步骤2：恢复后监控
**执行序列**：
1. 加强监控：`enableEnhancedMonitoring(service_name)`
2. 设置观察期：`setObservationPeriod(service_name, observation_duration)`
3. 监控关键指标：
   - 错误率
   - 响应时间
   - 吞吐量
4. 准备快速熔断：如果恢复后立即出现问题

**结果**：熔断器成功恢复到正常状态

## 监控和告警流程

### 1. 实时监控流程

**业务场景**：实时监控熔断器状态和服务健康

**执行步骤**：

#### 步骤1：指标收集
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::collectMetrics()
Metrics metrics = collectMetrics();
```

**内部执行序列**：
1. `collectMetrics()`
2. 收集状态指标：
   - 各服务的熔断器状态
   - 状态持续时间
   - 状态转换次数
3. 收集性能指标：
   - 请求成功率
   - 平均响应时间
   - 错误率分布
4. 收集业务指标：
   - 被拒绝的请求数
   - 恢复成功率
   - 熔断频率

#### 步骤2：指标暴露
**执行序列**：
1. 格式化指标：`formatPrometheusMetrics(metrics)`
2. 暴露HTTP端点：`/metrics/circuit-breaker`
3. 推送到监控系统：`pushToMonitoringSystem(metrics)`

**结果**：熔断器指标被正确收集和暴露

### 2. 告警流程

**业务场景**：在关键事件发生时发送告警

**执行步骤**：

#### 步骤1：告警条件检查
**执行序列**：
1. 熔断触发告警：`if (state_changed_to_open)`
2. 恢复失败告警：`if (recovery_attempts_failed)`
3. 频繁熔断告警：`if (trip_frequency > threshold)`
4. 服务降级告警：`if (service_degraded)`

#### 步骤2：告警发送
```cpp
// 调用类：CircuitBreaker
// 使用函数：CircuitBreaker::sendAlert()
void CircuitBreaker::sendAlert(const std::string& service_name, AlertType type, const std::string& message) {
    // 发送告警
}
```

**内部执行序列**：
1. `sendAlert(service_name, type, message)`
2. 构建告警内容：
   - 服务名称
   - 告警类型
   - 详细信息
   - 时间戳
3. 发送到告警系统：`alerting_system->send(alert)`
4. 记录告警日志：`LOG_ALERT("Circuit breaker alert: " + message)`

**结果**：关键事件的告警被及时发送

## 总结

CircuitBreaker系统的业务流程体现了高效故障保护的核心特性：

1. **状态机管理**：清晰的三状态转换逻辑
2. **智能熔断**：基于多种条件的熔断触发
3. **渐进恢复**：安全的服务恢复机制
4. **实时监控**：全面的状态和性能监控
5. **快速响应**：毫秒级的请求准入决策
6. **可配置性**：灵活的阈值和策略配置
7. **可观测性**：详细的指标和告警机制

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。

## 外部模块依赖关系

### 1. 核心依赖模块
- **Timer**: 恢复超时和状态检查
- **AlertingSystem**: 告警通知和事件发送
- **MetricsCollector**: 指标收集和暴露
- **Logger**: 统一的日志记录系统

### 2. 标准库依赖
- **std::atomic**: 线程安全的计数器
- **std::shared_mutex**: 读写锁和并发控制
- **std::chrono**: 时间测量和超时管理
- **std::condition_variable**: 状态变更通知

### 3. 集成特性
- **与ApiGateway集成**: 作为故障保护的核心组件
- **与监控系统集成**: 提供详细的熔断指标
- **与告警系统集成**: 及时的故障通知
- **与配置系统集成**: 支持动态阈值调整