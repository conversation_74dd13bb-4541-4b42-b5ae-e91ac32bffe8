# LoadBalancer 业务流程详细分析

## 目录
1. [负载均衡器初始化流程](#负载均衡器初始化流程)
2. [服务实例管理流程](#服务实例管理流程)
3. [轮询策略选择流程](#轮询策略选择流程)
4. [加权轮询策略选择流程](#加权轮询策略选择流程)
5. [最少连接策略选择流程](#最少连接策略选择流程)
6. [健康检查管理流程](#健康检查管理流程)
7. [连接数统计流程](#连接数统计流程)

## 负载均衡器初始化流程

### 1. 负载均衡器创建流程

**业务场景**：创建负载均衡器实例并设置策略

**执行步骤**：

#### 步骤1：负载均衡器实例化
```cpp
// 调用类：ApiGateway
// 使用函数：LoadBalancer::LoadBalancer()
auto load_balancer = std::make_unique<LoadBalancer>(Strategy::ROUND_ROBIN);
```

**内部执行序列**：
1. `LoadBalancer::LoadBalancer(strategy)` - 构造函数
2. `strategy_ = strategy` - 设置负载均衡策略
3. `service_instances_.clear()` - 初始化服务实例映射
4. `round_robin_index_.clear()` - 初始化轮询索引映射
5. `connection_counts_.clear()` - 初始化连接计数映射
6. 记录初始化日志：`LOG_INFO("LoadBalancer initialized with strategy: " + strategyToString(strategy))`

#### 步骤2：策略验证
**执行序列**：
1. 验证策略有效性：`validateStrategy(strategy)`
2. 根据策略初始化特定数据结构：
   - `ROUND_ROBIN`：初始化轮询索引
   - `WEIGHTED_ROUND_ROBIN`：初始化权重计算器
   - `LEAST_CONNECTIONS`：初始化连接计数器

**结果**：负载均衡器初始化完成，准备管理服务实例

### 2. 策略配置流程

**业务场景**：配置和切换负载均衡策略

**执行步骤**：

#### 步骤1：策略切换
```cpp
// 调用类：LoadBalancer
// 使用函数：LoadBalancer::setStrategy()
load_balancer->setStrategy(Strategy::WEIGHTED_ROUND_ROBIN);
```

**内部执行序列**：
1. `LoadBalancer::setStrategy(new_strategy)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(instances_mutex_)`
3. 保存旧策略：`Strategy old_strategy = strategy_`
4. 设置新策略：`strategy_ = new_strategy`
5. 重置策略相关数据：
   - 清空轮询索引：`round_robin_index_.clear()`
   - 重置连接计数：`resetConnectionCounts()`
6. 记录策略变更：`LOG_INFO("LoadBalancer strategy changed from " + strategyToString(old_strategy) + " to " + strategyToString(new_strategy))`

**结果**：负载均衡策略成功切换

## 服务实例管理流程

### 1. 服务实例添加流程

**业务场景**：向负载均衡器添加新的服务实例

**执行步骤**：

#### 步骤1：实例添加
```cpp
// 调用类：ApiGateway或ServiceDiscovery
// 使用函数：LoadBalancer::addInstance()
ServiceInstance instance{"************", 8080, 1, true, std::chrono::system_clock::now()};
load_balancer->addInstance("user-service", instance);
```

**内部执行序列**：
1. `LoadBalancer::addInstance(service_name, instance)`
2. 参数验证：
   - 检查服务名是否为空：`if (service_name.empty())`
   - 检查主机地址：`if (instance.host.empty())`
   - 检查端口范围：`if (instance.port <= 0 || instance.port > 65535)`
3. 获取写锁：`std::unique_lock<std::shared_mutex> lock(instances_mutex_)`
4. 检查实例是否已存在：
   - 生成实例ID：`std::string instance_id = instance.host + ":" + std::to_string(instance.port)`
   - 检查重复：`if (findInstance(service_name, instance_id) != nullptr)`
5. 添加实例到服务列表：`service_instances_[service_name].push_back(instance)`
6. 初始化连接计数：`connection_counts_[service_name][instance_id] = 0`
7. 记录添加日志：`LOG_INFO("Added instance " + instance_id + " to service " + service_name)`

#### 步骤2：实例验证
**执行序列**：
1. 验证实例连通性：`if (config.enable_health_check)`
2. 发送健康检查请求：`bool healthy = performHealthCheck(instance)`
3. 更新健康状态：`instance.healthy = healthy`
4. 记录健康状态：`LOG_DEBUG("Instance " + instance_id + " health status: " + (healthy ? "healthy" : "unhealthy"))`

**结果**：服务实例成功添加到负载均衡器

### 2. 服务实例移除流程

**业务场景**：从负载均衡器移除服务实例

**执行步骤**：

#### 步骤1：实例移除
```cpp
// 调用类：ApiGateway或ServiceDiscovery
// 使用函数：LoadBalancer::removeInstance()
load_balancer->removeInstance("user-service", "************:8080");
```

**内部执行序列**：
1. `LoadBalancer::removeInstance(service_name, instance_id)`
2. 参数验证：检查服务名和实例ID
3. 获取写锁：`std::unique_lock<std::shared_mutex> lock(instances_mutex_)`
4. 查找服务实例列表：`auto service_it = service_instances_.find(service_name)`
5. 查找目标实例：
   - 遍历实例列表：`for (auto it = instances.begin(); it != instances.end(); ++it)`
   - 匹配实例ID：`if (generateInstanceId(*it) == instance_id)`
6. 移除实例：`instances.erase(it)`
7. 清理连接计数：`connection_counts_[service_name].erase(instance_id)`
8. 重置轮询索引：`if (round_robin_index_[service_name] >= instances.size())`
9. 记录移除日志：`LOG_INFO("Removed instance " + instance_id + " from service " + service_name)`

#### 步骤2：清理操作
**执行序列**：
1. 检查服务是否还有实例：`if (service_instances_[service_name].empty())`
2. 清理空服务：
   - 移除服务映射：`service_instances_.erase(service_name)`
   - 清理轮询索引：`round_robin_index_.erase(service_name)`
   - 清理连接计数：`connection_counts_.erase(service_name)`

**结果**：服务实例成功从负载均衡器移除

### 3. 实例健康状态更新流程

**业务场景**：更新服务实例的健康状态

**执行步骤**：

#### 步骤1：健康状态更新
```cpp
// 调用类：HealthChecker
// 使用函数：LoadBalancer::updateInstanceHealth()
load_balancer->updateInstanceHealth("user-service", "************:8080", false);
```

**内部执行序列**：
1. `LoadBalancer::updateInstanceHealth(service_name, instance_id, healthy)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(instances_mutex_)`
3. 查找目标实例：`auto instance = findInstance(service_name, instance_id)`
4. 检查状态变化：`bool status_changed = (instance->healthy != healthy)`
5. 更新健康状态：`instance->healthy = healthy`
6. 更新检查时间：`instance->last_check = std::chrono::system_clock::now()`
7. 记录状态变化：
   - 如果状态改变：`LOG_INFO("Instance " + instance_id + " health changed to " + (healthy ? "healthy" : "unhealthy"))`
   - 如果实例恢复：触发恢复流程
   - 如果实例故障：从负载均衡中暂时移除

**结果**：实例健康状态成功更新

## 轮询策略选择流程

### 1. 轮询实例选择流程

**业务场景**：使用轮询策略选择服务实例

**执行步骤**：

#### 步骤1：轮询选择
```cpp
// 调用类：LoadBalancer
// 使用函数：LoadBalancer::selectRoundRobin()
auto instance = selectRoundRobin(service_name, healthy_instances);
```

**内部执行序列**：
1. `LoadBalancer::selectRoundRobin(service_name, instances)`
2. 边界检查：`if (instances.empty()) return std::nullopt`
3. 获取当前轮询索引：
   - 查找索引：`auto it = round_robin_index_.find(service_name)`
   - 如果不存在：`round_robin_index_[service_name] = 0`
4. 获取当前索引：`size_t current_index = round_robin_index_[service_name]`
5. 选择实例：`const auto& selected = instances[current_index]`
6. 更新索引：`round_robin_index_[service_name] = (current_index + 1) % instances.size()`
7. 记录选择日志：`LOG_DEBUG("Round-robin selected instance: " + selected.host + ":" + std::to_string(selected.port))`

#### 步骤2：索引管理
**执行序列**：
1. 处理索引溢出：`if (current_index >= instances.size())`
2. 重置索引：`round_robin_index_[service_name] = 0`
3. 确保索引有效性：`current_index = current_index % instances.size()`

**结果**：通过轮询策略选择了合适的服务实例

### 2. 轮询状态维护流程

**业务场景**：维护轮询策略的状态信息

**执行步骤**：

#### 步骤1：索引同步
**执行序列**：
1. 实例列表变化时：`onInstanceListChanged(service_name)`
2. 检查索引有效性：`if (round_robin_index_[service_name] >= new_instance_count)`
3. 调整索引：`round_robin_index_[service_name] = round_robin_index_[service_name] % new_instance_count`

#### 步骤2：状态重置
**执行序列**：
1. 服务重启时：`resetRoundRobinState(service_name)`
2. 重置索引：`round_robin_index_[service_name] = 0`
3. 记录重置：`LOG_DEBUG("Reset round-robin state for service: " + service_name)`

**结果**：轮询状态保持一致性

## 加权轮询策略选择流程

### 1. 加权轮询实例选择流程

**业务场景**：根据实例权重进行加权轮询选择

**执行步骤**：

#### 步骤1：权重计算
```cpp
// 调用类：LoadBalancer
// 使用函数：LoadBalancer::selectWeightedRoundRobin()
auto instance = selectWeightedRoundRobin(service_name, healthy_instances);
```

**内部执行序列**：
1. `LoadBalancer::selectWeightedRoundRobin(service_name, instances)`
2. 边界检查：`if (instances.empty()) return std::nullopt`
3. 计算总权重：
   - 遍历所有实例：`for (const auto& instance : instances)`
   - 累加权重：`total_weight += instance.weight`
   - 验证权重：`if (total_weight <= 0) return selectRoundRobin(service_name, instances)`

#### 步骤2：随机权重选择
**执行序列**：
1. 生成随机数：
   - 创建随机数生成器：`static thread_local std::mt19937 gen(rd())`
   - 生成权重范围内随机数：`std::uniform_int_distribution<> dis(1, total_weight)`
   - 获取随机权重：`int random_weight = dis(gen)`
2. 权重区间匹配：
   - 初始化累计权重：`int current_weight = 0`
   - 遍历实例：`for (const auto& instance : instances)`
   - 累加权重：`current_weight += instance.weight`
   - 检查区间：`if (random_weight <= current_weight) return instance`

#### 步骤3：选择结果记录
**执行序列**：
1. 记录选择结果：`LOG_DEBUG("Weighted round-robin selected: " + instance.host + ":" + std::to_string(instance.port) + " (weight: " + std::to_string(instance.weight) + ")")`
2. 更新权重统计：`updateWeightStats(instance)`

**结果**：根据权重概率选择了合适的服务实例

### 2. 权重管理流程

**业务场景**：管理和调整服务实例的权重

**执行步骤**：

#### 步骤1：权重更新
```cpp
// 调用类：LoadBalancer
// 使用函数：LoadBalancer::updateInstanceWeight()
load_balancer->updateInstanceWeight("user-service", "************:8080", 5);
```

**内部执行序列**：
1. `LoadBalancer::updateInstanceWeight(service_name, instance_id, new_weight)`
2. 参数验证：`if (new_weight <= 0) throw std::invalid_argument("Weight must be positive")`
3. 查找目标实例：`auto instance = findInstance(service_name, instance_id)`
4. 更新权重：`instance->weight = new_weight`
5. 记录权重变更：`LOG_INFO("Updated weight for " + instance_id + " to " + std::to_string(new_weight))`

#### 步骤2：动态权重调整
**执行序列**：
1. 根据性能指标调整权重：`adjustWeightByPerformance(instance)`
2. 监控响应时间：`if (avg_response_time > threshold)`
3. 降低权重：`new_weight = current_weight * 0.8`
4. 应用新权重：`updateInstanceWeight(service_name, instance_id, new_weight)`

**结果**：实例权重得到合理调整

## 最少连接策略选择流程

### 1. 最少连接实例选择流程

**业务场景**：选择当前连接数最少的服务实例

**执行步骤**：

#### 步骤1：连接数比较
```cpp
// 调用类：LoadBalancer
// 使用函数：LoadBalancer::selectLeastConnections()
auto instance = selectLeastConnections(service_name, healthy_instances);
```

**内部执行序列**：
1. `LoadBalancer::selectLeastConnections(service_name, instances)`
2. 边界检查：`if (instances.empty()) return std::nullopt`
3. 初始化最小连接数查找：
   - 设置初始值：`size_t min_connections = SIZE_MAX`
   - 初始化选中实例：`ServiceInstance* selected = nullptr`
4. 遍历所有实例：
   - 获取实例ID：`std::string instance_id = generateInstanceId(instance)`
   - 获取连接数：`size_t connections = getConnectionCount(service_name, instance_id)`
   - 比较连接数：`if (connections < min_connections)`
   - 更新最小值：`min_connections = connections; selected = &instance`

#### 步骤2：连接数获取
**执行序列**：
1. `getConnectionCount(service_name, instance_id)`
2. 查找连接计数：`auto service_it = connection_counts_.find(service_name)`
3. 查找实例计数：`auto instance_it = service_it->second.find(instance_id)`
4. 返回连接数：`return instance_it != service_it->second.end() ? instance_it->second : 0`

#### 步骤3：选择结果处理
**执行序列**：
1. 验证选择结果：`if (selected == nullptr) return std::nullopt`
2. 记录选择：`LOG_DEBUG("Least connections selected: " + selected->host + ":" + std::to_string(selected->port) + " (connections: " + std::to_string(min_connections) + ")")`
3. 返回选中实例：`return *selected`

**结果**：选择了连接数最少的服务实例

### 2. 连接数管理流程

**业务场景**：管理和维护服务实例的连接计数

**执行步骤**：

#### 步骤1：连接数增加
```cpp
// 调用类：ApiGateway
// 使用函数：LoadBalancer::incrementConnections()
load_balancer->incrementConnections("user-service", "************:8080");
```

**内部执行序列**：
1. `LoadBalancer::incrementConnections(service_name, instance_id)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(instances_mutex_)`
3. 增加连接计数：`connection_counts_[service_name][instance_id]++`
4. 记录连接增加：`LOG_DEBUG("Incremented connections for " + instance_id + " to " + std::to_string(new_count))`

#### 步骤2：连接数减少
```cpp
// 调用类：ApiGateway
// 使用函数：LoadBalancer::decrementConnections()
load_balancer->decrementConnections("user-service", "************:8080");
```

**内部执行序列**：
1. `LoadBalancer::decrementConnections(service_name, instance_id)`
2. 获取写锁：`std::unique_lock<std::shared_mutex> lock(instances_mutex_)`
3. 检查连接数：`if (connection_counts_[service_name][instance_id] > 0)`
4. 减少连接计数：`connection_counts_[service_name][instance_id]--`
5. 记录连接减少：`LOG_DEBUG("Decremented connections for " + instance_id + " to " + std::to_string(new_count))`

#### 步骤3：连接数重置
**执行序列**：
1. 实例重启时：`resetConnections(service_name, instance_id)`
2. 重置计数：`connection_counts_[service_name][instance_id] = 0`
3. 记录重置：`LOG_INFO("Reset connections for " + instance_id)`

**结果**：连接数统计保持准确性

## 健康检查管理流程

### 1. 健康检查执行流程

**业务场景**：定期检查服务实例的健康状态

**执行步骤**：

#### 步骤1：健康检查调度
**执行序列**：
1. 启动健康检查任务：`startHealthCheckTask()`
2. 创建定时器：`auto timer = std::make_shared<Timer>(health_check_interval)`
3. 设置检查回调：`timer->setCallback([this] { performHealthChecks(); })`
4. 启动定时器：`timer->start()`

#### 步骤2：批量健康检查
```cpp
// 调用类：LoadBalancer
// 使用函数：LoadBalancer::performHealthChecks()
void LoadBalancer::performHealthChecks() {
    // 执行所有实例的健康检查
}
```

**内部执行序列**：
1. `LoadBalancer::performHealthChecks()`
2. 获取所有服务实例：`auto all_instances = getAllInstances()`
3. 并行执行健康检查：
   - 创建检查任务：`for (const auto& [service_name, instances] : all_instances)`
   - 提交到线程池：`thread_pool->submit([this, service_name, instance] { checkInstanceHealth(service_name, instance); })`

#### 步骤3：单实例健康检查
**执行序列**：
1. `checkInstanceHealth(service_name, instance)`
2. 构建健康检查URL：`std::string health_url = "http://" + instance.host + ":" + std::to_string(instance.port) + "/health"`
3. 发送HTTP请求：`auto response = http_client.get(health_url, timeout)`
4. 判断健康状态：
   - 检查响应码：`bool healthy = (response.status_code == 200)`
   - 检查响应时间：`bool fast_enough = (response.duration < max_response_time)`
   - 综合判断：`bool final_healthy = healthy && fast_enough`
5. 更新健康状态：`updateInstanceHealth(service_name, instance_id, final_healthy)`

**结果**：所有服务实例的健康状态得到更新

### 2. 健康状态恢复流程

**业务场景**：处理不健康实例的恢复

**执行步骤**：

#### 步骤1：故障检测
**执行序列**：
1. 连续健康检查失败：`if (consecutive_failures >= failure_threshold)`
2. 标记实例不健康：`markInstanceUnhealthy(service_name, instance_id)`
3. 从负载均衡中移除：`excludeFromLoadBalancing(service_name, instance_id)`
4. 记录故障：`LOG_WARN("Instance " + instance_id + " marked as unhealthy")`

#### 步骤2：恢复检测
**执行序列**：
1. 继续监控不健康实例：`continueMonitoring(unhealthy_instances)`
2. 检测恢复信号：`if (health_check_success)`
3. 逐步恢复流程：
   - 标记为恢复中：`markInstanceRecovering(service_name, instance_id)`
   - 进行多次验证：`performRecoveryValidation(instance)`
   - 确认恢复：`if (validation_success_count >= recovery_threshold)`
4. 重新加入负载均衡：`includeInLoadBalancing(service_name, instance_id)`

**结果**：不健康实例得到正确的恢复处理

## 连接数统计流程

### 1. 连接统计收集流程

**业务场景**：收集和维护连接数统计信息

**执行步骤**：

#### 步骤1：连接事件监听
**执行序列**：
1. 监听连接建立事件：`onConnectionEstablished(service_name, instance_id)`
2. 监听连接关闭事件：`onConnectionClosed(service_name, instance_id)`
3. 监听连接超时事件：`onConnectionTimeout(service_name, instance_id)`

#### 步骤2：统计数据更新
**执行序列**：
1. 连接建立时：`incrementConnections(service_name, instance_id)`
2. 连接关闭时：`decrementConnections(service_name, instance_id)`
3. 更新统计时间：`updateLastActivityTime(service_name, instance_id)`

#### 步骤3：统计数据验证
**执行序列**：
1. 定期验证统计准确性：`validateConnectionCounts()`
2. 检查计数异常：`if (count < 0 || count > max_connections)`
3. 修正异常数据：`correctConnectionCount(service_name, instance_id)`

**结果**：连接统计数据保持准确和一致

### 2. 统计信息查询流程

**业务场景**：提供连接统计信息查询接口

**执行步骤**：

#### 步骤1：统计查询
```cpp
// 调用类：监控系统
// 使用函数：LoadBalancer::getConnectionStats()
auto stats = load_balancer->getConnectionStats();
```

**内部执行序列**：
1. `LoadBalancer::getConnectionStats()`
2. 获取读锁：`std::shared_lock<std::shared_mutex> lock(instances_mutex_)`
3. 收集统计数据：
   - 总连接数：`total_connections = sum(all_connection_counts)`
   - 平均连接数：`avg_connections = total_connections / instance_count`
   - 最大连接数：`max_connections = max(all_connection_counts)`
   - 最小连接数：`min_connections = min(all_connection_counts)`
4. 构建统计报告：`return ConnectionStats{total, avg, max, min}`

#### 步骤2：实例级统计
**执行序列**：
1. 获取特定实例统计：`getInstanceConnectionCount(service_name, instance_id)`
2. 获取服务级统计：`getServiceConnectionStats(service_name)`
3. 计算连接分布：`calculateConnectionDistribution(service_name)`

**结果**：提供详细的连接统计信息

## 总结

LoadBalancer系统的业务流程体现了高效负载均衡的核心特性：

1. **多策略支持**：轮询、加权轮询、最少连接等多种算法
2. **动态实例管理**：实时添加、移除和更新服务实例
3. **健康状态监控**：持续监控实例健康状态和自动恢复
4. **连接数统计**：精确的连接计数和负载监控
5. **线程安全**：高并发环境下的安全操作
6. **性能优化**：高效的实例选择和状态管理
7. **可观测性**：详细的统计信息和监控指标

每个业务流程都经过精心设计，确保高性能、高可靠性和易扩展性。

## 外部模块依赖关系

### 1. 核心依赖模块
- **HttpClient**: HTTP健康检查请求
- **Timer**: 定时健康检查任务
- **ThreadPool**: 并行健康检查执行
- **Logger**: 统一的日志记录系统

### 2. 标准库依赖
- **std::shared_mutex**: 读写锁和并发控制
- **std::atomic**: 线程安全的计数器
- **std::chrono**: 时间测量和超时管理
- **std::random**: 加权轮询的随机数生成

### 3. 集成特性
- **与ApiGateway集成**: 作为请求分发的核心组件
- **与服务发现集成**: 自动管理服务实例列表
- **与监控系统集成**: 提供负载均衡指标
- **与配置系统集成**: 支持策略和参数的动态配置
