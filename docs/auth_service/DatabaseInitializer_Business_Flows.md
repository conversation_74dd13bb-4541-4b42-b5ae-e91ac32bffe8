# DatabaseInitializer 业务流程详细分析

## 目录
1. [数据库初始化器创建流程](#数据库初始化器创建流程)
2. [数据库连接和验证流程](#数据库连接和验证流程)
3. [表结构创建和迁移流程](#表结构创建和迁移流程)
4. [索引和约束创建流程](#索引和约束创建流程)
5. [数据库版本管理流程](#数据库版本管理流程)
6. [初始数据填充流程](#初始数据填充流程)

## 数据库初始化器创建流程

### 1. 初始化器实例创建和配置加载流程

**业务场景**：创建数据库初始化器实例并加载数据库配置

**执行步骤**：

#### 步骤1：初始化器实例创建
```cpp
// 调用类：AuthService
// 使用函数：DatabaseInitializer::DatabaseInitializer()
database_initializer_ = std::make_unique<DatabaseInitializer>(config.database_config);
```

**内部执行序列**：
1. `DatabaseInitializer::DatabaseInitializer(database_config)` - 构造函数
2. 保存数据库配置：`config_ = database_config`
3. 初始化配置参数：
   - `mysql_host_ = database_config.mysql_host` - MySQL主机地址
   - `mysql_port_ = database_config.mysql_port` - MySQL端口（默认3306）
   - `mysql_database_ = database_config.mysql_database` - 数据库名称
   - `mysql_username_ = database_config.mysql_username` - 数据库用户名
   - `mysql_password_ = database_config.mysql_password` - 数据库密码
   - `redis_host_ = database_config.redis_host` - Redis主机地址
   - `redis_port_ = database_config.redis_port` - Redis端口（默认6379）

#### 步骤2：初始化状态设置
**执行序列**：
1. 初始化状态变量：
   ```cpp
   mysql_connection_ = nullptr;
   redis_connection_ = nullptr;
   initialized_ = false;
   current_schema_version_ = 0;
   target_schema_version_ = LATEST_SCHEMA_VERSION; // 当前代码支持的最新版本
   ```

2. 加载SQL脚本路径：
   ```cpp
   schema_scripts_path_ = config_.schema_scripts_path.empty() ? 
                         "sql/schema/" : config_.schema_scripts_path;
   migration_scripts_path_ = config_.migration_scripts_path.empty() ? 
                            "sql/migrations/" : config_.migration_scripts_path;
   ```

3. 记录初始化开始日志：
   ```cpp
   LOG_INFO("DatabaseInitializer created for database: " + mysql_database_);
   ```

**结果**：数据库初始化器实例创建完成

### 2. 数据库驱动加载流程

**业务场景**：加载和配置数据库驱动程序

**执行步骤**：

#### 步骤1：MySQL驱动初始化
```cpp
// 内部调用：DatabaseInitializer::initializeDrivers()
initializeDrivers();
```

**内部执行序列**：
1. 加载MySQL Connector/C++驱动：
   ```cpp
   try {
       mysql_driver_ = sql::mysql::get_mysql_driver_instance();
       if (!mysql_driver_) {
           throw std::runtime_error("Failed to get MySQL driver instance");
       }
       LOG_INFO("MySQL driver loaded successfully");
   } catch (const std::exception& e) {
       LOG_ERROR("Failed to load MySQL driver: " + std::string(e.what()));
       throw;
   }
   ```

2. 配置连接属性：
   ```cpp
   connection_properties_["hostName"] = mysql_host_;
   connection_properties_["port"] = mysql_port_;
   connection_properties_["userName"] = mysql_username_;
   connection_properties_["password"] = mysql_password_;
   connection_properties_["schema"] = mysql_database_;
   connection_properties_["charset"] = "utf8mb4";
   connection_properties_["autoReconnect"] = true;
   connection_properties_["connectTimeout"] = 30;
   ```

#### 步骤2：Redis客户端初始化
**执行序列**：
1. 初始化Redis连接配置：
   ```cpp
   redis_config_.host = redis_host_;
   redis_config_.port = redis_port_;
   redis_config_.password = config_.redis_password;
   redis_config_.connect_timeout = std::chrono::seconds(10);
   redis_config_.socket_timeout = std::chrono::seconds(5);
   ```

**结果**：数据库驱动加载完成

## 数据库连接和验证流程

### 1. 数据库连接建立流程

**业务场景**：建立到MySQL和Redis的数据库连接

**执行步骤**：

#### 步骤1：数据库初始化请求
```cpp
// 调用类：AuthService
// 使用函数：DatabaseInitializer::initialize()
bool success = database_initializer_->initialize();
```

**内部执行序列**：
1. `DatabaseInitializer::initialize()`
2. 检查初始化状态：
   ```cpp
   if (initialized_) {
       LOG_WARNING("DatabaseInitializer already initialized");
       return true;
   }
   ```

3. 建立MySQL连接：
   ```cpp
   try {
       mysql_connection_ = mysql_driver_->connect(connection_properties_);
       if (!mysql_connection_ || mysql_connection_->isClosed()) {
           throw std::runtime_error("Failed to establish MySQL connection");
       }
       LOG_INFO("MySQL connection established successfully");
   } catch (const std::exception& e) {
       LOG_ERROR("MySQL connection failed: " + std::string(e.what()));
       return false;
   }
   ```

#### 步骤2：Redis连接建立
**执行序列**：
1. 建立Redis连接：
   ```cpp
   try {
       redis_connection_ = std::make_unique<RedisClient>(redis_config_);
       redis_connection_->connect();
       
       // 测试连接
       auto ping_result = redis_connection_->ping();
       if (ping_result != "PONG") {
           throw std::runtime_error("Redis ping test failed");
       }
       LOG_INFO("Redis connection established successfully");
   } catch (const std::exception& e) {
       LOG_ERROR("Redis connection failed: " + std::string(e.what()));
       return false;
   }
   ```

#### 步骤3：数据库存在性检查
**执行序列**：
1. 检查数据库是否存在：
   ```cpp
   bool database_exists = checkDatabaseExists();
   if (!database_exists) {
       LOG_INFO("Database does not exist, creating: " + mysql_database_);
       if (!createDatabase()) {
           LOG_ERROR("Failed to create database");
           return false;
       }
   }
   ```

2. 选择数据库：
   ```cpp
   mysql_connection_->setSchema(mysql_database_);
   LOG_INFO("Using database: " + mysql_database_);
   ```

**结果**：数据库连接建立成功

### 2. 数据库版本检查流程

**业务场景**：检查当前数据库的架构版本

**执行步骤**：

#### 步骤1：版本表检查
```cpp
// 内部调用：DatabaseInitializer::checkSchemaVersion()
current_schema_version_ = checkSchemaVersion();
```

**内部执行序列**：
1. 检查版本表是否存在：
   ```sql
   SELECT COUNT(*) FROM information_schema.tables 
   WHERE table_schema = ? AND table_name = 'schema_version'
   ```

2. 如果版本表不存在，创建版本表：
   ```sql
   CREATE TABLE schema_version (
       version INT NOT NULL PRIMARY KEY,
       applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       description VARCHAR(255),
       checksum VARCHAR(64)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
   ```

3. 获取当前版本：
   ```sql
   SELECT MAX(version) FROM schema_version
   ```

#### 步骤2：版本兼容性检查
**执行序列**：
1. 检查版本兼容性：
   ```cpp
   if (current_schema_version_ > target_schema_version_) {
       LOG_ERROR("Database schema version (" + std::to_string(current_schema_version_) + 
                ") is newer than supported version (" + std::to_string(target_schema_version_) + ")");
       return false;
   }
   ```

2. 确定是否需要迁移：
   ```cpp
   bool needs_migration = (current_schema_version_ < target_schema_version_);
   if (needs_migration) {
       LOG_INFO("Database migration required from version " + 
               std::to_string(current_schema_version_) + " to " + 
               std::to_string(target_schema_version_));
   }
   ```

**结果**：数据库版本检查完成

## 表结构创建和迁移流程

### 1. 基础表结构创建流程

**业务场景**：创建认证服务所需的基础表结构

**执行步骤**：

#### 步骤1：用户表创建
```cpp
// 内部调用：DatabaseInitializer::createTables()
bool success = createTables();
```

**内部执行序列**：
1. 创建用户表：
   ```sql
   CREATE TABLE IF NOT EXISTS users (
       user_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
       username VARCHAR(50) UNIQUE,
       email VARCHAR(255) UNIQUE,
       password_hash VARCHAR(255) NOT NULL,
       nickname VARCHAR(100),
       avatar_url VARCHAR(500),
       phone VARCHAR(20),
       birth_date DATE,
       gender ENUM('male', 'female', 'other'),
       country VARCHAR(100),
       timezone VARCHAR(50),
       language VARCHAR(10) DEFAULT 'en',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
       last_login_at TIMESTAMP NULL,
       last_login_ip VARCHAR(45),
       last_login_user_agent TEXT,
       is_active BOOLEAN DEFAULT TRUE,
       is_verified BOOLEAN DEFAULT FALSE,
       verification_token VARCHAR(255),
       PRIMARY KEY (user_id)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
   ```

#### 步骤2：游戏数据表创建
**执行序列**：
1. 创建游戏用户数据表：
   ```sql
   CREATE TABLE IF NOT EXISTS game_user_data (
       id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
       user_id BIGINT UNSIGNED NOT NULL,
       game_type INT NOT NULL,
       level INT DEFAULT 1,
       experience BIGINT DEFAULT 0,
       coins BIGINT DEFAULT 0,
       gems INT DEFAULT 0,
       total_games INT DEFAULT 0,
       wins INT DEFAULT 0,
       losses INT DEFAULT 0,
       draws INT DEFAULT 0,
       best_score BIGINT DEFAULT 0,
       achievements JSON,
       settings JSON,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
       PRIMARY KEY (id),
       UNIQUE KEY uk_user_game (user_id, game_type),
       FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
   ```

#### 步骤3：会话和令牌表创建
**执行序列**：
1. 创建刷新令牌表：
   ```sql
   CREATE TABLE IF NOT EXISTS refresh_tokens (
       id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
       jti VARCHAR(36) NOT NULL UNIQUE,
       user_id BIGINT UNSIGNED NOT NULL,
       expires_at TIMESTAMP NOT NULL,
       is_active BOOLEAN DEFAULT TRUE,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       revoked_at TIMESTAMP NULL,
       PRIMARY KEY (id),
       INDEX idx_jti (jti),
       INDEX idx_user_id (user_id),
       INDEX idx_expires_at (expires_at),
       FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
   ```

2. 创建游戏会话表：
   ```sql
   CREATE TABLE IF NOT EXISTS game_sessions (
       id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
       session_id VARCHAR(36) NOT NULL UNIQUE,
       user_id BIGINT UNSIGNED NOT NULL,
       game_type INT NOT NULL,
       server_id VARCHAR(50),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       expires_at TIMESTAMP NOT NULL,
       ended_at TIMESTAMP NULL,
       is_active BOOLEAN DEFAULT TRUE,
       PRIMARY KEY (id),
       INDEX idx_session_id (session_id),
       INDEX idx_user_id (user_id),
       INDEX idx_game_type (game_type),
       INDEX idx_expires_at (expires_at),
       FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
   ```

#### 步骤4：审计和日志表创建
**执行序列**：
1. 创建密码历史表：
   ```sql
   CREATE TABLE IF NOT EXISTS password_history (
       id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
       user_id BIGINT UNSIGNED NOT NULL,
       password_hash VARCHAR(255) NOT NULL,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       PRIMARY KEY (id),
       INDEX idx_user_id (user_id),
       INDEX idx_created_at (created_at),
       FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
   ```

2. 创建登录日志表：
   ```sql
   CREATE TABLE IF NOT EXISTS login_logs (
       id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
       user_id BIGINT UNSIGNED,
       username VARCHAR(50),
       email VARCHAR(255),
       ip_address VARCHAR(45),
       user_agent TEXT,
       login_result ENUM('success', 'failed', 'blocked') NOT NULL,
       failure_reason VARCHAR(255),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       PRIMARY KEY (id),
       INDEX idx_user_id (user_id),
       INDEX idx_ip_address (ip_address),
       INDEX idx_login_result (login_result),
       INDEX idx_created_at (created_at),
       FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
   ```

**结果**：基础表结构创建完成

### 2. 数据库迁移执行流程

**业务场景**：执行数据库架构迁移

**执行步骤**：

#### 步骤1：迁移脚本扫描
```cpp
// 内部调用：DatabaseInitializer::runMigrations()
bool success = runMigrations();
```

**内部执行序列**：
1. 扫描迁移脚本：
   ```cpp
   std::vector<MigrationScript> pending_migrations;
   for (int version = current_schema_version_ + 1; version <= target_schema_version_; ++version) {
       std::string script_path = migration_scripts_path_ + "/migration_" + 
                                std::to_string(version) + ".sql";
       if (std::filesystem::exists(script_path)) {
           pending_migrations.push_back({version, script_path});
       }
   }
   ```

2. 验证迁移脚本：
   ```cpp
   for (const auto& migration : pending_migrations) {
       if (!validateMigrationScript(migration)) {
           LOG_ERROR("Invalid migration script: " + migration.script_path);
           return false;
       }
   }
   ```

#### 步骤2：迁移执行
**执行序列**：
1. 开启事务：
   ```cpp
   mysql_connection_->setAutoCommit(false);
   ```

2. 执行每个迁移脚本：
   ```cpp
   for (const auto& migration : pending_migrations) {
       try {
           LOG_INFO("Applying migration version " + std::to_string(migration.version));
           
           std::string sql_content = readFile(migration.script_path);
           auto statements = splitSQLStatements(sql_content);
           
           for (const auto& statement : statements) {
               if (!statement.empty()) {
                   auto stmt = mysql_connection_->createStatement();
                   stmt->execute(statement);
               }
           }
           
           // 记录迁移版本
           recordMigrationVersion(migration.version, migration.description);
           
           LOG_INFO("Migration version " + std::to_string(migration.version) + " applied successfully");
           
       } catch (const std::exception& e) {
           LOG_ERROR("Migration failed at version " + std::to_string(migration.version) + 
                    ": " + std::string(e.what()));
           mysql_connection_->rollback();
           return false;
       }
   }
   ```

3. 提交事务：
   ```cpp
   mysql_connection_->commit();
   mysql_connection_->setAutoCommit(true);
   ```

**结果**：数据库迁移执行完成

## 索引和约束创建流程

### 1. 性能索引创建流程

**业务场景**：创建提高查询性能的数据库索引

**执行步骤**：

#### 步骤1：用户表索引创建
```cpp
// 内部调用：DatabaseInitializer::createIndexes()
bool success = createIndexes();
```

**内部执行序列**：
1. 创建用户表索引：
   ```sql
   -- 用户名索引（唯一）
   CREATE UNIQUE INDEX idx_users_username ON users(username);
   
   -- 邮箱索引（唯一）
   CREATE UNIQUE INDEX idx_users_email ON users(email);
   
   -- 活跃用户索引
   CREATE INDEX idx_users_active ON users(is_active);
   
   -- 最后登录时间索引
   CREATE INDEX idx_users_last_login ON users(last_login_at);
   
   -- 创建时间索引
   CREATE INDEX idx_users_created_at ON users(created_at);
   
   -- 验证状态索引
   CREATE INDEX idx_users_verified ON users(is_verified);
   ```

#### 步骤2：游戏数据表索引创建
**执行序列**：
1. 创建游戏数据索引：
   ```sql
   -- 用户游戏类型复合索引（已在表创建时定义为唯一键）
   -- 游戏类型索引
   CREATE INDEX idx_game_data_type ON game_user_data(game_type);
   
   -- 等级索引（用于排行榜）
   CREATE INDEX idx_game_data_level ON game_user_data(level DESC);
   
   -- 最佳分数索引（用于排行榜）
   CREATE INDEX idx_game_data_best_score ON game_user_data(best_score DESC);
   
   -- 更新时间索引
   CREATE INDEX idx_game_data_updated ON game_user_data(updated_at);
   ```

#### 步骤3：会话和令牌表索引创建
**执行序列**：
1. 创建令牌表索引：
   ```sql
   -- 刷新令牌表索引（已在表创建时定义）
   -- 过期时间和活跃状态复合索引
   CREATE INDEX idx_refresh_tokens_active_expires ON refresh_tokens(is_active, expires_at);
   
   -- 游戏会话表索引
   -- 活跃会话索引
   CREATE INDEX idx_game_sessions_active ON game_sessions(is_active);
   
   -- 用户游戏类型复合索引
   CREATE INDEX idx_game_sessions_user_game ON game_sessions(user_id, game_type);
   
   -- 服务器ID索引
   CREATE INDEX idx_game_sessions_server ON game_sessions(server_id);
   ```

**结果**：数据库索引创建完成

### 2. 外键约束验证流程

**业务场景**：验证和创建表间的外键约束

**执行步骤**：

#### 步骤1：外键约束检查
```cpp
// 内部调用：DatabaseInitializer::validateForeignKeys()
bool valid = validateForeignKeys();
```

**内部执行序列**：
1. 检查现有外键约束：
   ```sql
   SELECT 
       CONSTRAINT_NAME,
       TABLE_NAME,
       COLUMN_NAME,
       REFERENCED_TABLE_NAME,
       REFERENCED_COLUMN_NAME
   FROM information_schema.KEY_COLUMN_USAGE
   WHERE CONSTRAINT_SCHEMA = ? AND REFERENCED_TABLE_NAME IS NOT NULL
   ```

2. 验证约束完整性：
   ```cpp
   std::vector<ForeignKeyConstraint> expected_constraints = {
       {"fk_game_data_user", "game_user_data", "user_id", "users", "user_id"},
       {"fk_refresh_tokens_user", "refresh_tokens", "user_id", "users", "user_id"},
       {"fk_game_sessions_user", "game_sessions", "user_id", "users", "user_id"},
       {"fk_password_history_user", "password_history", "user_id", "users", "user_id"},
       {"fk_login_logs_user", "login_logs", "user_id", "users", "user_id"}
   };
   
   for (const auto& constraint : expected_constraints) {
       if (!constraintExists(constraint)) {
           LOG_WARNING("Missing foreign key constraint: " + constraint.name);
           createForeignKeyConstraint(constraint);
       }
   }
   ```

**结果**：外键约束验证和创建完成

## 数据库版本管理流程

### 1. 版本记录管理流程

**业务场景**：记录和管理数据库架构版本

**执行步骤**：

#### 步骤1：版本记录创建
```cpp
// 内部调用：DatabaseInitializer::recordMigrationVersion()
recordMigrationVersion(version, description);
```

**内部执行序列**：
1. 计算迁移脚本校验和：
   ```cpp
   std::string script_content = readFile(migration.script_path);
   std::string checksum = calculateSHA256(script_content);
   ```

2. 记录版本信息：
   ```sql
   INSERT INTO schema_version (version, description, checksum, applied_at)
   VALUES (?, ?, ?, NOW())
   ```

3. 更新当前版本：
   ```cpp
   current_schema_version_ = version;
   ```

#### 步骤2：版本一致性检查
```cpp
// 内部调用：DatabaseInitializer::validateSchemaConsistency()
bool consistent = validateSchemaConsistency();
```

**内部执行序列**：
1. 检查版本记录连续性：
   ```sql
   SELECT version FROM schema_version ORDER BY version
   ```

2. 验证是否有缺失的版本：
   ```cpp
   for (int v = 1; v <= current_schema_version_; ++v) {
       if (applied_versions.find(v) == applied_versions.end()) {
           LOG_ERROR("Missing schema version: " + std::to_string(v));
           return false;
       }
   }
   ```

**结果**：数据库版本管理完成

## 初始数据填充流程

### 1. 基础数据填充流程

**业务场景**：填充系统运行所需的基础数据

**执行步骤**：

#### 步骤1：系统配置数据
```cpp
// 内部调用：DatabaseInitializer::populateInitialData()
bool success = populateInitialData();
```

**内部执行序列**：
1. 创建系统配置表（如果需要）：
   ```sql
   CREATE TABLE IF NOT EXISTS system_config (
       config_key VARCHAR(100) NOT NULL PRIMARY KEY,
       config_value TEXT,
       description VARCHAR(255),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
   ```

2. 插入默认配置：
   ```sql
   INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
   ('system.version', '1.0.0', 'System version'),
   ('auth.session_timeout', '1800', 'Session timeout in seconds'),
   ('auth.max_login_attempts', '5', 'Maximum login attempts before lockout'),
   ('game.default_coins', '100', 'Default coins for new users'),
   ('game.max_level', '100', 'Maximum game level')
   ```

#### 步骤2：游戏类型数据
**执行序列**：
1. 创建游戏类型表：
   ```sql
   CREATE TABLE IF NOT EXISTS game_types (
       game_type_id INT NOT NULL PRIMARY KEY,
       name VARCHAR(50) NOT NULL,
       display_name VARCHAR(100) NOT NULL,
       description TEXT,
       is_active BOOLEAN DEFAULT TRUE,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
   ```

2. 插入游戏类型数据：
   ```sql
   INSERT IGNORE INTO game_types (game_type_id, name, display_name, description) VALUES
   (1, 'snake', 'Snake Game', 'Classic snake game'),
   (2, 'tetris', 'Tetris', 'Block puzzle game'),
   (3, 'puzzle', 'Puzzle Game', 'Various puzzle games'),
   (4, 'arcade', 'Arcade Games', 'Classic arcade style games')
   ```

#### 步骤3：管理员账户创建
**执行序列**：
1. 检查是否存在管理员账户：
   ```sql
   SELECT COUNT(*) FROM users WHERE username = 'admin'
   ```

2. 如果不存在，创建默认管理员：
   ```cpp
   if (admin_count == 0) {
       std::string admin_password = generateSecurePassword();
       std::string password_hash = hashPassword(admin_password);
       
       // 插入管理员账户
       auto stmt = mysql_connection_->prepareStatement(
           "INSERT INTO users (username, email, password_hash, nickname, is_active, is_verified) "
           "VALUES ('admin', 'admin@localhost', ?, 'Administrator', 1, 1)"
       );
       stmt->setString(1, password_hash);
       stmt->executeUpdate();
       
       LOG_INFO("Default admin account created with password: " + admin_password);
       LOG_WARNING("Please change the default admin password immediately!");
   }
   ```

**结果**：初始数据填充完成

### 2. 数据完整性验证流程

**业务场景**：验证填充数据的完整性和正确性

**执行步骤**：

#### 步骤1：数据完整性检查
```cpp
// 内部调用：DatabaseInitializer::validateDataIntegrity()
bool valid = validateDataIntegrity();
```

**内部执行序列**：
1. 检查必需的配置项：
   ```cpp
   std::vector<std::string> required_configs = {
       "system.version", "auth.session_timeout", "auth.max_login_attempts"
   };
   
   for (const auto& config_key : required_configs) {
       auto stmt = mysql_connection_->prepareStatement(
           "SELECT COUNT(*) FROM system_config WHERE config_key = ?"
       );
       stmt->setString(1, config_key);
       auto result = stmt->executeQuery();
       
       if (result->next() && result->getInt(1) == 0) {
           LOG_ERROR("Missing required configuration: " + config_key);
           return false;
       }
   }
   ```

2. 验证游戏类型数据：
   ```sql
   SELECT COUNT(*) FROM game_types WHERE is_active = 1
   ```

3. 检查表结构完整性：
   ```cpp
   std::vector<std::string> required_tables = {
       "users", "game_user_data", "refresh_tokens", "game_sessions",
       "password_history", "login_logs", "schema_version"
   };
   
   for (const auto& table : required_tables) {
       if (!tableExists(table)) {
           LOG_ERROR("Required table missing: " + table);
           return false;
       }
   }
   ```

**结果**：数据完整性验证完成

### 3. 初始化完成确认流程

**业务场景**：确认数据库初始化完全成功

**执行步骤**：

#### 步骤1：最终状态确认
```cpp
// 内部调用：DatabaseInitializer::finalizeInitialization()
bool success = finalizeInitialization();
```

**内部执行序列**：
1. 设置初始化完成标志：
   ```cpp
   initialized_ = true;
   ```

2. 记录初始化完成日志：
   ```cpp
   LOG_INFO("Database initialization completed successfully");
   LOG_INFO("Current schema version: " + std::to_string(current_schema_version_));
   LOG_INFO("Total tables created: " + std::to_string(getTableCount()));
   LOG_INFO("Total indexes created: " + std::to_string(getIndexCount()));
   ```

3. 生成初始化报告：
   ```cpp
   InitializationReport report;
   report.success = true;
   report.schema_version = current_schema_version_;
   report.tables_created = getTableCount();
   report.indexes_created = getIndexCount();
   report.initial_data_populated = true;
   report.duration = std::chrono::duration_cast<std::chrono::milliseconds>(
       std::chrono::steady_clock::now() - initialization_start_time_
   );
   
   LOG_INFO("Initialization report: " + report.toJson());
   ```

**结果**：数据库初始化完全完成

## 总结

DatabaseInitializer系统的业务流程体现了现代数据库管理的核心特性：

1. **自动化初始化**：完全自动化的数据库创建和配置流程
2. **版本管理**：完善的数据库架构版本控制和迁移机制
3. **数据完整性**：全面的数据完整性检查和约束管理
4. **性能优化**：自动创建必要的索引和性能优化配置
5. **错误恢复**：完善的错误处理和回滚机制
6. **安全配置**：安全的默认配置和权限设置
7. **监控审计**：详细的初始化日志和状态报告
8. **可扩展性**：支持未来的架构变更和扩展

每个业务流程都经过精心设计，确保数据库初始化的可靠性、安全性和可维护性。
