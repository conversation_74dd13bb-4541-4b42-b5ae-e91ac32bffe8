# HttpServer 业务流程详细分析

## 目录
1. [HTTP服务器初始化流程](#HTTP服务器初始化流程)
2. [请求接收和路由流程](#请求接收和路由流程)
3. [中间件处理流程](#中间件处理流程)
4. [响应生成和发送流程](#响应生成和发送流程)
5. [连接管理和生命周期](#连接管理和生命周期)
6. [错误处理和监控流程](#错误处理和监控流程)

## HTTP服务器初始化流程

### 1. HTTP服务器创建和配置加载流程

**业务场景**：创建HTTP服务器实例并加载网络配置

**执行步骤**：

#### 步骤1：服务器实例创建
```cpp
// 调用类：AuthService
// 使用函数：HttpServer::HttpServer()
http_server_ = std::make_unique<HttpServer>(config_.server_config);
```

**内部执行序列**：
1. `HttpServer::HttpServer(server_config)` - 构造函数
2. 保存服务器配置：`config_ = server_config`
3. 初始化配置参数：
   - `host_ = server_config.host` - 监听地址（默认"0.0.0.0"）
   - `port_ = server_config.port` - 监听端口（默认8008）
   - `worker_threads_ = server_config.worker_threads` - 工作线程数（默认4）
   - `keep_alive_timeout_ms_ = server_config.keep_alive_timeout_ms` - Keep-Alive超时（默认60000）
   - `request_timeout_ms_ = server_config.request_timeout_ms` - 请求超时（默认30000）
   - `max_connections_ = server_config.max_connections` - 最大连接数（默认1000）

#### 步骤2：网络组件初始化
**执行序列**：
1. 创建EventLoop：
   ```cpp
   event_loop_ = std::make_unique<EventLoop>();
   if (!event_loop_) {
       throw std::runtime_error("Failed to create EventLoop");
   }
   ```

2. 创建监听Socket：
   ```cpp
   listen_socket_ = std::make_unique<Socket>();
   listen_socket_->setReuseAddr(true);
   listen_socket_->setReusePort(true);
   listen_socket_->setNonBlocking(true);
   ```

3. 初始化路由器：
   ```cpp
   router_ = std::make_unique<HttpRouter>();
   ```

4. 创建中间件管理器：
   ```cpp
   middleware_manager_ = std::make_unique<MiddlewareManager>();
   setupDefaultMiddlewares();
   ```

#### 步骤3：线程池创建
**执行序列**：
1. 创建工作线程池：
   ```cpp
   thread_pool_ = std::make_shared<ThreadPool>(worker_threads_);
   if (!thread_pool_->initialize()) {
       throw std::runtime_error("Failed to initialize thread pool");
   }
   ```

2. 初始化连接管理器：
   ```cpp
   connection_manager_ = std::make_unique<ConnectionManager>(max_connections_);
   ```

3. 记录初始化日志：
   ```cpp
   LOG_INFO("HttpServer initialized - Host: " + host_ + ", Port: " + std::to_string(port_) + 
            ", Workers: " + std::to_string(worker_threads_));
   ```

**结果**：HTTP服务器初始化完成

### 2. 默认中间件设置流程

**业务场景**：设置HTTP服务器的默认中间件

**执行步骤**：

#### 步骤1：默认中间件注册
```cpp
// 内部调用：HttpServer::setupDefaultMiddlewares()
setupDefaultMiddlewares();
```

**内部执行序列**：
1. 注册CORS中间件：
   ```cpp
   middleware_manager_->use([](const HttpRequest& req, HttpResponse& res, std::function<void()> next) {
       // 设置CORS头
       res.setHeader("Access-Control-Allow-Origin", "*");
       res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
       res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
       
       if (req.getMethod() == HttpMethod::OPTIONS) {
           res.setStatus(200);
           return;
       }
       
       next();
   });
   ```

2. 注册请求日志中间件：
   ```cpp
   middleware_manager_->use([](const HttpRequest& req, HttpResponse& res, std::function<void()> next) {
       auto start_time = std::chrono::high_resolution_clock::now();
       
       next();
       
       auto end_time = std::chrono::high_resolution_clock::now();
       auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
       
       LOG_INFO("HTTP " + methodToString(req.getMethod()) + " " + req.getPath() + 
                " -> " + std::to_string(res.getStatus()) + " (" + std::to_string(duration.count()) + "ms)");
   });
   ```

3. 注册安全头中间件：
   ```cpp
   middleware_manager_->use([](const HttpRequest& req, HttpResponse& res, std::function<void()> next) {
       res.setHeader("X-Content-Type-Options", "nosniff");
       res.setHeader("X-Frame-Options", "DENY");
       res.setHeader("X-XSS-Protection", "1; mode=block");
       res.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
       
       next();
   });
   ```

**结果**：默认中间件设置完成

## 请求接收和路由流程

### 1. 服务器启动和监听流程

**业务场景**：启动HTTP服务器开始监听客户端连接

**执行步骤**：

#### 步骤1：服务器启动请求
```cpp
// 调用类：AuthService
// 使用函数：HttpServer::start()
bool success = http_server_->start();
```

**内部执行序列**：
1. `HttpServer::start()`
2. 绑定监听地址：
   ```cpp
   if (!listen_socket_->bind(host_, port_)) {
       LOG_ERROR("Failed to bind to " + host_ + ":" + std::to_string(port_));
       return false;
   }
   ```

3. 开始监听：
   ```cpp
   if (!listen_socket_->listen(128)) { // backlog = 128
       LOG_ERROR("Failed to listen on socket");
       return false;
   }
   ```

4. 创建监听Channel：
   ```cpp
   listen_channel_ = std::make_unique<Channel>(event_loop_.get(), listen_socket_->fd());
   listen_channel_->setReadCallback([this]() { handleNewConnection(); });
   listen_channel_->enableReading();
   ```

#### 步骤2：EventLoop启动
**执行序列**：
1. 启动EventLoop线程：
   ```cpp
   event_loop_thread_ = std::thread([this]() {
       LOG_INFO("EventLoop thread started");
       event_loop_->loop();
       LOG_INFO("EventLoop thread exited");
   });
   ```

2. 等待服务器就绪：
   ```cpp
   std::this_thread::sleep_for(std::chrono::milliseconds(100));
   if (!isListening()) {
       LOG_ERROR("Server failed to start listening");
       return false;
   }
   ```

3. 记录启动成功日志：
   ```cpp
   LOG_INFO("HttpServer started successfully on " + host_ + ":" + std::to_string(port_));
   running_ = true;
   ```

**结果**：HTTP服务器启动成功，开始监听连接

### 2. 新连接处理流程

**业务场景**：处理客户端的新连接请求

**执行步骤**：

#### 步骤1：新连接接受
```cpp
// 内部回调：HttpServer::handleNewConnection()
handleNewConnection();
```

**内部执行序列**：
1. 接受新连接：
   ```cpp
   while (true) {
       auto client_socket = listen_socket_->accept();
       if (!client_socket) {
           break; // 没有更多连接
       }
       
       // 检查连接数限制
       if (connection_manager_->getActiveConnectionCount() >= max_connections_) {
           LOG_WARNING("Maximum connections reached, rejecting new connection");
           client_socket->close();
           continue;
       }
       
       handleClientConnection(std::move(client_socket));
   }
   ```

#### 步骤2：客户端连接处理
```cpp
// 内部调用：HttpServer::handleClientConnection()
handleClientConnection(std::move(client_socket));
```

**内部执行序列**：
1. 配置客户端Socket：
   ```cpp
   client_socket->setNonBlocking(true);
   client_socket->setTcpNoDelay(true);
   client_socket->setKeepAlive(true);
   ```

2. 创建HTTP会话：
   ```cpp
   auto session = std::make_shared<HttpSession>(
       event_loop_.get(),
       std::move(client_socket),
       [this](const HttpRequest& req, HttpResponse& res) {
           handleHttpRequest(req, res);
       }
   );
   ```

3. 注册会话到连接管理器：
   ```cpp
   connection_manager_->addConnection(session);
   ```

4. 设置会话超时：
   ```cpp
   session->setTimeout(request_timeout_ms_);
   ```

**结果**：新连接处理完成，HTTP会话建立

### 3. HTTP请求路由流程

**业务场景**：将HTTP请求路由到相应的处理器

**执行步骤**：

#### 步骤1：请求路由处理
```cpp
// 内部回调：HttpServer::handleHttpRequest()
handleHttpRequest(req, res);
```

**内部执行序列**：
1. 请求预处理：
   ```cpp
   // 记录请求统计
   request_stats_.total_requests++;
   
   // 设置默认响应头
   res.setHeader("Server", "AuthService/1.0");
   res.setHeader("Date", getCurrentHttpDate());
   ```

2. 中间件处理：
   ```cpp
   middleware_manager_->process(req, res, [this, &req, &res]() {
       // 路由匹配和处理
       auto handler = router_->match(req.getMethod(), req.getPath());
       if (handler) {
           try {
               handler(req, res);
           } catch (const std::exception& e) {
               LOG_ERROR("Request handler error: " + std::string(e.what()));
               res.setStatus(500);
               res.setBody("Internal Server Error");
           }
       } else {
           res.setStatus(404);
           res.setBody("Not Found");
       }
   });
   ```

#### 步骤2：路由匹配
```cpp
// 内部调用：HttpRouter::match()
auto handler = router_->match(req.getMethod(), req.getPath());
```

**内部执行序列**：
1. 方法和路径匹配：
   ```cpp
   std::string method_str = methodToString(req.getMethod());
   auto method_routes = routes_.find(method_str);
   if (method_routes == routes_.end()) {
       return nullptr;
   }
   ```

2. 路径模式匹配：
   ```cpp
   for (const auto& route : method_routes->second) {
       if (route.pattern == req.getPath()) {
           // 精确匹配
           return route.handler;
       } else if (route.is_regex && std::regex_match(req.getPath(), route.regex)) {
           // 正则匹配
           return route.handler;
       }
   }
   ```

3. 参数提取（如果是参数化路由）：
   ```cpp
   // 提取路径参数，如 /users/:id
   extractPathParameters(req, route.pattern);
   ```

**结果**：HTTP请求路由完成，找到对应处理器

## 中间件处理流程

### 1. 中间件链执行流程

**业务场景**：按顺序执行注册的中间件

**执行步骤**：

#### 步骤1：中间件链处理
```cpp
// 调用类：MiddlewareManager
// 使用函数：MiddlewareManager::process()
middleware_manager_->process(req, res, final_handler);
```

**内部执行序列**：
1. `MiddlewareManager::process(req, res, final_handler)`
2. 初始化中间件索引：
   ```cpp
   current_middleware_index_ = 0;
   ```

3. 创建next函数：
   ```cpp
   std::function<void()> next = [this, &req, &res, &final_handler]() {
       if (current_middleware_index_ < middlewares_.size()) {
           auto middleware = middlewares_[current_middleware_index_++];
           middleware(req, res, next);
       } else {
           // 所有中间件执行完毕，执行最终处理器
           final_handler();
       }
   };
   ```

4. 开始执行中间件链：
   ```cpp
   next();
   ```

#### 步骤2：单个中间件执行
**执行序列**：
1. 中间件前置处理：
   ```cpp
   // 例如：认证中间件
   auto auth_middleware = [](const HttpRequest& req, HttpResponse& res, std::function<void()> next) {
       std::string auth_header = req.getHeader("Authorization");
       if (auth_header.empty()) {
           res.setStatus(401);
           res.setBody("Unauthorized");
           return; // 不调用next()，中断处理链
       }
       
       // 验证令牌
       if (!validateToken(auth_header)) {
           res.setStatus(401);
           res.setBody("Invalid token");
           return;
       }
       
       // 验证通过，继续下一个中间件
       next();
   };
   ```

2. 中间件后置处理：
   ```cpp
   // 例如：响应时间中间件
   auto timing_middleware = [](const HttpRequest& req, HttpResponse& res, std::function<void()> next) {
       auto start_time = std::chrono::high_resolution_clock::now();
       
       next(); // 执行后续处理
       
       auto end_time = std::chrono::high_resolution_clock::now();
       auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
       res.setHeader("X-Response-Time", std::to_string(duration.count()) + "ms");
   };
   ```

**结果**：中间件链执行完成

### 2. 认证中间件流程

**业务场景**：验证请求的身份认证信息

**执行步骤**：

#### 步骤1：令牌提取和验证
```cpp
// 认证中间件实现
auto auth_middleware = [this](const HttpRequest& req, HttpResponse& res, std::function<void()> next) {
    // 检查是否需要认证
    if (isPublicEndpoint(req.getPath())) {
        next();
        return;
    }
    
    // 提取Authorization头
    std::string auth_header = req.getHeader("Authorization");
    if (auth_header.length() < 7 || auth_header.substr(0, 7) != "Bearer ") {
        res.setStatus(401);
        res.setBody("{\"error\":\"Missing or invalid authorization header\"}");
        res.setHeader("Content-Type", "application/json");
        return;
    }
    
    std::string token = auth_header.substr(7);
    
    // 验证令牌
    auto verification_result = jwt_manager_->verifyAccessToken(token);
    if (!verification_result.success) {
        res.setStatus(401);
        res.setBody("{\"error\":\"" + verification_result.error_message + "\"}");
        res.setHeader("Content-Type", "application/json");
        return;
    }
    
    // 将用户信息添加到请求上下文
    req.setUserContext(verification_result.user_id, verification_result.username);
    
    next();
};
```

**结果**：认证中间件处理完成

## 响应生成和发送流程

### 1. HTTP响应构建流程

**业务场景**：构建HTTP响应并发送给客户端

**执行步骤**：

#### 步骤1：响应数据准备
```cpp
// 在路由处理器中
auto handler = [](const HttpRequest& req, HttpResponse& res) {
    // 处理业务逻辑
    auto result = processBusinessLogic(req);
    
    // 设置响应状态
    res.setStatus(200);
    
    // 设置响应头
    res.setHeader("Content-Type", "application/json");
    res.setHeader("Cache-Control", "no-cache");
    
    // 设置响应体
    res.setBody(result.toJson());
};
```

#### 步骤2：响应发送
```cpp
// 内部调用：HttpSession::sendResponse()
session->sendResponse(res);
```

**内部执行序列**：
1. 构建HTTP响应字符串：
   ```cpp
   std::string response_str = buildHttpResponse(res);
   // 格式：HTTP/1.1 200 OK\r\nContent-Type: application/json\r\n\r\n{...}
   ```

2. 异步发送响应：
   ```cpp
   auto send_buffer = std::make_shared<std::string>(std::move(response_str));
   
   socket_->asyncWrite(send_buffer->data(), send_buffer->size(),
       [this, send_buffer](ssize_t bytes_written, int error_code) {
           if (error_code == 0 && bytes_written > 0) {
               LOG_DEBUG("Response sent: " + std::to_string(bytes_written) + " bytes");
               
               // 检查是否保持连接
               if (shouldKeepAlive()) {
                   resetForNextRequest();
               } else {
                   closeConnection();
               }
           } else {
               LOG_ERROR("Failed to send response: " + std::string(strerror(error_code)));
               closeConnection();
           }
       });
   ```

#### 步骤3：连接状态管理
**执行序列**：
1. Keep-Alive检查：
   ```cpp
   bool shouldKeepAlive() const {
       std::string connection = request_.getHeader("Connection");
       return (connection == "keep-alive" || 
               (request_.getHttpVersion() == "HTTP/1.1" && connection != "close"));
   }
   ```

2. 连接重置或关闭：
   ```cpp
   if (shouldKeepAlive()) {
       // 重置会话状态，准备下一个请求
       input_buffer_.clear();
       request_.reset();
       response_.reset();
       
       // 重新启用读事件
       channel_->enableReading();
   } else {
       // 关闭连接
       closeConnection();
   }
   ```

**结果**：HTTP响应发送完成

### 2. 错误响应处理流程

**业务场景**：处理和发送错误响应

**执行步骤**：

#### 步骤1：错误响应生成
```cpp
// 内部调用：HttpServer::sendErrorResponse()
sendErrorResponse(res, 500, "Internal Server Error", error_details);
```

**内部执行序列**：
1. 设置错误状态和头：
   ```cpp
   res.setStatus(status_code);
   res.setHeader("Content-Type", "application/json");
   res.setHeader("Cache-Control", "no-cache");
   ```

2. 构建错误响应体：
   ```cpp
   nlohmann::json error_response;
   error_response["error"] = {
       {"code", status_code},
       {"message", error_message},
       {"timestamp", getCurrentTimestamp()},
       {"path", req.getPath()}
   };
   
   if (!error_details.empty()) {
       error_response["error"]["details"] = error_details;
   }
   
   res.setBody(error_response.dump());
   ```

3. 记录错误日志：
   ```cpp
   LOG_ERROR("HTTP Error " + std::to_string(status_code) + " for " + 
            req.getMethod() + " " + req.getPath() + ": " + error_message);
   ```

**结果**：错误响应处理完成

## 连接管理和生命周期

### 1. 连接生命周期管理流程

**业务场景**：管理HTTP连接的完整生命周期

**执行步骤**：

#### 步骤1：连接创建和注册
```cpp
// 内部调用：ConnectionManager::addConnection()
connection_manager_->addConnection(session);
```

**内部执行序列**：
1. 分配连接ID：
   ```cpp
   uint64_t connection_id = next_connection_id_++;
   session->setConnectionId(connection_id);
   ```

2. 注册到连接池：
   ```cpp
   std::lock_guard<std::mutex> lock(connections_mutex_);
   active_connections_[connection_id] = session;
   connection_stats_.total_connections++;
   connection_stats_.active_connections++;
   ```

3. 设置连接超时：
   ```cpp
   session->setIdleTimeout(keep_alive_timeout_ms_);
   ```

#### 步骤2：连接监控和清理
```cpp
// 定期调用：ConnectionManager::cleanupIdleConnections()
cleanupIdleConnections();
```

**内部执行序列**：
1. 扫描空闲连接：
   ```cpp
   auto now = std::chrono::steady_clock::now();
   std::vector<uint64_t> idle_connections;
   
   std::lock_guard<std::mutex> lock(connections_mutex_);
   for (const auto& [id, session] : active_connections_) {
       if (session->getIdleTime() > keep_alive_timeout_ms_) {
           idle_connections.push_back(id);
       }
   }
   ```

2. 关闭空闲连接：
   ```cpp
   for (uint64_t id : idle_connections) {
       auto it = active_connections_.find(id);
       if (it != active_connections_.end()) {
           it->second->close();
           active_connections_.erase(it);
           connection_stats_.timeout_count++;
       }
   }
   ```

**结果**：连接生命周期管理完成

### 2. 连接池管理流程

**业务场景**：管理HTTP连接池的状态和性能

**执行步骤**：

#### 步骤1：连接池状态监控
```cpp
// 调用类：HttpServer
// 使用函数：ConnectionManager::getConnectionStats()
auto stats = connection_manager_->getConnectionStats();
```

**内部执行序列**：
1. 收集连接统计：
   ```cpp
   ConnectionStats stats;
   std::lock_guard<std::mutex> lock(connections_mutex_);
   
   stats.active_connections = active_connections_.size();
   stats.total_connections = connection_stats_.total_connections;
   stats.total_requests = connection_stats_.total_requests;
   stats.total_responses = connection_stats_.total_responses;
   stats.error_count = connection_stats_.error_count;
   stats.timeout_count = connection_stats_.timeout_count;
   ```

2. 计算性能指标：
   ```cpp
   auto uptime = std::chrono::duration_cast<std::chrono::seconds>(
       std::chrono::steady_clock::now() - start_time_
   ).count();
   
   stats.uptime_seconds = uptime;
   stats.requests_per_second = uptime > 0 ? stats.total_requests / uptime : 0;
   ```

**结果**：连接池状态监控完成

## 错误处理和监控流程

### 1. 错误处理流程

**业务场景**：处理HTTP服务器运行时的各种错误

**执行步骤**：

#### 步骤1：异常捕获和处理
```cpp
// 在各个关键点进行异常处理
try {
    // 执行HTTP处理逻辑
    handleHttpRequest(req, res);
} catch (const std::exception& e) {
    LOG_ERROR("HTTP request processing error: " + std::string(e.what()));
    
    if (!res.isHeadersSent()) {
        res.setStatus(500);
        res.setHeader("Content-Type", "application/json");
        res.setBody("{\"error\":\"Internal Server Error\"}");
    }
    
    // 更新错误统计
    request_stats_.error_count++;
} catch (...) {
    LOG_ERROR("Unknown error in HTTP request processing");
    
    if (!res.isHeadersSent()) {
        res.setStatus(500);
        res.setBody("Internal Server Error");
    }
    
    request_stats_.error_count++;
}
```

#### 步骤2：网络错误处理
**执行序列**：
1. Socket错误处理：
   ```cpp
   void handleSocketError(int error_code) {
       switch (error_code) {
           case ECONNRESET:
               LOG_DEBUG("Connection reset by peer");
               break;
           case ETIMEDOUT:
               LOG_DEBUG("Connection timed out");
               connection_stats_.timeout_count++;
               break;
           case EPIPE:
               LOG_DEBUG("Broken pipe");
               break;
           default:
               LOG_ERROR("Socket error: " + std::string(strerror(error_code)));
               break;
       }
       
       closeConnection();
   }
   ```

**结果**：错误处理完成

### 2. 性能监控流程

**业务场景**：监控HTTP服务器的性能指标

**执行步骤**：

#### 步骤1：性能指标收集
```cpp
// 定期调用：HttpServer::collectPerformanceMetrics()
collectPerformanceMetrics();
```

**内部执行序列**：
1. 收集请求性能：
   ```cpp
   PerformanceMetrics metrics;
   metrics.requests_per_second = calculateRequestsPerSecond();
   metrics.average_response_time = calculateAverageResponseTime();
   metrics.memory_usage = getCurrentMemoryUsage();
   metrics.cpu_usage = getCurrentCpuUsage();
   ```

2. 收集连接性能：
   ```cpp
   metrics.active_connections = connection_manager_->getActiveConnectionCount();
   metrics.connection_pool_utilization = calculateConnectionPoolUtilization();
   ```

#### 步骤2：健康检查
```cpp
// 调用类：HttpServer
// 使用函数：HttpServer::healthCheck()
bool healthy = http_server_->healthCheck();
```

**内部执行序列**：
1. 检查服务器状态：
   ```cpp
   bool healthy = true;
   
   // 检查监听状态
   if (!isListening()) {
       healthy = false;
   }
   
   // 检查EventLoop状态
   if (!event_loop_->isRunning()) {
       healthy = false;
   }
   
   // 检查线程池状态
   if (!thread_pool_->isHealthy()) {
       healthy = false;
   }
   
   // 检查连接池状态
   if (connection_manager_->getActiveConnectionCount() >= max_connections_) {
       healthy = false;
   }
   ```

**结果**：性能监控和健康检查完成

## 总结

HttpServer系统的业务流程体现了现代HTTP服务器的核心特性：

1. **高性能网络**：基于EventLoop的异步I/O处理
2. **灵活路由**：支持精确匹配和正则表达式的路由系统
3. **中间件架构**：可扩展的中间件处理链
4. **连接管理**：完善的连接生命周期和池管理
5. **错误处理**：全面的异常处理和错误恢复机制
6. **性能监控**：详细的性能指标收集和健康检查
7. **安全特性**：内置的安全头和CORS支持
8. **可扩展性**：支持高并发和大量连接

每个业务流程都经过精心设计，确保HTTP服务器的高性能、可靠性和可维护性。
