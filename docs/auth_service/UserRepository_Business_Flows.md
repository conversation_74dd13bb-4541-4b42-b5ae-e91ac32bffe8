# UserRepository 业务流程详细分析

## 目录
1. [用户仓库初始化流程](#用户仓库初始化流程)
2. [用户CRUD操作流程](#用户CRUD操作流程)
3. [用户查询和验证流程](#用户查询和验证流程)
4. [游戏数据管理流程](#游戏数据管理流程)
5. [令牌管理流程](#令牌管理流程)
6. [数据库连接和事务管理](#数据库连接和事务管理)

## 用户仓库初始化流程

### 1. 用户仓库创建和数据库连接流程

**业务场景**：创建用户仓库实例并建立数据库连接

**执行步骤**：

#### 步骤1：仓库实例创建
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::UserRepository()
user_repository_ = std::make_unique<UserRepository>(config.database_config);
```

**内部执行序列**：
1. `UserRepository::UserRepository(database_config)` - 构造函数
2. 保存数据库配置：`config_ = database_config`
3. 初始化连接池指针：
   - `mysql_pool_ = nullptr` - MySQL连接池
   - `redis_pool_ = nullptr` - Redis连接池
4. 初始化状态标志：`initialized_ = false`

#### 步骤2：数据库连接池初始化
```cpp
// 调用类：UserRepository
// 使用函数：UserRepository::initialize()
bool success = user_repository_->initialize();
```

**内部执行序列**：
1. `UserRepository::initialize()`
2. 创建MySQL连接池：
   ```cpp
   mysql_pool_ = std::make_shared<MySQLPool>(
       config_.mysql_host,
       config_.mysql_port,
       config_.mysql_database,
       config_.mysql_username,
       config_.mysql_password,
       config_.mysql_pool_size
   );
   ```

3. 初始化MySQL连接池：
   ```cpp
   if (!mysql_pool_->initialize()) {
       LOG_ERROR("Failed to initialize MySQL connection pool");
       return false;
   }
   ```

4. 创建Redis连接池：
   ```cpp
   redis_pool_ = std::make_shared<RedisPool>(
       config_.redis_host,
       config_.redis_port,
       config_.redis_password,
       config_.redis_pool_size
   );
   ```

5. 初始化Redis连接池：
   ```cpp
   if (!redis_pool_->initialize()) {
       LOG_ERROR("Failed to initialize Redis connection pool");
       return false;
   }
   ```

6. 验证数据库表结构：`validateTableStructure()`
7. 设置初始化标志：`initialized_ = true`
8. 记录初始化日志：`LOG_INFO("UserRepository initialized successfully")`

**结果**：用户仓库初始化完成，数据库连接建立

### 2. 表结构验证流程

**业务场景**：验证数据库表结构是否正确

**执行步骤**：

#### 步骤1：表存在性检查
**执行序列**：
1. 检查用户表：`checkTableExists("users")`
2. 检查游戏数据表：`checkTableExists("game_user_data")`
3. 检查刷新令牌表：`checkTableExists("refresh_tokens")`
4. 检查游戏会话表：`checkTableExists("game_sessions")`

#### 步骤2：表结构验证
**执行序列**：
1. 验证用户表字段：
   ```sql
   DESCRIBE users;
   -- 验证必需字段：user_id, username, email, password_hash, nickname, created_at, updated_at, is_active
   ```

2. 验证索引存在：
   ```sql
   SHOW INDEX FROM users WHERE Key_name IN ('idx_username', 'idx_email');
   ```

3. 验证外键约束：检查表间关系的完整性

**结果**：数据库表结构验证完成

## 用户CRUD操作流程

### 1. 用户创建流程

**业务场景**：在数据库中创建新用户记录

**执行步骤**：

#### 步骤1：用户创建请求
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::create()
auto created_user = user_repository_->create(user_info);
```

**内部执行序列**：
1. `UserRepository::create(user_info)`
2. 验证用户信息：
   ```cpp
   if (user_info.username.empty() && user_info.email.empty()) {
       throw std::invalid_argument("Username or email is required");
   }
   if (user_info.password_hash.empty()) {
       throw std::invalid_argument("Password hash is required");
   }
   ```

3. 检查唯一性约束：
   ```cpp
   if (!user_info.username.empty() && existsByUsername(user_info.username)) {
       throw std::runtime_error("Username already exists");
   }
   if (!user_info.email.empty() && existsByEmail(user_info.email)) {
       throw std::runtime_error("Email already exists");
   }
   ```

#### 步骤2：数据库插入操作
**执行序列**：
1. 获取数据库连接：`auto connection = mysql_pool_->getConnection()`
2. 准备SQL语句：
   ```sql
   INSERT INTO users (username, email, password_hash, nickname, created_at, updated_at, is_active)
   VALUES (?, ?, ?, ?, NOW(), NOW(), 1)
   ```

3. 绑定参数并执行：
   ```cpp
   auto stmt = connection->prepareStatement(sql);
   stmt->setString(1, user_info.username);
   stmt->setString(2, user_info.email);
   stmt->setString(3, user_info.password_hash);
   stmt->setString(4, user_info.nickname);
   stmt->executeUpdate();
   ```

4. 获取生成的用户ID：
   ```cpp
   auto result = connection->createStatement()->executeQuery("SELECT LAST_INSERT_ID()");
   if (result->next()) {
       user_info.user_id = result->getUInt64(1);
   }
   ```

#### 步骤3：缓存更新和日志记录
**执行序列**：
1. 更新Redis缓存：
   ```cpp
   std::string cache_key = "user:" + std::to_string(user_info.user_id);
   redis_pool_->setex(cache_key, user_info.toJson(), 3600); // 1小时缓存
   ```

2. 记录创建日志：`LOG_INFO("User created: " + user_info.username + " (ID: " + std::to_string(user_info.user_id) + ")")`
3. 返回创建的用户信息：`return user_info`

**结果**：用户创建成功，返回完整用户信息

### 2. 用户更新流程

**业务场景**：更新现有用户的信息

**执行步骤**：

#### 步骤1：用户更新请求
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::update()
bool success = user_repository_->update(user_id, update_info);
```

**内部执行序列**：
1. `UserRepository::update(user_id, update_info)`
2. 验证用户存在：
   ```cpp
   auto existing_user = findById(user_id);
   if (!existing_user.has_value()) {
       throw std::runtime_error("User not found");
   }
   ```

3. 验证更新数据：
   ```cpp
   if (!update_info.email.empty() && !isValidEmail(update_info.email)) {
       throw std::invalid_argument("Invalid email format");
   }
   ```

#### 步骤2：数据库更新操作
**执行序列**：
1. 构建动态SQL语句：
   ```cpp
   std::string sql = "UPDATE users SET updated_at = NOW()";
   std::vector<std::string> params;
   
   if (!update_info.email.empty()) {
       sql += ", email = ?";
       params.push_back(update_info.email);
   }
   if (!update_info.nickname.empty()) {
       sql += ", nickname = ?";
       params.push_back(update_info.nickname);
   }
   sql += " WHERE user_id = ?";
   params.push_back(std::to_string(user_id));
   ```

2. 执行更新：
   ```cpp
   auto connection = mysql_pool_->getConnection();
   auto stmt = connection->prepareStatement(sql);
   for (size_t i = 0; i < params.size(); ++i) {
       stmt->setString(i + 1, params[i]);
   }
   int affected_rows = stmt->executeUpdate();
   ```

#### 步骤3：缓存失效和日志记录
**执行序列**：
1. 清除Redis缓存：
   ```cpp
   std::string cache_key = "user:" + std::to_string(user_id);
   redis_pool_->del(cache_key);
   ```

2. 记录更新日志：`LOG_INFO("User updated: " + std::to_string(user_id))`
3. 返回更新结果：`return affected_rows > 0`

**结果**：用户信息更新成功

### 3. 用户删除流程

**业务场景**：软删除或硬删除用户记录

**执行步骤**：

#### 步骤1：软删除操作
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::softDelete()
bool success = user_repository_->softDelete(user_id);
```

**内部执行序列**：
1. 更新用户状态为非活跃：
   ```sql
   UPDATE users SET is_active = 0, updated_at = NOW() WHERE user_id = ?
   ```

2. 撤销用户所有令牌：`revokeAllUserTokens(user_id)`
3. 清除缓存：`redis_pool_->del("user:" + std::to_string(user_id))`

#### 步骤2：硬删除操作（管理员功能）
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::hardDelete()
bool success = user_repository_->hardDelete(user_id);
```

**内部执行序列**：
1. 开启事务：`connection->setAutoCommit(false)`
2. 删除关联数据：
   ```sql
   DELETE FROM game_user_data WHERE user_id = ?;
   DELETE FROM refresh_tokens WHERE user_id = ?;
   DELETE FROM game_sessions WHERE user_id = ?;
   DELETE FROM users WHERE user_id = ?;
   ```

3. 提交事务：`connection->commit()`
4. 清除所有相关缓存

**结果**：用户删除操作完成

## 用户查询和验证流程

### 1. 用户查询流程

**业务场景**：根据不同条件查询用户信息

**执行步骤**：

#### 步骤1：按ID查询用户
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::findById()
auto user_opt = user_repository_->findById(user_id);
```

**内部执行序列**：
1. `UserRepository::findById(user_id)`
2. 检查Redis缓存：
   ```cpp
   std::string cache_key = "user:" + std::to_string(user_id);
   auto cached_data = redis_pool_->get(cache_key);
   if (!cached_data.empty()) {
       return UserInfo::fromJson(nlohmann::json::parse(cached_data));
   }
   ```

3. 数据库查询：
   ```sql
   SELECT user_id, username, email, password_hash, nickname, created_at, updated_at, is_active
   FROM users WHERE user_id = ? AND is_active = 1
   ```

4. 结果处理：
   ```cpp
   auto connection = mysql_pool_->getConnection();
   auto stmt = connection->prepareStatement(sql);
   stmt->setUInt64(1, user_id);
   auto result = stmt->executeQuery();
   
   if (result->next()) {
       UserInfo user_info = mapResultToUserInfo(result);
       // 更新缓存
       redis_pool_->setex(cache_key, user_info.toJson(), 3600);
       return user_info;
   }
   return std::nullopt;
   ```

#### 步骤2：按用户名查询
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::findByUsername()
auto user_opt = user_repository_->findByUsername(username);
```

**内部执行序列**：
1. 检查用户名缓存：
   ```cpp
   std::string username_cache_key = "username:" + username;
   auto user_id_str = redis_pool_->get(username_cache_key);
   if (!user_id_str.empty()) {
       return findById(std::stoull(user_id_str));
   }
   ```

2. 数据库查询：
   ```sql
   SELECT user_id, username, email, password_hash, nickname, created_at, updated_at, is_active
   FROM users WHERE username = ? AND is_active = 1
   ```

3. 缓存用户名到ID的映射：
   ```cpp
   if (user_info.has_value()) {
       redis_pool_->setex(username_cache_key, std::to_string(user_info->user_id), 3600);
   }
   ```

#### 步骤3：按邮箱查询
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::findByEmail()
auto user_opt = user_repository_->findByEmail(email);
```

**内部执行序列**：
1. 类似用户名查询，使用邮箱作为查询条件
2. 缓存邮箱到用户ID的映射
3. 返回查询结果

**结果**：用户查询完成，返回用户信息或空值

### 2. 用户存在性验证流程

**业务场景**：验证用户名或邮箱是否已存在

**执行步骤**：

#### 步骤1：用户名存在性检查
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::existsByUsername()
bool exists = user_repository_->existsByUsername(username);
```

**内部执行序列**：
1. `UserRepository::existsByUsername(username)`
2. 快速缓存检查：
   ```cpp
   std::string cache_key = "exists:username:" + username;
   auto cached_result = redis_pool_->get(cache_key);
   if (!cached_result.empty()) {
       return cached_result == "1";
   }
   ```

3. 数据库查询：
   ```sql
   SELECT COUNT(*) FROM users WHERE username = ? AND is_active = 1
   ```

4. 缓存结果：
   ```cpp
   bool exists = count > 0;
   redis_pool_->setex(cache_key, exists ? "1" : "0", 300); // 5分钟缓存
   return exists;
   ```

#### 步骤2：邮箱存在性检查
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::existsByEmail()
bool exists = user_repository_->existsByEmail(email);
```

**内部执行序列**：
1. 类似用户名存在性检查
2. 使用邮箱作为查询条件
3. 缓存查询结果

**结果**：返回用户名或邮箱是否存在

## 游戏数据管理流程

### 1. 游戏用户数据查询流程

**业务场景**：查询用户的游戏相关数据

**执行步骤**：

#### 步骤1：游戏数据查询
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::findGameUserData()
auto game_data_opt = user_repository_->findGameUserData(user_id, game_type);
```

**内部执行序列**：
1. `UserRepository::findGameUserData(user_id, game_type)`
2. 检查游戏数据缓存：
   ```cpp
   std::string cache_key = "game_data:" + std::to_string(user_id) + ":" + std::to_string(static_cast<int>(game_type));
   auto cached_data = redis_pool_->get(cache_key);
   if (!cached_data.empty()) {
       return GameUserData::fromJson(nlohmann::json::parse(cached_data));
   }
   ```

3. 数据库查询：
   ```sql
   SELECT user_id, game_type, level, experience, coins, gems, total_games, wins, losses, draws, best_score, created_at, updated_at
   FROM game_user_data WHERE user_id = ? AND game_type = ?
   ```

4. 结果处理和缓存：
   ```cpp
   if (result->next()) {
       GameUserData game_data = mapResultToGameUserData(result);
       redis_pool_->setex(cache_key, game_data.toJson(), 1800); // 30分钟缓存
       return game_data;
   }
   return std::nullopt;
   ```

#### 步骤2：创建默认游戏数据
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::createGameUserData()
auto game_data = user_repository_->createGameUserData(user_id, game_type);
```

**内部执行序列**：
1. 创建默认游戏数据：
   ```cpp
   GameUserData default_data;
   default_data.user_id = user_id;
   default_data.game_type = game_type;
   default_data.level = 1;
   default_data.experience = 0;
   default_data.coins = 100; // 初始金币
   default_data.gems = 0;
   default_data.total_games = 0;
   default_data.wins = 0;
   default_data.losses = 0;
   default_data.draws = 0;
   default_data.best_score = 0;
   ```

2. 插入数据库：
   ```sql
   INSERT INTO game_user_data (user_id, game_type, level, experience, coins, gems, total_games, wins, losses, draws, best_score, created_at, updated_at)
   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
   ```

3. 更新缓存：缓存新创建的游戏数据

**结果**：游戏数据查询或创建完成

### 2. 游戏数据更新流程

**业务场景**：更新用户的游戏数据

**执行步骤**：

#### 步骤1：游戏数据更新
```cpp
// 调用类：AuthService
// 使用函数：UserRepository::updateGameUserData()
bool success = user_repository_->updateGameUserData(user_id, game_data);
```

**内部执行序列**：
1. 验证数据合法性：
   ```cpp
   if (game_data.level < 1 || game_data.experience < 0 || game_data.coins < 0) {
       throw std::invalid_argument("Invalid game data values");
   }
   ```

2. 数据库更新：
   ```sql
   UPDATE game_user_data SET 
       level = ?, experience = ?, coins = ?, gems = ?, 
       total_games = ?, wins = ?, losses = ?, draws = ?, best_score = ?, 
       updated_at = NOW()
   WHERE user_id = ? AND game_type = ?
   ```

3. 缓存更新：
   ```cpp
   std::string cache_key = "game_data:" + std::to_string(user_id) + ":" + std::to_string(static_cast<int>(game_data.game_type));
   redis_pool_->setex(cache_key, game_data.toJson(), 1800);
   ```

**结果**：游戏数据更新成功

## 令牌管理流程

### 1. 刷新令牌管理流程

**业务场景**：管理用户的刷新令牌

**执行步骤**：

#### 步骤1：保存刷新令牌
```cpp
// 调用类：JWTManager
// 使用函数：UserRepository::saveRefreshToken()
bool success = user_repository_->saveRefreshToken(token_info);
```

**内部执行序列**：
1. 插入刷新令牌记录：
   ```sql
   INSERT INTO refresh_tokens (jti, user_id, expires_at, is_active, created_at)
   VALUES (?, ?, ?, 1, NOW())
   ```

2. 清理用户旧令牌（可选）：
   ```sql
   UPDATE refresh_tokens SET is_active = 0 
   WHERE user_id = ? AND jti != ? AND is_active = 1
   ```

#### 步骤2：查找刷新令牌
```cpp
// 调用类：JWTManager
// 使用函数：UserRepository::findRefreshToken()
auto token_opt = user_repository_->findRefreshToken(jti);
```

**内部执行序列**：
1. 数据库查询：
   ```sql
   SELECT jti, user_id, expires_at, is_active, created_at
   FROM refresh_tokens WHERE jti = ? AND is_active = 1
   ```

2. 验证令牌有效性：检查过期时间和活跃状态

#### 步骤3：撤销刷新令牌
```cpp
// 调用类：JWTManager
// 使用函数：UserRepository::revokeRefreshToken()
bool success = user_repository_->revokeRefreshToken(jti);
```

**内部执行序列**：
1. 更新令牌状态：
   ```sql
   UPDATE refresh_tokens SET is_active = 0 WHERE jti = ?
   ```

2. 记录撤销日志

**结果**：刷新令牌管理完成

## 数据库连接和事务管理

### 1. 连接池管理流程

**业务场景**：管理数据库连接池的生命周期

**执行步骤**：

#### 步骤1：连接获取
```cpp
// 内部使用
auto connection = mysql_pool_->getConnection();
```

**内部执行序列**：
1. 从连接池获取可用连接
2. 检查连接有效性
3. 重置连接状态（自动提交等）
4. 返回连接对象

#### 步骤2：连接释放
**执行序列**：
1. 连接自动释放（RAII）
2. 重置连接状态
3. 返回连接池

#### 步骤3：连接池健康检查
```cpp
// 调用类：UserRepository
// 使用函数：UserRepository::healthCheck()
bool healthy = user_repository_->healthCheck();
```

**内部执行序列**：
1. 检查MySQL连接池状态：`mysql_pool_->healthCheck()`
2. 检查Redis连接池状态：`redis_pool_->healthCheck()`
3. 执行简单查询验证连接：`SELECT 1`
4. 返回健康状态

**结果**：数据库连接池正常运行

### 2. 事务管理流程

**业务场景**：管理复杂操作的事务一致性

**执行步骤**：

#### 步骤1：事务开启
```cpp
// 内部使用
auto connection = mysql_pool_->getConnection();
connection->setAutoCommit(false);
```

#### 步骤2：事务操作
**执行序列**：
1. 执行多个相关的数据库操作
2. 检查每个操作的结果
3. 如果任何操作失败，准备回滚

#### 步骤3：事务提交或回滚
```cpp
try {
    // 执行操作
    connection->commit();
    LOG_INFO("Transaction committed successfully");
} catch (const std::exception& e) {
    connection->rollback();
    LOG_ERROR("Transaction rolled back: " + std::string(e.what()));
    throw;
}
```

**结果**：事务一致性得到保障

## 总结

UserRepository系统的业务流程体现了现代数据访问层的核心特性：

1. **数据持久化**：完整的用户和游戏数据CRUD操作
2. **缓存优化**：Redis缓存提高查询性能
3. **连接管理**：高效的数据库连接池管理
4. **事务支持**：确保数据一致性的事务管理
5. **查询优化**：多种查询方式和索引优化
6. **数据验证**：完整的数据验证和约束检查
7. **错误处理**：完善的异常处理和错误恢复
8. **监控支持**：健康检查和性能监控

每个业务流程都经过精心设计，确保数据的一致性、完整性和高性能访问。
