# Auth Service Postman 测试指南

## 📋 概述

本文档提供了使用Postman测试Auth Service的完整指南，包括所有API端点的测试示例、请求格式、响应格式和测试场景。

## 🚀 快速开始

### 环境配置

1. **服务地址**: `http://localhost:8001` (根据配置文件中的端口)
2. **Content-Type**: `application/json`
3. **Authorization**: `Bearer {access_token}` (需要认证的接口)

### Postman环境变量设置

```json
{
  "auth_service_url": "http://localhost:8001",
  "access_token": "",
  "refresh_token": "",
  "user_id": "",
  "session_id": ""
}
```

### JWT令牌详细说明

Auth Service使用JWT (JSON Web Token) 进行身份认证，具体配置如下：

**JWT配置信息**:
- **算法**: HS256 (HMAC SHA-256)
- **发行者 (issuer)**: `game-microservices-auth`
- **受众 (audience)**: `game-microservices-clients`
- **访问令牌有效期**: 3600秒 (1小时)
- **刷新令牌有效期**: 604800秒 (7天)
- **密钥**: `game_microservices_jwt_secret_key_2025_very_secure`

**JWT令牌结构**:
```
Header: {
  "alg": "HS256",
  "typ": "JWT"
}

Payload: {
  "iss": "game-microservices-auth",
  "aud": "game-microservices-clients",
  "sub": "用户ID",
  "iat": 签发时间,
  "exp": 过期时间,
  "nbf": 生效时间,
  "jti": "令牌唯一标识",
  "username": "用户名",
  "email": "邮箱地址",
  "roles": ["用户角色"],
  "token_type": "access" | "refresh",
  "session_id": "会话ID",
  "device_type": "设备类型"
}
```

**Authorization头部格式**:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature_here
```

### 🔑 接口认证要求

**无需认证的接口**:
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `GET /health` - 健康检查

**需要认证的接口**:
- `GET /api/v1/auth/validate` - 令牌验证
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/game/login` - 游戏登录
- `GET /api/v1/game/servers` - 获取游戏服务器列表
- `GET /api/v1/admin/status` - 服务状态
- `GET /api/v1/admin/statistics` - 统计信息

**认证方式**:
所有需要认证的接口都必须在请求头中包含有效的JWT访问令牌：
```
Authorization: Bearer {access_token}
```

**令牌获取方式**:
1. 通过用户注册接口获取
2. 通过用户登录接口获取
3. 通过刷新令牌接口更新

## 🔐 认证相关接口测试

### 1. 用户注册

**接口**: `POST {{auth_service_url}}/api/v1/auth/register`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "username": "testuser001",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "nickname": "测试用户001",
  "device_type": "web",
  "device_id": "web_browser_001"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "user_info": {
      "user_id": 1,
      "username": "testuser001",
      "email": "<EMAIL>",
      "nickname": "测试用户001",
      "status": 1,
      "online_status": 1,
      "created_at": "2025-07-21T10:30:00Z"
    },
    "session": {
      "session_id": "sess_abc123def456",
      "device_type": "web",
      "created_at": "2025-07-21T10:30:00Z",
      "expires_at": "2025-07-21T18:30:00Z"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2025-07-21T10:30:00Z",
  "service": "auth_service"
}
```

**测试脚本**:
```javascript
// 保存令牌到环境变量
if (pm.response.code === 200) {
    const response = pm.response.json();
    if (response.success) {
        pm.environment.set("access_token", response.data.access_token);
        pm.environment.set("refresh_token", response.data.refresh_token);
        pm.environment.set("user_id", response.data.user_info.user_id);
        pm.environment.set("session_id", response.data.session.session_id);
    }
}
```

### 2. 用户登录

**接口**: `POST {{auth_service_url}}/api/v1/auth/login`

**请求体**:
```json
{
  "username": "testuser001",
  "password": "SecurePassword123!",
  "device_type": "web",
  "device_id": "web_browser_001"
}
```

**或使用邮箱登录**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "device_type": "mobile",
  "device_id": "mobile_app_001"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "user_info": {
      "user_id": 1,
      "username": "testuser001",
      "email": "<EMAIL>",
      "nickname": "测试用户001",
      "status": 1,
      "online_status": 1,
      "last_login_at": "2025-07-21T10:35:00Z"
    },
    "session": {
      "session_id": "sess_xyz789abc123",
      "device_type": "web",
      "created_at": "2025-07-21T10:35:00Z",
      "expires_at": "2025-07-21T18:35:00Z"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2025-07-21T10:35:00Z",
  "service": "auth_service"
}
```

### 3. 令牌验证

**接口**: `GET {{auth_service_url}}/api/v1/auth/validate`

**请求头**:
```
Authorization: Bearer {{access_token}}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user_id": 1,
    "username": "testuser001",
    "session_id": "sess_xyz789abc123",
    "expires_at": "2025-07-21T18:35:00Z",
    "remaining_seconds": 28800
  },
  "timestamp": "2025-07-21T10:35:00Z",
  "service": "auth_service"
}
```

### 4. 刷新令牌

**接口**: `POST {{auth_service_url}}/api/v1/auth/refresh`

**请求体**:
```json
{
  "refresh_token": "{{refresh_token}}"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-07-21T19:00:00Z"
  },
  "timestamp": "2025-07-21T11:00:00Z",
  "service": "auth_service"
}
```

### 5. 用户登出

**接口**: `POST {{auth_service_url}}/api/v1/auth/logout`

**请求头**:
```
Authorization: Bearer {{access_token}}
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "message": "登出成功"
  },
  "timestamp": "2025-07-21T11:30:00Z",
  "service": "auth_service"
}
```

## 🎮 游戏相关接口测试

### 1. 游戏登录

**接口**: `POST {{auth_service_url}}/api/v1/game/login`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {{access_token}}
```

**请求体**:
```json
{
  "game_type": 1,
  "preferred_region": "asia-east"
}
```

**游戏类型说明**:
- 1: 贪吃蛇 (SNAKE)
- 2: 俄罗斯方块 (TETRIS)
- 3: 象棋 (CHESS)
- 4: 扑克 (POKER)
- 5: RPG
- 6: MOBA
- 7: FPS
- 8: 策略 (STRATEGY)

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "user_info": {
      "user_id": 1,
      "username": "testuser001",
      "nickname": "测试用户001"
    },
    "game_data": {
      "user_id": 1,
      "game_type": 1,
      "level": 5,
      "experience": 1250,
      "coins": 500,
      "gems": 10,
      "total_games": 25,
      "wins": 15,
      "losses": 8,
      "draws": 2,
      "best_score": 9850
    },
    "server_info": {
      "server_id": "snake-server-001",
      "server_name": "贪吃蛇服务器1",
      "host": "game1.example.com",
      "port": 9001,
      "region": "asia-east",
      "current_players": 45,
      "max_players": 100
    },
    "game_session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2025-07-21T11:45:00Z",
  "service": "auth_service"
}
```

### 2. 获取游戏服务器列表

**接口**: `GET {{auth_service_url}}/api/v1/game/servers?game_type=1&region=asia-east`

**请求头**:
```
Authorization: Bearer {{access_token}}
```

**查询参数**:
- `game_type`: 游戏类型 (必需)
- `region`: 区域 (可选)

**成功响应** (200):
```json
{
  "success": true,
  "data": [
    {
      "server_id": "snake-server-001",
      "server_name": "贪吃蛇服务器1",
      "host": "game1.example.com",
      "port": 9001,
      "region": "asia-east",
      "current_players": 45,
      "max_players": 100,
      "cpu_usage": 35.5,
      "memory_usage": 42.3,
      "is_healthy": true,
      "is_accepting_players": true
    },
    {
      "server_id": "snake-server-002",
      "server_name": "贪吃蛇服务器2",
      "host": "game2.example.com",
      "port": 9002,
      "region": "asia-east",
      "current_players": 78,
      "max_players": 100,
      "cpu_usage": 68.2,
      "memory_usage": 71.8,
      "is_healthy": true,
      "is_accepting_players": true
    }
  ],
  "timestamp": "2025-07-21T11:50:00Z",
  "service": "auth_service"
}
```

## 🏥 管理和监控接口测试

### 1. 健康检查

**接口**: `GET {{auth_service_url}}/health`

**成功响应** (200):
```json
{
  "service": "auth_service",
  "version": "1.0.0",
  "status": "healthy",
  "timestamp": "2025-07-21T12:00:00Z",
  "uptime_seconds": 3600,
  "checks": {
    "mysql": {
      "status": "healthy",
      "pool_size": 10,
      "active_connections": 3
    },
    "redis": {
      "status": "healthy",
      "pool_size": 5,
      "active_connections": 2
    },
    "kafka": {
      "status": "healthy",
      "connected": true
    }
  }
}
```

### 2. 服务状态

**接口**: `GET {{auth_service_url}}/api/v1/admin/status`

**请求头**:
```
Authorization: Bearer {{access_token}}
```

**成功响应** (200):
```json
{
  "service": "auth_service",
  "version": "1.0.0",
  "status": "running",
  "initialized": true,
  "timestamp": "2025-07-21T12:05:00Z",
  "components": {
    "http_server": "initialized",
    "mysql_pool": "initialized",
    "redis_pool": "initialized",
    "jwt_manager": "initialized",
    "password_manager": "initialized",
    "session_manager": "initialized",
    "user_repository": "initialized"
  }
}
```

### 3. 统计信息

**接口**: `GET {{auth_service_url}}/api/v1/admin/statistics`

**请求头**:
```
Authorization: Bearer {{access_token}}
```

**成功响应** (200):
```json
{
  "service": "auth_service",
  "version": "1.0.0",
  "timestamp": "2025-07-21T12:10:00Z",
  "requests": {
    "total": 1250,
    "successful": 1180,
    "failed": 70,
    "success_rate": 94.4
  },
  "auth": {
    "registrations": 45,
    "logins": 320,
    "game_logins": 180
  },
  "jwt": {
    "tokens_generated": 365,
    "tokens_validated": 2840,
    "tokens_revoked": 25,
    "blacklist_size": 25
  },
  "session": {
    "active_sessions": 85,
    "total_sessions": 365,
    "cleanup_runs": 12
  }
}
```

## ❌ 错误处理测试

### 1. 注册错误场景

#### 请求体为空
**请求体**: (空)

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "EMPTY_REQUEST_BODY",
    "message": "请求体不能为空"
  },
  "timestamp": "2025-07-21T12:15:00Z",
  "service": "auth_service"
}
```

#### Content-Type错误
**请求头**:
```
Content-Type: text/plain
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CONTENT_TYPE",
    "message": "Content-Type必须为application/json"
  },
  "timestamp": "2025-07-21T12:15:00Z",
  "service": "auth_service"
}
```

#### JSON格式错误
**请求体**:
```
{invalid json format
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "INVALID_JSON",
    "message": "请求体JSON格式无效"
  },
  "timestamp": "2025-07-21T12:15:00Z",
  "service": "auth_service"
}
```

#### 用户名已存在
**请求体**:
```json
{
  "username": "testuser001",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "USERNAME_EXISTS",
    "message": "用户名已存在"
  },
  "timestamp": "2025-07-21T12:15:00Z",
  "service": "auth_service"
}
```

#### 邮箱已存在
**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "EMAIL_EXISTS",
    "message": "邮箱已存在"
  },
  "timestamp": "2025-07-21T12:16:00Z",
  "service": "auth_service"
}
```

#### 密码强度不足
**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "123456"
}
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "WEAK_PASSWORD",
    "message": "密码强度不足，需要包含大小写字母、数字和特殊字符"
  },
  "timestamp": "2025-07-21T12:17:00Z",
  "service": "auth_service"
}
```

### 2. 登录错误场景

#### 用户不存在
**请求体**:
```json
{
  "username": "nonexistentuser",
  "password": "SecurePassword123!"
}
```

**错误响应** (401):
```json
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "用户不存在"
  },
  "timestamp": "2025-07-21T12:18:00Z",
  "service": "auth_service"
}
```

#### 密码错误
**请求体**:
```json
{
  "username": "testuser001",
  "password": "WrongPassword123!"
}
```

**错误响应** (401):
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PASSWORD",
    "message": "密码错误"
  },
  "timestamp": "2025-07-21T12:19:00Z",
  "service": "auth_service"
}
```

#### 账户被锁定
**错误响应** (423):
```json
{
  "success": false,
  "error": {
    "code": "ACCOUNT_LOCKED",
    "message": "账户已被锁定，请稍后再试"
  },
  "timestamp": "2025-07-21T12:20:00Z",
  "service": "auth_service"
}
```

### 3. 令牌相关错误

#### 无效令牌
**请求头**:
```
Authorization: Bearer invalid_token_here
```

**错误响应** (401):
```json
{
  "success": false,
  "error": {
    "code": "INVALID_TOKEN",
    "message": "无效的访问令牌"
  },
  "timestamp": "2025-07-21T12:21:00Z",
  "service": "auth_service"
}
```

#### 令牌过期
**错误响应** (401):
```json
{
  "success": false,
  "error": {
    "code": "TOKEN_EXPIRED",
    "message": "访问令牌已过期"
  },
  "timestamp": "2025-07-21T12:22:00Z",
  "service": "auth_service"
}
```

#### 令牌被撤销
**错误响应** (401):
```json
{
  "success": false,
  "error": {
    "code": "TOKEN_REVOKED",
    "message": "访问令牌已被撤销"
  },
  "timestamp": "2025-07-21T12:23:00Z",
  "service": "auth_service"
}
```

## 🧪 测试场景集合

### 完整用户流程测试

#### 场景1: 新用户注册到游戏登录
1. **用户注册** → 获取access_token和refresh_token
2. **令牌验证** → 确认令牌有效
3. **游戏登录** → 选择贪吃蛇游戏
4. **获取服务器列表** → 查看可用服务器
5. **用户登出** → 清理会话

#### 场景2: 老用户登录和令牌刷新
1. **用户登录** → 使用已有账户
2. **令牌验证** → 确认令牌有效
3. **等待令牌接近过期** → 模拟时间流逝
4. **刷新令牌** → 获取新的访问令牌
5. **游戏登录** → 使用新令牌登录游戏

#### 场景3: 多设备登录测试
1. **设备1登录** → device_type: "web", device_id: "web_001"
2. **设备2登录** → device_type: "mobile", device_id: "mobile_001"
3. **验证两个设备的令牌** → 都应该有效
4. **设备1登出** → 只影响设备1的会话
5. **验证设备2令牌** → 仍然有效

### 压力测试场景

#### 并发注册测试
- 同时发送100个注册请求
- 验证用户名和邮箱唯一性约束
- 检查服务响应时间和成功率

#### 并发登录测试
- 同一用户多设备同时登录
- 验证会话管理和令牌分发
- 检查Redis缓存性能

#### 令牌验证压力测试
- 高频率令牌验证请求
- 测试JWT验证性能
- 检查黑名单查询效率

## 📊 性能基准测试

### 响应时间基准
- **用户注册**: < 500ms
- **用户登录**: < 300ms
- **令牌验证**: < 50ms
- **游戏登录**: < 400ms
- **健康检查**: < 100ms

### 并发处理能力
- **最大并发用户**: 1000+
- **每秒请求数**: 500+ QPS
- **令牌验证**: 2000+ QPS

## 🔧 Postman Collection 配置

### Pre-request Script (全局)
```javascript
// 自动设置时间戳
pm.globals.set("timestamp", new Date().toISOString());

// 自动生成设备ID
if (!pm.environment.get("device_id")) {
    pm.environment.set("device_id", "postman_" + Date.now());
}
```

### Tests Script (全局)
```javascript
// 通用响应验证
pm.test("Status code is success", function () {
    pm.expect(pm.response.code).to.be.oneOf([200, 201]);
});

pm.test("Response has required structure", function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property('success');
    pm.expect(response).to.have.property('timestamp');
    pm.expect(response).to.have.property('service');
});

pm.test("Response time is acceptable", function () {
    pm.expect(pm.response.responseTime).to.be.below(5000);
});

// 自动保存令牌
if (pm.response.code === 200) {
    const response = pm.response.json();
    if (response.success && response.data) {
        if (response.data.access_token) {
            pm.environment.set("access_token", response.data.access_token);
        }
        if (response.data.refresh_token) {
            pm.environment.set("refresh_token", response.data.refresh_token);
        }
        if (response.data.user_info && response.data.user_info.user_id) {
            pm.environment.set("user_id", response.data.user_info.user_id);
        }
    }
}
```

## 📝 测试报告模板

### 测试执行记录
```
测试日期: 2025-07-21
测试环境: http://localhost:8001
测试执行者: [测试人员姓名]

功能测试结果:
✅ 用户注册 - 通过
✅ 用户登录 - 通过
✅ 令牌验证 - 通过
✅ 令牌刷新 - 通过
✅ 用户登出 - 通过
✅ 游戏登录 - 通过
✅ 服务器列表 - 通过
✅ 健康检查 - 通过

错误处理测试:
✅ 用户名重复 - 正确返回错误
✅ 邮箱重复 - 正确返回错误
✅ 密码强度不足 - 正确返回错误
✅ 无效令牌 - 正确返回错误
✅ 令牌过期 - 正确返回错误

性能测试:
- 平均响应时间: 245ms
- 最大响应时间: 892ms
- 成功率: 99.2%
- 并发处理: 100用户同时在线

问题记录:
[记录发现的问题和建议]
```
