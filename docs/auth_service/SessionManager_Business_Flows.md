# SessionManager 业务流程详细分析

## 目录
1. [会话管理器初始化流程](#会话管理器初始化流程)
2. [会话创建和存储流程](#会话创建和存储流程)
3. [会话验证和更新流程](#会话验证和更新流程)
4. [会话清理和销毁流程](#会话清理和销毁流程)
5. [会话安全和监控流程](#会话安全和监控流程)
6. [Redis缓存管理流程](#Redis缓存管理流程)

## 会话管理器初始化流程

### 1. 会话管理器创建和Redis连接流程

**业务场景**：创建会话管理器实例并建立Redis连接

**执行步骤**：

#### 步骤1：管理器实例创建
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::SessionManager()
session_manager_ = std::make_unique<SessionManager>(config.session_config);
```

**内部执行序列**：
1. `SessionManager::SessionManager(session_config)` - 构造函数
2. 保存会话配置：`config_ = session_config`
3. 初始化配置参数：
   - `session_timeout_minutes_ = session_config.timeout_minutes` - 会话超时时间（默认30分钟）
   - `max_sessions_per_user_ = session_config.max_sessions_per_user` - 每用户最大会话数（默认5）
   - `cleanup_interval_minutes_ = session_config.cleanup_interval_minutes` - 清理间隔（默认10分钟）
   - `enable_session_tracking_ = session_config.enable_tracking` - 是否启用会话跟踪
4. 初始化状态：
   - `redis_pool_ = nullptr` - Redis连接池指针
   - `cleanup_task_running_ = false` - 清理任务状态
   - `initialized_ = false` - 初始化状态

#### 步骤2：Redis连接池初始化
```cpp
// 调用类：SessionManager
// 使用函数：SessionManager::initialize()
bool success = session_manager_->initialize();
```

**内部执行序列**：
1. `SessionManager::initialize()`
2. 创建Redis连接池：
   ```cpp
   redis_pool_ = std::make_shared<RedisPool>(
       config_.redis_host,
       config_.redis_port,
       config_.redis_password,
       config_.redis_pool_size
   );
   ```

3. 初始化Redis连接：
   ```cpp
   if (!redis_pool_->initialize()) {
       LOG_ERROR("Failed to initialize Redis connection pool for SessionManager");
       return false;
   }
   ```

4. 测试Redis连接：
   ```cpp
   try {
       auto test_result = redis_pool_->ping();
       if (test_result != "PONG") {
           LOG_ERROR("Redis connection test failed");
           return false;
       }
   } catch (const std::exception& e) {
       LOG_ERROR("Redis connection error: " + std::string(e.what()));
       return false;
   }
   ```

5. 清理过期会话：`cleanupExpiredSessions()`
6. 设置初始化标志：`initialized_ = true`
7. 记录初始化日志：`LOG_INFO("SessionManager initialized successfully")`

**结果**：会话管理器初始化完成，Redis连接建立

### 2. 清理任务启动流程

**业务场景**：启动后台清理任务定期清理过期会话

**执行步骤**：

#### 步骤1：清理任务启动
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::startCleanupTask()
session_manager_->startCleanupTask();
```

**内部执行序列**：
1. `SessionManager::startCleanupTask()`
2. 检查任务状态：
   ```cpp
   if (cleanup_task_running_) {
       LOG_WARNING("Cleanup task is already running");
       return;
   }
   ```

3. 启动清理线程：
   ```cpp
   cleanup_task_running_ = true;
   cleanup_thread_ = std::thread([this]() {
       while (cleanup_task_running_) {
           try {
               cleanupExpiredSessions();
               std::this_thread::sleep_for(std::chrono::minutes(cleanup_interval_minutes_));
           } catch (const std::exception& e) {
               LOG_ERROR("Session cleanup error: " + std::string(e.what()));
           }
       }
   });
   ```

4. 记录启动日志：`LOG_INFO("Session cleanup task started")`

**结果**：后台清理任务启动成功

## 会话创建和存储流程

### 1. 用户会话创建流程

**业务场景**：为用户登录创建新的会话

**执行步骤**：

#### 步骤1：会话创建请求
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::createSession()
std::string session_id = session_manager_->createSession(user_id, client_ip, user_agent);
```

**内部执行序列**：
1. `SessionManager::createSession(user_id, client_ip, user_agent)`
2. 生成唯一会话ID：
   ```cpp
   std::string session_id = generateUUID();
   // 确保会话ID唯一性
   while (redis_pool_->exists("session:" + session_id)) {
       session_id = generateUUID();
   }
   ```

3. 检查用户会话数量限制：
   ```cpp
   int active_sessions = getUserActiveSessionCount(user_id);
   if (active_sessions >= max_sessions_per_user_) {
       // 清理最旧的会话
       cleanupOldestUserSession(user_id);
   }
   ```

#### 步骤2：会话信息构建
**执行序列**：
1. 创建会话对象：
   ```cpp
   SessionInfo session;
   session.session_id = session_id;
   session.user_id = user_id;
   session.client_ip = client_ip;
   session.user_agent = user_agent;
   session.created_at = std::chrono::system_clock::now();
   session.last_access_at = session.created_at;
   session.is_active = true;
   session.device_fingerprint = generateDeviceFingerprint(client_ip, user_agent);
   ```

2. 计算过期时间：
   ```cpp
   auto expiry_time = session.created_at + std::chrono::minutes(session_timeout_minutes_);
   int expiry_seconds = std::chrono::duration_cast<std::chrono::seconds>(
       expiry_time - std::chrono::system_clock::now()
   ).count();
   ```

#### 步骤3：会话存储到Redis
**执行序列**：
1. 存储会话数据：
   ```cpp
   std::string session_key = "session:" + session_id;
   std::string session_data = session.toJson();
   redis_pool_->setex(session_key, session_data, expiry_seconds);
   ```

2. 更新用户会话列表：
   ```cpp
   std::string user_sessions_key = "user_sessions:" + std::to_string(user_id);
   redis_pool_->sadd(user_sessions_key, session_id);
   redis_pool_->expire(user_sessions_key, expiry_seconds);
   ```

3. 记录会话统计：
   ```cpp
   redis_pool_->incr("session_stats:total_created");
   redis_pool_->incr("session_stats:active_count");
   ```

4. 记录创建日志：
   ```cpp
   LOG_INFO("Session created: " + session_id + " for user: " + std::to_string(user_id) + 
            " from IP: " + client_ip);
   ```

**结果**：会话创建成功，返回会话ID

### 2. 设备指纹生成流程

**业务场景**：生成设备指纹用于会话安全验证

**执行步骤**：

#### 步骤1：设备指纹计算
```cpp
// 内部使用函数：SessionManager::generateDeviceFingerprint()
std::string fingerprint = generateDeviceFingerprint(client_ip, user_agent);
```

**内部执行序列**：
1. 收集设备信息：
   ```cpp
   std::string device_info = client_ip + "|" + user_agent;
   // 可以添加更多设备特征：屏幕分辨率、时区、语言等
   ```

2. 生成指纹哈希：
   ```cpp
   std::hash<std::string> hasher;
   size_t hash_value = hasher(device_info);
   std::string fingerprint = std::to_string(hash_value);
   ```

3. 存储设备指纹映射：
   ```cpp
   std::string fingerprint_key = "device:" + fingerprint;
   redis_pool_->setex(fingerprint_key, device_info, 86400); // 24小时
   ```

**结果**：设备指纹生成完成

## 会话验证和更新流程

### 1. 会话有效性验证流程

**业务场景**：验证会话是否有效和活跃

**执行步骤**：

#### 步骤1：会话验证请求
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::isSessionValid()
bool valid = session_manager_->isSessionValid(session_id);
```

**内部执行序列**：
1. `SessionManager::isSessionValid(session_id)`
2. 检查会话ID格式：
   ```cpp
   if (session_id.empty() || session_id.length() != 36) { // UUID长度
       return false;
   }
   ```

3. 从Redis获取会话数据：
   ```cpp
   std::string session_key = "session:" + session_id;
   auto session_data = redis_pool_->get(session_key);
   if (session_data.empty()) {
       LOG_DEBUG("Session not found: " + session_id);
       return false;
   }
   ```

#### 步骤2：会话数据验证
**执行序列**：
1. 解析会话数据：
   ```cpp
   try {
       auto session_json = nlohmann::json::parse(session_data);
       SessionInfo session = SessionInfo::fromJson(session_json);
   } catch (const std::exception& e) {
       LOG_ERROR("Failed to parse session data: " + std::string(e.what()));
       return false;
   }
   ```

2. 检查会话状态：
   ```cpp
   if (!session.is_active) {
       LOG_DEBUG("Session is inactive: " + session_id);
       return false;
   }
   ```

3. 检查会话过期：
   ```cpp
   auto now = std::chrono::system_clock::now();
   auto last_access_duration = now - session.last_access_at;
   if (last_access_duration > std::chrono::minutes(session_timeout_minutes_)) {
       LOG_DEBUG("Session expired: " + session_id);
       destroySession(session_id);
       return false;
   }
   ```

#### 步骤3：安全验证（可选）
**执行序列**：
1. IP地址验证：
   ```cpp
   if (config_.enable_ip_validation && current_ip != session.client_ip) {
       LOG_WARNING("IP address mismatch for session: " + session_id);
       // 根据配置决定是否拒绝或记录警告
   }
   ```

2. 设备指纹验证：
   ```cpp
   if (config_.enable_device_validation) {
       std::string current_fingerprint = generateDeviceFingerprint(current_ip, current_user_agent);
       if (current_fingerprint != session.device_fingerprint) {
           LOG_WARNING("Device fingerprint mismatch for session: " + session_id);
       }
   }
   ```

**结果**：会话验证完成，返回有效性状态

### 2. 会话最后访问时间更新流程

**业务场景**：更新会话的最后访问时间

**执行步骤**：

#### 步骤1：访问时间更新
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::updateLastAccess()
bool success = session_manager_->updateLastAccess(session_id);
```

**内部执行序列**：
1. `SessionManager::updateLastAccess(session_id)`
2. 获取当前会话数据：
   ```cpp
   std::string session_key = "session:" + session_id;
   auto session_data = redis_pool_->get(session_key);
   if (session_data.empty()) {
       return false;
   }
   ```

3. 更新访问时间：
   ```cpp
   auto session_json = nlohmann::json::parse(session_data);
   SessionInfo session = SessionInfo::fromJson(session_json);
   session.last_access_at = std::chrono::system_clock::now();
   ```

4. 重新计算过期时间：
   ```cpp
   auto new_expiry = session.last_access_at + std::chrono::minutes(session_timeout_minutes_);
   int expiry_seconds = std::chrono::duration_cast<std::chrono::seconds>(
       new_expiry - std::chrono::system_clock::now()
   ).count();
   ```

5. 更新Redis存储：
   ```cpp
   redis_pool_->setex(session_key, session.toJson(), expiry_seconds);
   ```

**结果**：会话访问时间更新成功

### 3. 会话信息获取流程

**业务场景**：获取会话的详细信息

**执行步骤**：

#### 步骤1：会话信息查询
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::getSessionInfo()
auto session_opt = session_manager_->getSessionInfo(session_id);
```

**内部执行序列**：
1. 从Redis获取会话数据
2. 解析会话信息
3. 验证会话有效性
4. 返回会话信息对象

**结果**：返回会话详细信息

## 会话清理和销毁流程

### 1. 单个会话销毁流程

**业务场景**：销毁指定的用户会话

**执行步骤**：

#### 步骤1：会话销毁请求
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::destroySession()
bool success = session_manager_->destroySession(session_id);
```

**内部执行序列**：
1. `SessionManager::destroySession(session_id)`
2. 获取会话信息：
   ```cpp
   std::string session_key = "session:" + session_id;
   auto session_data = redis_pool_->get(session_key);
   if (session_data.empty()) {
       return false; // 会话不存在
   }
   ```

3. 解析用户ID：
   ```cpp
   auto session_json = nlohmann::json::parse(session_data);
   uint64_t user_id = session_json["user_id"];
   ```

#### 步骤2：清理会话数据
**执行序列**：
1. 删除会话数据：
   ```cpp
   redis_pool_->del(session_key);
   ```

2. 从用户会话列表中移除：
   ```cpp
   std::string user_sessions_key = "user_sessions:" + std::to_string(user_id);
   redis_pool_->srem(user_sessions_key, session_id);
   ```

3. 更新统计信息：
   ```cpp
   redis_pool_->decr("session_stats:active_count");
   redis_pool_->incr("session_stats:total_destroyed");
   ```

4. 记录销毁日志：
   ```cpp
   LOG_INFO("Session destroyed: " + session_id + " for user: " + std::to_string(user_id));
   ```

**结果**：会话销毁成功

### 2. 用户所有会话销毁流程

**业务场景**：销毁用户的所有活跃会话

**执行步骤**：

#### 步骤1：用户会话批量销毁
```cpp
// 调用类：AuthService
// 使用函数：SessionManager::destroyAllUserSessions()
int destroyed_count = session_manager_->destroyAllUserSessions(user_id);
```

**内部执行序列**：
1. `SessionManager::destroyAllUserSessions(user_id)`
2. 获取用户所有会话：
   ```cpp
   std::string user_sessions_key = "user_sessions:" + std::to_string(user_id);
   auto session_ids = redis_pool_->smembers(user_sessions_key);
   ```

3. 批量删除会话：
   ```cpp
   int destroyed_count = 0;
   for (const auto& session_id : session_ids) {
       std::string session_key = "session:" + session_id;
       if (redis_pool_->del(session_key) > 0) {
           destroyed_count++;
       }
   }
   ```

4. 清理用户会话列表：
   ```cpp
   redis_pool_->del(user_sessions_key);
   ```

5. 更新统计信息：
   ```cpp
   redis_pool_->decrby("session_stats:active_count", destroyed_count);
   redis_pool_->incrby("session_stats:total_destroyed", destroyed_count);
   ```

**结果**：用户所有会话销毁完成

### 3. 过期会话清理流程

**业务场景**：定期清理过期的会话数据

**执行步骤**：

#### 步骤1：过期会话扫描
```cpp
// 内部定期调用：SessionManager::cleanupExpiredSessions()
int cleaned_count = cleanupExpiredSessions();
```

**内部执行序列**：
1. `SessionManager::cleanupExpiredSessions()`
2. 扫描会话键：
   ```cpp
   auto session_keys = redis_pool_->keys("session:*");
   int cleaned_count = 0;
   ```

3. 检查每个会话：
   ```cpp
   for (const auto& session_key : session_keys) {
       auto session_data = redis_pool_->get(session_key);
       if (session_data.empty()) continue;
       
       try {
           auto session_json = nlohmann::json::parse(session_data);
           SessionInfo session = SessionInfo::fromJson(session_json);
           
           auto now = std::chrono::system_clock::now();
           auto duration = now - session.last_access_at;
           
           if (duration > std::chrono::minutes(session_timeout_minutes_)) {
               // 会话过期，删除
               redis_pool_->del(session_key);
               cleaned_count++;
           }
       } catch (const std::exception& e) {
           // 数据损坏，删除
           redis_pool_->del(session_key);
           cleaned_count++;
       }
   }
   ```

4. 记录清理结果：
   ```cpp
   if (cleaned_count > 0) {
       LOG_INFO("Cleaned up " + std::to_string(cleaned_count) + " expired sessions");
       redis_pool_->decrby("session_stats:active_count", cleaned_count);
   }
   ```

**结果**：过期会话清理完成

## 会话安全和监控流程

### 1. 会话安全检查流程

**业务场景**：检测和防范会话安全威胁

**执行步骤**：

#### 步骤1：异常会话检测
```cpp
// 内部安全检查：SessionManager::detectAnomalousSession()
bool anomalous = detectAnomalousSession(session_id, current_ip, current_user_agent);
```

**内部执行序列**：
1. IP地址变化检测：
   ```cpp
   if (session.client_ip != current_ip) {
       // 记录IP变化
       LOG_WARNING("IP address changed for session: " + session_id + 
                  " from " + session.client_ip + " to " + current_ip);
       
       // 根据配置决定处理方式
       if (config_.strict_ip_validation) {
           return true; // 标记为异常
       }
   }
   ```

2. 用户代理变化检测：
   ```cpp
   if (session.user_agent != current_user_agent) {
       LOG_WARNING("User agent changed for session: " + session_id);
   }
   ```

3. 会话劫持检测：
   ```cpp
   // 检测同一用户的多个会话是否来自不同地理位置
   auto user_sessions = getUserActiveSessions(session.user_id);
   if (detectGeographicalAnomaly(user_sessions)) {
       LOG_CRITICAL("Potential session hijacking detected for user: " + 
                   std::to_string(session.user_id));
   }
   ```

#### 步骤2：安全响应处理
**执行序列**：
1. 异常会话处理：
   ```cpp
   if (anomalous) {
       // 标记会话为可疑
       markSessionAsSuspicious(session_id);
       
       // 可选：强制重新认证
       if (config_.force_reauth_on_anomaly) {
           destroySession(session_id);
       }
       
       // 通知用户
       notifyUserOfSuspiciousActivity(session.user_id);
   }
   ```

**结果**：会话安全检查完成

### 2. 会话统计和监控流程

**业务场景**：收集和监控会话相关的统计信息

**执行步骤**：

#### 步骤1：会话统计收集
```cpp
// 调用类：SessionManager
// 使用函数：SessionManager::getSessionStatistics()
auto stats = session_manager_->getSessionStatistics();
```

**内部执行序列**：
1. 收集基本统计：
   ```cpp
   SessionStatistics stats;
   stats.total_active_sessions = redis_pool_->get("session_stats:active_count");
   stats.total_created_today = redis_pool_->get("session_stats:created_today");
   stats.total_destroyed_today = redis_pool_->get("session_stats:destroyed_today");
   ```

2. 计算平均会话时长：
   ```cpp
   auto session_keys = redis_pool_->keys("session:*");
   long total_duration = 0;
   int valid_sessions = 0;
   
   for (const auto& key : session_keys) {
       auto session_data = redis_pool_->get(key);
       if (!session_data.empty()) {
           auto session = SessionInfo::fromJson(nlohmann::json::parse(session_data));
           auto duration = std::chrono::system_clock::now() - session.created_at;
           total_duration += std::chrono::duration_cast<std::chrono::minutes>(duration).count();
           valid_sessions++;
       }
   }
   
   stats.average_session_duration_minutes = valid_sessions > 0 ? total_duration / valid_sessions : 0;
   ```

#### 步骤2：性能监控
**执行序列**：
1. Redis性能监控：
   ```cpp
   auto redis_info = redis_pool_->info();
   stats.redis_memory_usage = parseRedisMemoryUsage(redis_info);
   stats.redis_connected_clients = parseRedisConnectedClients(redis_info);
   ```

2. 会话操作性能：
   ```cpp
   // 记录操作耗时
   auto start_time = std::chrono::high_resolution_clock::now();
   // 执行会话操作
   auto end_time = std::chrono::high_resolution_clock::now();
   auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
   
   // 更新性能统计
   updatePerformanceMetrics("session_operation", duration.count());
   ```

**结果**：会话统计和监控数据收集完成

## Redis缓存管理流程

### 1. Redis连接管理流程

**业务场景**：管理Redis连接池的生命周期

**执行步骤**：

#### 步骤1：连接健康检查
```cpp
// 调用类：SessionManager
// 使用函数：SessionManager::healthCheck()
bool healthy = session_manager_->healthCheck();
```

**内部执行序列**：
1. 检查Redis连接池状态：
   ```cpp
   if (!redis_pool_ || !redis_pool_->isHealthy()) {
       return false;
   }
   ```

2. 执行Redis ping测试：
   ```cpp
   try {
       auto ping_result = redis_pool_->ping();
       if (ping_result != "PONG") {
           LOG_ERROR("Redis ping test failed");
           return false;
       }
   } catch (const std::exception& e) {
       LOG_ERROR("Redis health check failed: " + std::string(e.what()));
       return false;
   }
   ```

3. 检查会话数据完整性：
   ```cpp
   // 随机检查几个会话的数据完整性
   auto sample_keys = redis_pool_->randomkey("session:*", 5);
   for (const auto& key : sample_keys) {
       auto data = redis_pool_->get(key);
       if (!data.empty()) {
           try {
               SessionInfo::fromJson(nlohmann::json::parse(data));
           } catch (const std::exception& e) {
               LOG_WARNING("Corrupted session data found: " + key);
           }
       }
   }
   ```

**结果**：Redis连接健康状态检查完成

### 2. 缓存优化流程

**业务场景**：优化Redis缓存的性能和存储

**执行步骤**：

#### 步骤1：内存优化
**执行序列**：
1. 监控内存使用：
   ```cpp
   auto memory_info = redis_pool_->memory_usage();
   if (memory_info.used_memory_ratio > 0.8) {
       LOG_WARNING("Redis memory usage is high: " + 
                  std::to_string(memory_info.used_memory_ratio * 100) + "%");
       
       // 触发清理操作
       cleanupExpiredSessions();
   }
   ```

2. 数据压缩：
   ```cpp
   // 对会话数据进行压缩存储（可选）
   std::string compressed_data = compressSessionData(session.toJson());
   redis_pool_->setex(session_key, compressed_data, expiry_seconds);
   ```

#### 步骤2：性能优化
**执行序列**：
1. 批量操作优化：
   ```cpp
   // 使用Redis pipeline进行批量操作
   auto pipeline = redis_pool_->pipeline();
   for (const auto& session_id : session_ids) {
       pipeline.del("session:" + session_id);
   }
   pipeline.exec();
   ```

2. 连接池调优：
   ```cpp
   // 根据负载动态调整连接池大小
   if (getCurrentLoad() > 0.8) {
       redis_pool_->expandPool(additional_connections);
   }
   ```

**结果**：Redis缓存性能优化完成

## 总结

SessionManager系统的业务流程体现了现代会话管理的核心特性：

1. **会话生命周期**：完整的会话创建、验证、更新和销毁流程
2. **安全保护**：设备指纹、IP验证、异常检测等安全机制
3. **性能优化**：Redis缓存、连接池、批量操作等性能优化
4. **自动清理**：定期清理过期会话，防止内存泄漏
5. **监控统计**：全面的会话统计和性能监控
6. **高可用性**：健康检查、错误恢复、连接管理
7. **可扩展性**：支持大量并发会话和用户
8. **安全审计**：详细的会话日志和安全事件记录

每个业务流程都经过精心设计，确保会话管理的安全性、可靠性和高性能。
