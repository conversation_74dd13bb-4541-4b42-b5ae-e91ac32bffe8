# AuthService 主要业务流程详细分析

## 目录
1. [服务启动和初始化流程](#服务启动和初始化流程)
2. [用户注册流程](#用户注册流程)
3. [用户登录流程](#用户登录流程)
4. [令牌验证流程](#令牌验证流程)
5. [游戏登录流程](#游戏登录流程)
6. [用户信息管理流程](#用户信息管理流程)
7. [会话管理流程](#会话管理流程)
8. [密码管理流程](#密码管理流程)

## 服务启动和初始化流程

### 1. 服务实例创建和配置加载流程

**业务场景**：创建认证服务实例并加载完整配置

**执行步骤**：

#### 步骤1：配置加载
```cpp
// 调用类：main() 函数
// 使用函数：AuthService::Config::fromConfigManager()
auto config = AuthService::Config::fromConfigManager();
```

**内部执行序列**：
1. `Config::fromConfigManager()` - 从配置管理器加载配置
2. 加载HTTP服务器配置：
   - `config.server_config.host` - 监听地址 (默认: "0.0.0.0")
   - `config.server_config.port` - 监听端口 (默认: 8008)
   - `config.server_config.worker_threads` - 工作线程数 (默认: 4)
   - `config.server_config.keep_alive_timeout_ms` - Keep-Alive超时 (默认: 60000)
   - `config.server_config.request_timeout_ms` - 请求超时 (默认: 30000)
3. 加载数据库配置：
   - `config.database_config.mysql_host` - MySQL主机地址
   - `config.database_config.mysql_port` - MySQL端口
   - `config.database_config.mysql_database` - 数据库名称
   - `config.database_config.mysql_username` - 数据库用户名
   - `config.database_config.mysql_password` - 数据库密码
   - `config.database_config.redis_host` - Redis主机地址
   - `config.database_config.redis_port` - Redis端口
4. 加载JWT配置：
   - `config.jwt_config.secret_key` - JWT签名密钥
   - `config.jwt_config.access_token_expiry_hours` - 访问令牌过期时间
   - `config.jwt_config.refresh_token_expiry_days` - 刷新令牌过期时间
   - `config.jwt_config.issuer` - JWT签发者
   - `config.jwt_config.audience` - JWT受众
5. 加载安全配置：
   - `config.security_config.password_min_length` - 密码最小长度
   - `config.security_config.password_require_uppercase` - 是否需要大写字母
   - `config.security_config.password_require_lowercase` - 是否需要小写字母
   - `config.security_config.password_require_numbers` - 是否需要数字
   - `config.security_config.password_require_special_chars` - 是否需要特殊字符
   - `config.security_config.max_login_attempts` - 最大登录尝试次数
   - `config.security_config.lockout_duration_minutes` - 锁定持续时间

#### 步骤2：服务实例创建
```cpp
// 调用类：main() 函数
// 使用函数：AuthService::AuthService()
auto auth_service = std::make_unique<AuthService>(config);
```

**内部执行序列**：
1. `AuthService::AuthService(config)` - 构造函数
2. `config_ = config` - 保存配置
3. `running_ = false` - 初始化运行状态
4. 创建核心组件：
   - `password_manager_ = std::make_unique<PasswordManager>(config.security_config)` - 密码管理器
   - `jwt_manager_ = std::make_unique<JWTManager>(config.jwt_config)` - JWT管理器
   - `session_manager_ = std::make_unique<SessionManager>(config.session_config)` - 会话管理器
   - `user_repository_ = std::make_unique<UserRepository>(config.database_config)` - 用户仓库
   - `database_initializer_ = std::make_unique<DatabaseInitializer>(config.database_config)` - 数据库初始化器
5. 创建线程池：`thread_pool_ = std::make_shared<ThreadPool>(config.server_config.worker_threads)`

#### 步骤3：组件初始化
```cpp
// 调用类：AuthService
// 使用函数：AuthService::initialize()
bool success = auth_service->initialize();
```

**内部执行序列**：
1. `AuthService::initialize()`
2. 初始化数据库：`database_initializer_->initialize()`
   - 创建数据库表结构
   - 执行数据库迁移
   - 验证数据库连接
3. 初始化用户仓库：`user_repository_->initialize()`
   - 建立数据库连接池
   - 验证表结构
   - 加载缓存数据
4. 初始化会话管理器：`session_manager_->initialize()`
   - 连接Redis缓存
   - 清理过期会话
   - 初始化会话存储
5. 初始化HTTP服务器：`http_server_ = std::make_unique<HttpServer>(config_.server_config)`
6. 注册路由处理器：`registerRoutes()`
   - 注册认证相关路由
   - 注册用户管理路由
   - 注册游戏相关路由
7. 记录初始化日志：`LOG_INFO("AuthService initialized successfully")`

**结果**：认证服务初始化完成，准备启动服务

### 2. 服务启动流程

**业务场景**：启动认证服务开始处理请求

**执行步骤**：

#### 步骤1：服务启动
```cpp
// 调用类：main() 函数
// 使用函数：AuthService::start()
auth_service->start();
```

**内部执行序列**：
1. `AuthService::start()`
2. 检查初始化状态：`if (!http_server_)`
3. 启动HTTP服务器：`http_server_->start()`
4. 设置运行状态：`running_ = true`
5. 启动后台任务：
   - 启动会话清理任务：`session_manager_->startCleanupTask()`
   - 启动统计收集任务：`startStatisticsTask()`
   - 启动健康检查任务：`startHealthCheckTask()`
6. 记录启动日志：`LOG_INFO("AuthService started on " + config_.server_config.host + ":" + std::to_string(config_.server_config.port))`

**结果**：认证服务成功启动并开始监听请求

## 用户注册流程

### 1. 用户注册请求处理流程

**业务场景**：处理新用户注册请求

**执行步骤**：

#### 步骤1：请求接收和验证
```cpp
// 调用类：AuthService
// 路由：POST /api/v1/auth/register
// 使用函数：AuthService::register()
auto result = this->register(auth_request);
```

**内部执行序列**：
1. 接收HTTP POST请求到 `/api/v1/auth/register`
2. 解析请求体JSON：`auto auth_request = AuthRequest::fromJson(nlohmann::json::parse(req.getBody()))`
3. 验证请求格式：`std::string validation_error = auth_request.validate()`
4. 检查必需字段：
   - 用户名或邮箱不能为空
   - 密码不能为空
   - 用户名格式验证（如果提供）
   - 邮箱格式验证（如果提供）
5. 如果验证失败：返回400错误响应

#### 步骤2：用户唯一性检查
**执行序列**：
1. `AuthService::register(auth_request)`
2. 检查用户名唯一性：`if (!auth_request.username.empty())`
   - `bool username_exists = user_repository_->existsByUsername(auth_request.username)`
   - 如果用户名已存在：返回冲突错误
3. 检查邮箱唯一性：`if (!auth_request.email.empty())`
   - `bool email_exists = user_repository_->existsByEmail(auth_request.email)`
   - 如果邮箱已存在：返回冲突错误

#### 步骤3：密码处理和用户创建
**执行序列**：
1. 密码强度验证：`password_manager_->validatePassword(auth_request.password)`
2. 密码哈希处理：`std::string hashed_password = password_manager_->hashPassword(auth_request.password)`
3. 创建用户对象：
   ```cpp
   UserInfo user_info;
   user_info.username = auth_request.username;
   user_info.email = auth_request.email;
   user_info.password_hash = hashed_password;
   user_info.nickname = auth_request.nickname.empty() ? auth_request.username : auth_request.nickname;
   user_info.created_at = std::chrono::system_clock::now();
   user_info.updated_at = user_info.created_at;
   user_info.is_active = true;
   ```
4. 保存用户到数据库：`auto created_user = user_repository_->create(user_info)`
5. 记录注册日志：`LOG_INFO("User registered successfully: " + user_info.username)`

**结果**：用户注册成功，返回用户信息

## 用户登录流程

### 1. 用户登录认证流程

**业务场景**：验证用户凭据并生成访问令牌

**执行步骤**：

#### 步骤1：登录请求处理
```cpp
// 调用类：AuthService
// 路由：POST /api/v1/auth/login
// 使用函数：AuthService::login()
auto result = this->login(auth_request);
```

**内部执行序列**：
1. 接收HTTP POST请求到 `/api/v1/auth/login`
2. 解析请求体：`auto auth_request = AuthRequest::fromJson(nlohmann::json::parse(req.getBody()))`
3. 验证请求格式：检查用户名/邮箱和密码是否提供
4. 提取客户端信息：
   - `auth_request.client_ip = req.getClientIP()`
   - `auth_request.user_agent = req.getHeader("User-Agent")`

#### 步骤2：用户查找和验证
**执行序列**：
1. `AuthService::login(auth_request)`
2. 根据用户名或邮箱查找用户：
   ```cpp
   std::optional<UserInfo> user_opt;
   if (!auth_request.username.empty()) {
       user_opt = user_repository_->findByUsername(auth_request.username);
   } else if (!auth_request.email.empty()) {
       user_opt = user_repository_->findByEmail(auth_request.email);
   }
   ```
3. 检查用户是否存在：`if (!user_opt.has_value())`
4. 检查用户状态：`if (!user_opt->is_active)`
5. 检查账户锁定状态：`if (isAccountLocked(user_opt->user_id))`

#### 步骤3：密码验证和令牌生成
**执行序列**：
1. 验证密码：`bool password_valid = password_manager_->verifyPassword(auth_request.password, user_opt->password_hash)`
2. 如果密码错误：
   - 记录失败尝试：`recordLoginAttempt(user_opt->user_id, false)`
   - 检查是否需要锁定账户
   - 返回认证失败错误
3. 如果密码正确：
   - 记录成功登录：`recordLoginAttempt(user_opt->user_id, true)`
   - 清除失败计数：`clearFailedAttempts(user_opt->user_id)`
   - 生成访问令牌：`std::string access_token = jwt_manager_->generateAccessToken(user_opt.value())`
   - 生成刷新令牌：`std::string refresh_token = jwt_manager_->generateRefreshToken(user_opt.value())`
   - 创建会话：`std::string session_id = session_manager_->createSession(user_opt->user_id, auth_request.client_ip, auth_request.user_agent)`

**结果**：登录成功，返回访问令牌和刷新令牌

## 令牌验证流程

### 1. JWT令牌验证流程

**业务场景**：验证客户端提供的JWT访问令牌

**执行步骤**：

#### 步骤1：令牌提取和格式验证
```cpp
// 调用类：AuthService
// 路由：POST /api/v1/auth/verify
// 使用函数：AuthService::verifyToken()
auto result = this->verifyToken(token_request);
```

**内部执行序列**：
1. 接收HTTP POST请求到 `/api/v1/auth/verify`
2. 从Authorization头提取令牌：
   ```cpp
   std::string auth_header = req.getHeader("Authorization");
   if (auth_header.length() >= 7 && auth_header.substr(0, 7) == "Bearer ") {
       std::string token = auth_header.substr(7);
   }
   ```
3. 验证令牌格式：检查是否为有效的JWT格式

#### 步骤2：JWT令牌解析和验证
**执行序列**：
1. `AuthService::verifyToken(token_request)`
2. 解析JWT令牌：`auto jwt_result = jwt_manager_->verifyAccessToken(token_request.access_token)`
3. JWT验证步骤：
   - 验证令牌签名：`verifySignature(token, secret_key)`
   - 检查令牌过期：`checkExpiration(payload)`
   - 验证签发者：`checkIssuer(payload)`
   - 验证受众：`checkAudience(payload)`
4. 提取用户信息：`auto user_claims = extractUserClaims(payload)`

#### 步骤3：用户状态验证
**执行序列**：
1. 根据用户ID查找用户：`auto user_opt = user_repository_->findById(user_claims.user_id)`
2. 检查用户是否存在：`if (!user_opt.has_value())`
3. 检查用户状态：`if (!user_opt->is_active)`
4. 检查会话有效性：`bool session_valid = session_manager_->isSessionValid(user_claims.session_id)`
5. 更新最后访问时间：`session_manager_->updateLastAccess(user_claims.session_id)`

**结果**：令牌验证成功，返回用户信息

## 游戏登录流程

### 1. 游戏登录处理流程

**业务场景**：为已认证用户提供游戏登录服务

**执行步骤**：

#### 步骤1：游戏登录请求处理
```cpp
// 调用类：AuthService
// 路由：POST /api/v1/game/login
// 使用函数：AuthService::gameLogin()
auto result = this->gameLogin(game_request);
```

**内部执行序列**：
1. 接收HTTP POST请求到 `/api/v1/game/login`
2. 处理请求体（支持空请求体）：
   ```cpp
   GameLoginRequest game_request;
   std::string request_body = req.getBody();
   if (!request_body.empty()) {
       game_request = GameLoginRequest::fromJson(nlohmann::json::parse(request_body));
   } else {
       game_request.game_type = GameType::SNAKE; // 默认游戏类型
       game_request.preferred_server_region = "default";
   }
   ```
3. 从Authorization头获取访问令牌：
   ```cpp
   if (game_request.access_token.empty()) {
       std::string auth_header = req.getHeader("Authorization");
       if (auth_header.length() >= 7 && auth_header.substr(0, 7) == "Bearer ") {
           game_request.access_token = auth_header.substr(7);
       }
   }
   ```

#### 步骤2：用户认证和游戏数据获取
**执行序列**：
1. `AuthService::gameLogin(game_request)`
2. 验证访问令牌：`auto token_result = jwt_manager_->verifyAccessToken(game_request.access_token)`
3. 获取用户信息：`auto user_info = user_repository_->findById(token_result.user_id)`
4. 获取或创建游戏数据：
   ```cpp
   auto game_data_opt = user_repository_->findGameUserData(user_info.user_id, game_request.game_type);
   if (!game_data_opt.has_value()) {
       game_data_opt = user_repository_->createGameUserData(user_info.user_id, game_request.game_type);
   }
   ```

#### 步骤3：游戏服务器分配和会话创建
**执行序列**：
1. 选择游戏服务器：`auto server_opt = selectGameServer(game_request.game_type, game_request.preferred_server_region)`
2. 生成游戏会话令牌：`std::string game_session_token = jwt_manager_->generateGameSessionToken(user_info, game_request.game_type, server_info.server_id)`
3. 创建游戏登录结果：
   ```cpp
   GameLoginResult result(game_request.game_type, game_data, server_info.host, server_info.port);
   result.game_server_id = server_info.server_id;
   result.game_session_token = game_session_token;
   ```
4. 发布游戏登录事件：记录游戏登录统计信息

**结果**：游戏登录成功，返回游戏数据和服务器信息

## 用户信息管理流程

### 1. 用户信息查询流程

**业务场景**：获取当前用户的详细信息

**执行步骤**：

#### 步骤1：用户信息请求处理
```cpp
// 调用类：AuthService
// 路由：GET /api/v1/user/profile
// 使用函数：AuthService::getUserProfile()
auto result = this->getUserProfile(user_id);
```

**内部执行序列**：
1. 接收HTTP GET请求到 `/api/v1/user/profile`
2. 从Authorization头提取并验证令牌
3. 从JWT载荷中提取用户ID
4. 查询用户详细信息：`auto user_info = user_repository_->findById(user_id)`
5. 构建响应数据（排除敏感信息如密码哈希）

#### 步骤2：用户信息更新流程
```cpp
// 调用类：AuthService
// 路由：PUT /api/v1/user/profile
// 使用函数：AuthService::updateUserProfile()
auto result = this->updateUserProfile(user_id, update_request);
```

**内部执行序列**：
1. 验证用户身份和权限
2. 解析更新请求：`auto update_request = UserUpdateRequest::fromJson(req.getBody())`
3. 验证更新数据：检查邮箱格式、昵称长度等
4. 检查邮箱唯一性（如果更新邮箱）
5. 更新用户信息：`user_repository_->update(user_id, update_request)`
6. 记录更新日志

**结果**：用户信息查询或更新成功

### 2. 游戏数据管理流程

**业务场景**：管理用户的游戏相关数据

**执行步骤**：

#### 步骤1：游戏数据查询
```cpp
// 调用类：AuthService
// 路由：GET /api/v1/user/game-data
// 使用函数：AuthService::getGameData()
auto result = this->getGameData(user_id, game_type);
```

**内部执行序列**：
1. 验证用户身份
2. 解析查询参数：`GameType game_type = parseGameType(req.getQueryParam("game_type"))`
3. 查询游戏数据：`auto game_data = user_repository_->findGameUserData(user_id, game_type)`
4. 如果数据不存在，创建默认数据
5. 返回游戏数据

#### 步骤2：游戏数据更新
```cpp
// 调用类：AuthService
// 路由：PUT /api/v1/user/game-data
// 使用函数：AuthService::updateGameData()
auto result = this->updateGameData(user_id, game_data);
```

**内部执行序列**：
1. 验证用户身份和游戏会话
2. 解析游戏数据更新请求
3. 验证数据合法性（防止作弊）
4. 更新游戏数据：`user_repository_->updateGameUserData(user_id, game_data)`
5. 更新排行榜和统计信息

**结果**：游戏数据管理成功

## 会话管理流程

### 1. 会话创建和维护流程

**业务场景**：管理用户登录会话的生命周期

**执行步骤**：

#### 步骤1：会话创建
```cpp
// 调用类：SessionManager
// 使用函数：SessionManager::createSession()
std::string session_id = session_manager_->createSession(user_id, client_ip, user_agent);
```

**内部执行序列**：
1. `SessionManager::createSession(user_id, client_ip, user_agent)`
2. 生成唯一会话ID：`std::string session_id = generateUUID()`
3. 创建会话对象：
   ```cpp
   SessionInfo session;
   session.session_id = session_id;
   session.user_id = user_id;
   session.client_ip = client_ip;
   session.user_agent = user_agent;
   session.created_at = std::chrono::system_clock::now();
   session.last_access_at = session.created_at;
   session.is_active = true;
   ```
4. 存储到Redis缓存：`redis_pool_->setex(session_key, session_data, expiry_seconds)`
5. 记录会话创建日志

#### 步骤2：会话验证和更新
```cpp
// 调用类：SessionManager
// 使用函数：SessionManager::isSessionValid()
bool valid = session_manager_->isSessionValid(session_id);
```

**内部执行序列**：
1. 从Redis获取会话数据：`auto session_data = redis_pool_->get(session_key)`
2. 检查会话是否存在和活跃
3. 检查会话是否过期
4. 更新最后访问时间：`updateLastAccess(session_id)`
5. 返回验证结果

#### 步骤3：会话清理
**执行序列**：
1. 定期清理过期会话：`cleanupExpiredSessions()`
2. 用户主动登出：`destroySession(session_id)`
3. 强制清理用户所有会话：`destroyAllUserSessions(user_id)`

**结果**：会话生命周期被正确管理

## 密码管理流程

### 1. 密码验证和哈希流程

**业务场景**：安全地处理用户密码

**执行步骤**：

#### 步骤1：密码强度验证
```cpp
// 调用类：PasswordManager
// 使用函数：PasswordManager::validatePassword()
bool valid = password_manager_->validatePassword(password);
```

**内部执行序列**：
1. `PasswordManager::validatePassword(password)`
2. 检查密码长度：`if (password.length() < config_.min_length)`
3. 检查字符要求：
   - 大写字母：`if (config_.require_uppercase && !hasUppercase(password))`
   - 小写字母：`if (config_.require_lowercase && !hasLowercase(password))`
   - 数字：`if (config_.require_numbers && !hasNumbers(password))`
   - 特殊字符：`if (config_.require_special_chars && !hasSpecialChars(password))`
4. 检查常见弱密码：`if (isCommonPassword(password))`
5. 返回验证结果

#### 步骤2：密码哈希处理
```cpp
// 调用类：PasswordManager
// 使用函数：PasswordManager::hashPassword()
std::string hash = password_manager_->hashPassword(password);
```

**内部执行序列**：
1. 生成随机盐值：`std::string salt = generateSalt()`
2. 使用bcrypt算法哈希：`std::string hash = bcrypt::hashpw(password + salt, bcrypt::gensalt())`
3. 组合盐值和哈希：`return salt + "$" + hash`

#### 步骤3：密码验证
```cpp
// 调用类：PasswordManager
// 使用函数：PasswordManager::verifyPassword()
bool valid = password_manager_->verifyPassword(password, stored_hash);
```

**内部执行序列**：
1. 分离盐值和哈希：`auto [salt, hash] = splitSaltAndHash(stored_hash)`
2. 重新计算哈希：`std::string computed_hash = bcrypt::hashpw(password + salt, hash)`
3. 比较哈希值：`return computed_hash == hash`

**结果**：密码安全处理完成

### 2. 密码重置流程

**业务场景**：用户忘记密码时的重置流程

**执行步骤**：

#### 步骤1：密码重置请求
```cpp
// 调用类：AuthService
// 路由：POST /api/v1/auth/forgot-password
// 使用函数：AuthService::forgotPassword()
auto result = this->forgotPassword(email);
```

**内部执行序列**：
1. 验证邮箱格式和存在性
2. 生成重置令牌：`std::string reset_token = generateSecureToken()`
3. 存储重置令牌：`redis_pool_->setex(reset_key, reset_token, 3600)` // 1小时过期
4. 发送重置邮件：`sendPasswordResetEmail(email, reset_token)`
5. 记录重置请求日志

#### 步骤2：密码重置确认
```cpp
// 调用类：AuthService
// 路由：POST /api/v1/auth/reset-password
// 使用函数：AuthService::resetPassword()
auto result = this->resetPassword(reset_token, new_password);
```

**内部执行序列**：
1. 验证重置令牌：`auto email = redis_pool_->get(reset_key)`
2. 验证新密码强度
3. 哈希新密码：`std::string new_hash = password_manager_->hashPassword(new_password)`
4. 更新用户密码：`user_repository_->updatePassword(user_id, new_hash)`
5. 删除重置令牌：`redis_pool_->del(reset_key)`
6. 强制登出所有会话：`session_manager_->destroyAllUserSessions(user_id)`

**结果**：密码重置成功

## 错误处理和安全机制

### 1. 登录失败处理流程

**业务场景**：处理登录失败和账户保护

**执行步骤**：

#### 步骤1：失败尝试记录
```cpp
// 调用类：AuthService
// 使用函数：AuthService::recordLoginAttempt()
recordLoginAttempt(user_id, false);
```

**内部执行序列**：
1. 获取当前失败次数：`int attempts = getFailedAttempts(user_id)`
2. 增加失败计数：`redis_pool_->incr(attempts_key)`
3. 设置过期时间：`redis_pool_->expire(attempts_key, lockout_window)`
4. 检查是否达到锁定阈值：`if (attempts >= max_attempts)`
5. 如果达到阈值，锁定账户：`lockAccount(user_id, lockout_duration)`

#### 步骤2：账户解锁
**执行序列**：
1. 检查锁定状态：`bool locked = isAccountLocked(user_id)`
2. 检查锁定时间：`auto lock_expiry = getLockExpiry(user_id)`
3. 自动解锁：`if (current_time > lock_expiry) unlockAccount(user_id)`
4. 管理员手动解锁：`adminUnlockAccount(user_id, admin_id)`

**结果**：账户安全得到保护

### 2. 令牌安全管理

**业务场景**：确保JWT令牌的安全性

**执行步骤**：

#### 步骤1：令牌黑名单管理
```cpp
// 调用类：JWTManager
// 使用函数：JWTManager::revokeToken()
jwt_manager_->revokeToken(token);
```

**内部执行序列**：
1. 解析令牌获取JTI：`std::string jti = extractJTI(token)`
2. 添加到黑名单：`redis_pool_->setex(blacklist_key, "revoked", token_expiry)`
3. 记录撤销日志

#### 步骤2：令牌验证增强
**执行序列**：
1. 检查令牌黑名单：`bool revoked = redis_pool_->exists(blacklist_key)`
2. 验证令牌签名和时间
3. 检查用户状态和权限
4. 验证会话有效性

**结果**：令牌安全得到保障

## 监控和统计

### 1. 性能监控流程

**业务场景**：监控服务性能和健康状态

**执行步骤**：

#### 步骤1：指标收集
**执行序列**：
1. 请求计数：`stats_.total_requests++`
2. 响应时间：`stats_.response_times.push_back(duration)`
3. 错误统计：`stats_.error_count++`
4. 数据库连接池状态
5. Redis连接状态

#### 步骤2：健康检查
```cpp
// 调用类：AuthService
// 路由：GET /health
// 使用函数：AuthService::healthCheck()
auto result = this->healthCheck();
```

**内部执行序列**：
1. 检查数据库连接：`database_health = user_repository_->healthCheck()`
2. 检查Redis连接：`redis_health = session_manager_->healthCheck()`
3. 检查服务状态：`service_health = isServiceHealthy()`
4. 构建健康报告

**结果**：服务健康状态被监控和报告

## 总结

AuthService系统的业务流程体现了现代认证服务的核心特性：

1. **安全认证**：多层次的身份验证和授权机制
2. **会话管理**：完整的用户会话生命周期管理
3. **密码安全**：强密码策略和安全哈希算法
4. **令牌管理**：JWT令牌的生成、验证和撤销
5. **游戏集成**：专门的游戏登录和数据管理
6. **用户管理**：完整的用户信息和游戏数据管理
7. **安全防护**：登录保护、令牌黑名单、失败重试限制
8. **监控可观测**：全面的性能监控和健康检查

每个业务流程都经过精心设计，确保高安全性、高性能和易扩展性。
