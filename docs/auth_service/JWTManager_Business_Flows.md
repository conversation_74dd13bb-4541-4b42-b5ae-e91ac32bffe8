# JWTManager 业务流程详细分析

## 目录
1. [JWT管理器初始化流程](#JWT管理器初始化流程)
2. [访问令牌生成流程](#访问令牌生成流程)
3. [刷新令牌生成流程](#刷新令牌生成流程)
4. [令牌验证流程](#令牌验证流程)
5. [游戏会话令牌流程](#游戏会话令牌流程)
6. [令牌撤销和黑名单管理](#令牌撤销和黑名单管理)

## JWT管理器初始化流程

### 1. JWT管理器创建和配置加载流程

**业务场景**：创建JWT管理器实例并加载JWT相关配置

**执行步骤**：

#### 步骤1：配置加载
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::JWTManager()
jwt_manager_ = std::make_unique<JWTManager>(config.jwt_config);
```

**内部执行序列**：
1. `JWTManager::JWTManager(jwt_config)` - 构造函数
2. 加载JWT配置参数：
   - `config_ = jwt_config` - 保存配置
   - `secret_key_ = jwt_config.secret_key` - JWT签名密钥
   - `access_token_expiry_ = jwt_config.access_token_expiry_hours` - 访问令牌过期时间（小时）
   - `refresh_token_expiry_ = jwt_config.refresh_token_expiry_days` - 刷新令牌过期时间（天）
   - `issuer_ = jwt_config.issuer` - JWT签发者标识
   - `audience_ = jwt_config.audience` - JWT受众标识
3. 验证配置有效性：
   - 检查密钥长度：`if (secret_key_.length() < 32)`
   - 验证过期时间：`if (access_token_expiry_ <= 0 || refresh_token_expiry_ <= 0)`
   - 验证签发者和受众：`if (issuer_.empty() || audience_.empty())`
4. 初始化JWT库：`jwt::create()` - 准备JWT创建器
5. 记录初始化日志：`LOG_INFO("JWTManager initialized with issuer: " + issuer_)`

**结果**：JWT管理器初始化完成，准备生成和验证令牌

## 访问令牌生成流程

### 1. 用户访问令牌生成流程

**业务场景**：为已认证用户生成访问令牌

**执行步骤**：

#### 步骤1：用户信息准备
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::generateAccessToken()
std::string access_token = jwt_manager_->generateAccessToken(user_info);
```

**内部执行序列**：
1. `JWTManager::generateAccessToken(user_info)`
2. 生成唯一令牌ID：`std::string jti = generateUUID()`
3. 计算过期时间：
   ```cpp
   auto now = std::chrono::system_clock::now();
   auto expiry = now + std::chrono::hours(config_.access_token_expiry_hours);
   auto iat = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
   auto exp = std::chrono::duration_cast<std::chrono::seconds>(expiry.time_since_epoch()).count();
   ```

#### 步骤2：JWT载荷构建
**执行序列**：
1. 构建标准声明（Claims）：
   ```cpp
   auto token = jwt::create()
       .set_issuer(config_.issuer)                    // iss: 签发者
       .set_subject(std::to_string(user_info.user_id)) // sub: 主题（用户ID）
       .set_audience(config_.audience)                 // aud: 受众
       .set_issued_at(std::chrono::system_clock::now()) // iat: 签发时间
       .set_expires_at(expiry)                        // exp: 过期时间
       .set_not_before(std::chrono::system_clock::now()) // nbf: 生效时间
       .set_id(jti);                                  // jti: JWT ID
   ```

2. 添加自定义声明：
   ```cpp
   token.set_payload_claim("username", jwt::claim(user_info.username))
        .set_payload_claim("email", jwt::claim(user_info.email))
        .set_payload_claim("nickname", jwt::claim(user_info.nickname))
        .set_payload_claim("roles", jwt::claim(user_info.roles))
        .set_payload_claim("token_type", jwt::claim("access_token"));
   ```

#### 步骤3：令牌签名和生成
**执行序列**：
1. 使用HMAC SHA256算法签名：`token.sign(jwt::algorithm::hs256{config_.secret_key})`
2. 生成最终令牌字符串：`std::string jwt_token = token.sign(...)`
3. 记录令牌生成日志：`LOG_DEBUG("Access token generated for user: " + user_info.username + ", JTI: " + jti)`
4. 返回生成的令牌：`return jwt_token`

**结果**：访问令牌生成成功

### 2. 令牌元数据记录流程

**业务场景**：记录令牌相关的元数据用于管理

**执行步骤**：

#### 步骤1：令牌元数据存储
**执行序列**：
1. 构建令牌元数据：
   ```cpp
   TokenMetadata metadata;
   metadata.jti = jti;
   metadata.user_id = user_info.user_id;
   metadata.token_type = "access_token";
   metadata.issued_at = iat;
   metadata.expires_at = exp;
   metadata.client_ip = user_info.last_login_ip;
   metadata.user_agent = user_info.last_login_user_agent;
   ```

2. 存储到Redis（可选）：
   ```cpp
   std::string metadata_key = "token_meta:" + jti;
   redis_pool_->setex(metadata_key, metadata.toJson(), token_expiry_seconds);
   ```

**结果**：令牌元数据被记录用于后续管理

## 刷新令牌生成流程

### 1. 刷新令牌生成流程

**业务场景**：生成长期有效的刷新令牌

**执行步骤**：

#### 步骤1：刷新令牌创建
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::generateRefreshToken()
std::string refresh_token = jwt_manager_->generateRefreshToken(user_info);
```

**内部执行序列**：
1. `JWTManager::generateRefreshToken(user_info)`
2. 生成刷新令牌ID：`std::string refresh_jti = generateUUID()`
3. 计算长期过期时间：
   ```cpp
   auto refresh_expiry = now + std::chrono::hours(24 * config_.refresh_token_expiry_days);
   auto refresh_exp = std::chrono::duration_cast<std::chrono::seconds>(refresh_expiry.time_since_epoch()).count();
   ```

#### 步骤2：刷新令牌载荷构建
**执行序列**：
1. 构建刷新令牌载荷：
   ```cpp
   auto refresh_token = jwt::create()
       .set_issuer(config_.issuer)
       .set_subject(std::to_string(user_info.user_id))
       .set_audience(config_.audience)
       .set_issued_at(std::chrono::system_clock::now())
       .set_expires_at(refresh_expiry)
       .set_id(refresh_jti)
       .set_payload_claim("token_type", jwt::claim("refresh_token"))
       .set_payload_claim("username", jwt::claim(user_info.username));
   ```

2. 签名生成：`refresh_token.sign(jwt::algorithm::hs256{config_.secret_key})`

#### 步骤3：刷新令牌存储
**执行序列**：
1. 存储刷新令牌到数据库：
   ```cpp
   RefreshTokenInfo token_info;
   token_info.jti = refresh_jti;
   token_info.user_id = user_info.user_id;
   token_info.expires_at = refresh_expiry;
   token_info.is_active = true;
   user_repository_->saveRefreshToken(token_info);
   ```

2. 清理用户旧的刷新令牌（可选）：`cleanupOldRefreshTokens(user_info.user_id)`

**结果**：刷新令牌生成并存储成功

## 令牌验证流程

### 1. 访问令牌验证流程

**业务场景**：验证客户端提供的访问令牌

**执行步骤**：

#### 步骤1：令牌格式验证
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::verifyAccessToken()
auto result = jwt_manager_->verifyAccessToken(token);
```

**内部执行序列**：
1. `JWTManager::verifyAccessToken(token)`
2. 检查令牌格式：`if (token.empty() || std::count(token.begin(), token.end(), '.') != 2)`
3. 解析JWT结构：
   ```cpp
   try {
       auto decoded_token = jwt::decode(token);
   } catch (const std::exception& e) {
       return TokenVerificationResult::invalid("Invalid token format");
   }
   ```

#### 步骤2：令牌签名验证
**执行序列**：
1. 验证签名：
   ```cpp
   auto verifier = jwt::verify()
       .allow_algorithm(jwt::algorithm::hs256{config_.secret_key})
       .with_issuer(config_.issuer)
       .with_audience(config_.audience);
   
   try {
       verifier.verify(decoded_token);
   } catch (const std::exception& e) {
       return TokenVerificationResult::invalid("Signature verification failed");
   }
   ```

#### 步骤3：令牌内容验证
**执行序列**：
1. 检查令牌类型：
   ```cpp
   auto token_type = decoded_token.get_payload_claim("token_type").as_string();
   if (token_type != "access_token") {
       return TokenVerificationResult::invalid("Invalid token type");
   }
   ```

2. 检查过期时间：
   ```cpp
   auto exp = decoded_token.get_expires_at();
   if (exp < std::chrono::system_clock::now()) {
       return TokenVerificationResult::expired("Token has expired");
   }
   ```

3. 检查生效时间：
   ```cpp
   auto nbf = decoded_token.get_not_before();
   if (nbf > std::chrono::system_clock::now()) {
       return TokenVerificationResult::invalid("Token not yet valid");
   }
   ```

#### 步骤4：黑名单检查
**执行序列**：
1. 提取JTI：`std::string jti = decoded_token.get_id()`
2. 检查黑名单：
   ```cpp
   std::string blacklist_key = "blacklist:" + jti;
   bool is_revoked = redis_pool_->exists(blacklist_key);
   if (is_revoked) {
       return TokenVerificationResult::revoked("Token has been revoked");
   }
   ```

#### 步骤5：用户信息提取
**执行序列**：
1. 提取用户声明：
   ```cpp
   TokenVerificationResult result;
   result.success = true;
   result.user_id = std::stoll(decoded_token.get_subject());
   result.username = decoded_token.get_payload_claim("username").as_string();
   result.email = decoded_token.get_payload_claim("email").as_string();
   result.nickname = decoded_token.get_payload_claim("nickname").as_string();
   result.roles = decoded_token.get_payload_claim("roles").as_array();
   result.jti = jti;
   ```

**结果**：令牌验证完成，返回验证结果和用户信息

### 2. 刷新令牌验证流程

**业务场景**：验证刷新令牌并生成新的访问令牌

**执行步骤**：

#### 步骤1：刷新令牌验证
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::verifyRefreshToken()
auto result = jwt_manager_->verifyRefreshToken(refresh_token);
```

**内部执行序列**：
1. 基本JWT验证（类似访问令牌）
2. 检查令牌类型：`token_type == "refresh_token"`
3. 数据库验证：
   ```cpp
   std::string jti = decoded_token.get_id();
   auto token_info = user_repository_->findRefreshToken(jti);
   if (!token_info.has_value() || !token_info->is_active) {
       return TokenVerificationResult::invalid("Refresh token not found or inactive");
   }
   ```

#### 步骤2：新访问令牌生成
**执行序列**：
1. 获取用户信息：`auto user_info = user_repository_->findById(result.user_id)`
2. 生成新访问令牌：`std::string new_access_token = generateAccessToken(user_info.value())`
3. 可选：轮换刷新令牌：
   ```cpp
   if (config_.rotate_refresh_tokens) {
       // 撤销旧刷新令牌
       user_repository_->revokeRefreshToken(jti);
       // 生成新刷新令牌
       std::string new_refresh_token = generateRefreshToken(user_info.value());
   }
   ```

**结果**：刷新令牌验证成功，生成新的访问令牌

## 游戏会话令牌流程

### 1. 游戏会话令牌生成流程

**业务场景**：为游戏登录生成专用的会话令牌

**执行步骤**：

#### 步骤1：游戏会话令牌创建
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::generateGameSessionToken()
std::string game_token = jwt_manager_->generateGameSessionToken(user_info, game_type, server_id);
```

**内部执行序列**：
1. `JWTManager::generateGameSessionToken(user_info, game_type, server_id)`
2. 生成游戏会话ID：`std::string session_jti = generateUUID()`
3. 计算游戏会话过期时间：
   ```cpp
   auto game_expiry = now + std::chrono::hours(config_.game_session_expiry_hours); // 默认2小时
   ```

#### 步骤2：游戏令牌载荷构建
**执行序列**：
1. 构建游戏专用载荷：
   ```cpp
   auto game_token = jwt::create()
       .set_issuer(config_.issuer)
       .set_subject(std::to_string(user_info.user_id))
       .set_audience(config_.audience)
       .set_issued_at(std::chrono::system_clock::now())
       .set_expires_at(game_expiry)
       .set_id(session_jti)
       .set_payload_claim("token_type", jwt::claim("game_session"))
       .set_payload_claim("username", jwt::claim(user_info.username))
       .set_payload_claim("game_type", jwt::claim(static_cast<int>(game_type)))
       .set_payload_claim("server_id", jwt::claim(server_id))
       .set_payload_claim("session_id", jwt::claim(session_jti));
   ```

2. 签名生成：`game_token.sign(jwt::algorithm::hs256{config_.secret_key})`

#### 步骤3：游戏会话记录
**执行序列**：
1. 记录游戏会话：
   ```cpp
   GameSessionInfo session_info;
   session_info.session_id = session_jti;
   session_info.user_id = user_info.user_id;
   session_info.game_type = game_type;
   session_info.server_id = server_id;
   session_info.created_at = std::chrono::system_clock::now();
   session_info.expires_at = game_expiry;
   user_repository_->saveGameSession(session_info);
   ```

**结果**：游戏会话令牌生成成功

## 令牌撤销和黑名单管理

### 1. 令牌撤销流程

**业务场景**：主动撤销已发布的令牌

**执行步骤**：

#### 步骤1：单个令牌撤销
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::revokeToken()
bool success = jwt_manager_->revokeToken(token);
```

**内部执行序列**：
1. `JWTManager::revokeToken(token)`
2. 解析令牌获取JTI：
   ```cpp
   auto decoded_token = jwt::decode(token);
   std::string jti = decoded_token.get_id();
   auto exp = decoded_token.get_expires_at();
   ```

3. 添加到黑名单：
   ```cpp
   std::string blacklist_key = "blacklist:" + jti;
   auto expiry_seconds = std::chrono::duration_cast<std::chrono::seconds>(exp - std::chrono::system_clock::now()).count();
   redis_pool_->setex(blacklist_key, "revoked", expiry_seconds);
   ```

4. 记录撤销日志：`LOG_INFO("Token revoked: " + jti)`

#### 步骤2：用户所有令牌撤销
```cpp
// 调用类：AuthService
// 使用函数：JWTManager::revokeAllUserTokens()
bool success = jwt_manager_->revokeAllUserTokens(user_id);
```

**内部执行序列**：
1. 获取用户所有活跃令牌：`auto tokens = user_repository_->findActiveTokens(user_id)`
2. 批量撤销：
   ```cpp
   for (const auto& token_info : tokens) {
       std::string blacklist_key = "blacklist:" + token_info.jti;
       redis_pool_->setex(blacklist_key, "revoked", token_info.remaining_seconds);
   }
   ```

3. 撤销刷新令牌：`user_repository_->revokeAllRefreshTokens(user_id)`

#### 步骤3：黑名单清理
**执行序列**：
1. 定期清理过期的黑名单条目（Redis自动过期）
2. 统计黑名单大小：`redis_pool_->dbsize()`
3. 监控黑名单性能影响

**结果**：令牌撤销和黑名单管理完成

### 2. 令牌安全增强

**业务场景**：增强令牌的安全性

**执行步骤**：

#### 步骤1：令牌绑定验证
**执行序列**：
1. IP地址绑定：验证令牌使用的IP是否与签发时一致
2. User-Agent绑定：验证浏览器指纹
3. 设备指纹验证：检查设备特征

#### 步骤2：令牌使用频率监控
**执行序列**：
1. 记录令牌使用：`redis_pool_->incr("token_usage:" + jti)`
2. 检测异常使用：高频率或异地使用
3. 自动撤销可疑令牌

**结果**：令牌安全性得到增强

## 总结

JWTManager系统的业务流程体现了现代JWT令牌管理的核心特性：

1. **安全生成**：使用强密钥和标准算法生成令牌
2. **完整验证**：多层次的令牌验证机制
3. **灵活管理**：支持不同类型令牌的生成和管理
4. **安全撤销**：完善的令牌撤销和黑名单机制
5. **游戏集成**：专门的游戏会话令牌支持
6. **性能优化**：Redis缓存和高效的验证算法
7. **安全增强**：令牌绑定和使用监控

每个业务流程都经过精心设计，确保JWT令牌的安全性、可靠性和高性能。
