# PasswordManager 业务流程详细分析

## 目录
1. [密码管理器初始化流程](#密码管理器初始化流程)
2. [密码强度验证流程](#密码强度验证流程)
3. [密码哈希处理流程](#密码哈希处理流程)
4. [密码验证流程](#密码验证流程)
5. [密码安全策略流程](#密码安全策略流程)
6. [密码历史管理流程](#密码历史管理流程)

## 密码管理器初始化流程

### 1. 密码管理器创建和配置加载流程

**业务场景**：创建密码管理器实例并加载安全配置

**执行步骤**：

#### 步骤1：管理器实例创建
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::PasswordManager()
password_manager_ = std::make_unique<PasswordManager>(config.security_config);
```

**内部执行序列**：
1. `PasswordManager::PasswordManager(security_config)` - 构造函数
2. 保存安全配置：`config_ = security_config`
3. 加载密码策略参数：
   - `min_length_ = security_config.password_min_length` - 最小长度（默认8）
   - `max_length_ = security_config.password_max_length` - 最大长度（默认128）
   - `require_uppercase_ = security_config.password_require_uppercase` - 需要大写字母
   - `require_lowercase_ = security_config.password_require_lowercase` - 需要小写字母
   - `require_numbers_ = security_config.password_require_numbers` - 需要数字
   - `require_special_chars_ = security_config.password_require_special_chars` - 需要特殊字符
   - `min_special_chars_ = security_config.min_special_chars` - 最少特殊字符数
   - `max_repeated_chars_ = security_config.max_repeated_chars` - 最大重复字符数

#### 步骤2：哈希算法配置
**执行序列**：
1. 配置bcrypt参数：
   ```cpp
   bcrypt_rounds_ = security_config.bcrypt_rounds; // 默认12轮
   if (bcrypt_rounds_ < 10 || bcrypt_rounds_ > 15) {
       LOG_WARNING("bcrypt rounds should be between 10-15, using default 12");
       bcrypt_rounds_ = 12;
   }
   ```

2. 初始化盐值生成器：
   ```cpp
   salt_length_ = security_config.salt_length; // 默认16字节
   random_generator_.seed(std::chrono::system_clock::now().time_since_epoch().count());
   ```

3. 加载弱密码字典：
   ```cpp
   loadCommonPasswordDictionary();
   // 加载常见弱密码列表：123456, password, qwerty等
   ```

#### 步骤3：验证配置有效性
**执行序列**：
1. 验证配置参数：
   ```cpp
   if (min_length_ < 6 || min_length_ > max_length_) {
       throw std::invalid_argument("Invalid password length configuration");
   }
   
   if (!require_uppercase_ && !require_lowercase_ && !require_numbers_ && !require_special_chars_) {
       LOG_WARNING("No character requirements specified, passwords may be weak");
   }
   ```

2. 记录初始化日志：
   ```cpp
   LOG_INFO("PasswordManager initialized with policy: min_length=" + 
            std::to_string(min_length_) + ", bcrypt_rounds=" + std::to_string(bcrypt_rounds_));
   ```

**结果**：密码管理器初始化完成，安全策略配置生效

### 2. 弱密码字典加载流程

**业务场景**：加载常见弱密码字典用于密码强度检查

**执行步骤**：

#### 步骤1：字典文件加载
```cpp
// 内部调用：PasswordManager::loadCommonPasswordDictionary()
loadCommonPasswordDictionary();
```

**内部执行序列**：
1. 加载内置弱密码列表：
   ```cpp
   common_passwords_ = {
       "123456", "password", "123456789", "12345678", "12345",
       "1234567", "1234567890", "qwerty", "abc123", "111111",
       "123123", "admin", "letmein", "welcome", "monkey",
       "dragon", "pass", "master", "hello", "freedom"
       // ... 更多常见弱密码
   };
   ```

2. 创建快速查找结构：
   ```cpp
   common_passwords_set_.clear();
   for (const auto& password : common_passwords_) {
       common_passwords_set_.insert(password);
       // 同时添加常见变体
       common_passwords_set_.insert(password + "123");
       common_passwords_set_.insert(password + "!");
   }
   ```

3. 加载外部字典文件（可选）：
   ```cpp
   if (config_.external_dictionary_path && 
       std::filesystem::exists(config_.external_dictionary_path)) {
       loadExternalDictionary(config_.external_dictionary_path);
   }
   ```

**结果**：弱密码字典加载完成

## 密码强度验证流程

### 1. 密码强度综合验证流程

**业务场景**：验证用户提供的密码是否符合安全策略

**执行步骤**：

#### 步骤1：密码强度验证请求
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::validatePassword()
auto result = password_manager_->validatePassword(password);
```

**内部执行序列**：
1. `PasswordManager::validatePassword(password)`
2. 基本长度检查：
   ```cpp
   if (password.length() < min_length_) {
       return PasswordValidationResult::invalid(
           "Password must be at least " + std::to_string(min_length_) + " characters long"
       );
   }
   
   if (password.length() > max_length_) {
       return PasswordValidationResult::invalid(
           "Password must not exceed " + std::to_string(max_length_) + " characters"
       );
   }
   ```

#### 步骤2：字符要求验证
**执行序列**：
1. 大写字母检查：
   ```cpp
   if (require_uppercase_) {
       bool has_uppercase = std::any_of(password.begin(), password.end(), 
                                       [](char c) { return std::isupper(c); });
       if (!has_uppercase) {
           return PasswordValidationResult::invalid("Password must contain at least one uppercase letter");
       }
   }
   ```

2. 小写字母检查：
   ```cpp
   if (require_lowercase_) {
       bool has_lowercase = std::any_of(password.begin(), password.end(), 
                                       [](char c) { return std::islower(c); });
       if (!has_lowercase) {
           return PasswordValidationResult::invalid("Password must contain at least one lowercase letter");
       }
   }
   ```

3. 数字检查：
   ```cpp
   if (require_numbers_) {
       bool has_digit = std::any_of(password.begin(), password.end(), 
                                   [](char c) { return std::isdigit(c); });
       if (!has_digit) {
           return PasswordValidationResult::invalid("Password must contain at least one digit");
       }
   }
   ```

4. 特殊字符检查：
   ```cpp
   if (require_special_chars_) {
       const std::string special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
       int special_count = 0;
       for (char c : password) {
           if (special_chars.find(c) != std::string::npos) {
               special_count++;
           }
       }
       
       if (special_count < min_special_chars_) {
           return PasswordValidationResult::invalid(
               "Password must contain at least " + std::to_string(min_special_chars_) + " special characters"
           );
       }
   }
   ```

#### 步骤3：高级安全检查
**执行序列**：
1. 重复字符检查：
   ```cpp
   if (max_repeated_chars_ > 0) {
       int max_consecutive = getMaxConsecutiveChars(password);
       if (max_consecutive > max_repeated_chars_) {
           return PasswordValidationResult::invalid(
               "Password contains too many consecutive identical characters"
           );
       }
   }
   ```

2. 常见模式检查：
   ```cpp
   if (containsCommonPatterns(password)) {
       return PasswordValidationResult::invalid("Password contains common patterns (e.g., 123, abc, qwerty)");
   }
   ```

3. 弱密码字典检查：
   ```cpp
   std::string lowercase_password = toLowercase(password);
   if (common_passwords_set_.count(lowercase_password) > 0) {
       return PasswordValidationResult::invalid("Password is too common and easily guessable");
   }
   ```

#### 步骤4：密码强度评分
**执行序列**：
1. 计算密码强度分数：
   ```cpp
   int strength_score = calculatePasswordStrength(password);
   PasswordStrength strength_level;
   
   if (strength_score >= 80) {
       strength_level = PasswordStrength::VERY_STRONG;
   } else if (strength_score >= 60) {
       strength_level = PasswordStrength::STRONG;
   } else if (strength_score >= 40) {
       strength_level = PasswordStrength::MEDIUM;
   } else if (strength_score >= 20) {
       strength_level = PasswordStrength::WEAK;
   } else {
       strength_level = PasswordStrength::VERY_WEAK;
   }
   ```

2. 构建验证结果：
   ```cpp
   PasswordValidationResult result;
   result.is_valid = true;
   result.strength_score = strength_score;
   result.strength_level = strength_level;
   result.suggestions = generatePasswordSuggestions(password);
   return result;
   ```

**结果**：密码强度验证完成，返回验证结果和强度评分

### 2. 密码强度计算流程

**业务场景**：计算密码的强度分数

**执行步骤**：

#### 步骤1：基础分数计算
```cpp
// 内部调用：PasswordManager::calculatePasswordStrength()
int score = calculatePasswordStrength(password);
```

**内部执行序列**：
1. 长度分数：
   ```cpp
   int length_score = std::min(password.length() * 2, 25); // 最多25分
   ```

2. 字符多样性分数：
   ```cpp
   int diversity_score = 0;
   if (hasUppercase(password)) diversity_score += 10;
   if (hasLowercase(password)) diversity_score += 10;
   if (hasDigits(password)) diversity_score += 10;
   if (hasSpecialChars(password)) diversity_score += 15;
   ```

3. 复杂度分数：
   ```cpp
   int complexity_score = 0;
   int unique_chars = countUniqueCharacters(password);
   complexity_score += std::min(unique_chars * 2, 20);
   
   // 字符分布均匀性
   if (isCharacterDistributionBalanced(password)) {
       complexity_score += 10;
   }
   ```

4. 减分项：
   ```cpp
   int penalty = 0;
   if (containsRepeatedChars(password)) penalty += 10;
   if (containsSequentialChars(password)) penalty += 15;
   if (isCommonPassword(password)) penalty += 25;
   ```

5. 最终分数：
   ```cpp
   int final_score = length_score + diversity_score + complexity_score - penalty;
   return std::max(0, std::min(100, final_score));
   ```

**结果**：密码强度分数计算完成

## 密码哈希处理流程

### 1. 密码哈希生成流程

**业务场景**：将明文密码转换为安全的哈希值

**执行步骤**：

#### 步骤1：密码哈希请求
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::hashPassword()
std::string hash = password_manager_->hashPassword(password);
```

**内部执行序列**：
1. `PasswordManager::hashPassword(password)`
2. 生成随机盐值：
   ```cpp
   std::string salt = generateSalt();
   // 生成16字节随机盐值
   ```

3. 使用bcrypt算法哈希：
   ```cpp
   std::string bcrypt_salt = bcrypt::gensalt(bcrypt_rounds_);
   std::string hash = bcrypt::hashpw(password, bcrypt_salt);
   ```

4. 构建最终哈希字符串：
   ```cpp
   // 格式：$2b$rounds$salt$hash
   std::string final_hash = hash; // bcrypt已包含所有信息
   ```

5. 记录哈希操作日志：
   ```cpp
   LOG_DEBUG("Password hashed with " + std::to_string(bcrypt_rounds_) + " rounds");
   ```

**结果**：密码哈希生成完成

### 2. 盐值生成流程

**业务场景**：生成密码哈希所需的随机盐值

**执行步骤**：

#### 步骤1：随机盐值生成
```cpp
// 内部调用：PasswordManager::generateSalt()
std::string salt = generateSalt();
```

**内部执行序列**：
1. 使用加密安全的随机数生成器：
   ```cpp
   std::random_device rd;
   std::mt19937 gen(rd());
   std::uniform_int_distribution<> dis(0, 255);
   ```

2. 生成随机字节：
   ```cpp
   std::vector<unsigned char> salt_bytes(salt_length_);
   for (size_t i = 0; i < salt_length_; ++i) {
       salt_bytes[i] = static_cast<unsigned char>(dis(gen));
   }
   ```

3. 转换为Base64编码：
   ```cpp
   std::string salt = base64_encode(salt_bytes.data(), salt_bytes.size());
   ```

**结果**：随机盐值生成完成

## 密码验证流程

### 1. 密码验证流程

**业务场景**：验证用户输入的密码是否与存储的哈希匹配

**执行步骤**：

#### 步骤1：密码验证请求
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::verifyPassword()
bool valid = password_manager_->verifyPassword(password, stored_hash);
```

**内部执行序列**：
1. `PasswordManager::verifyPassword(password, stored_hash)`
2. 验证哈希格式：
   ```cpp
   if (stored_hash.empty() || stored_hash.length() < 60) {
       LOG_ERROR("Invalid hash format");
       return false;
   }
   ```

3. 使用bcrypt验证：
   ```cpp
   try {
       bool is_valid = bcrypt::checkpw(password, stored_hash);
       return is_valid;
   } catch (const std::exception& e) {
       LOG_ERROR("Password verification failed: " + std::string(e.what()));
       return false;
   }
   ```

4. 记录验证结果：
   ```cpp
   if (is_valid) {
       LOG_DEBUG("Password verification successful");
   } else {
       LOG_DEBUG("Password verification failed");
   }
   ```

**结果**：密码验证完成，返回验证结果

### 2. 哈希升级检查流程

**业务场景**：检查是否需要升级旧的密码哈希

**执行步骤**：

#### 步骤1：哈希升级检查
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::needsRehash()
bool needs_upgrade = password_manager_->needsRehash(stored_hash);
```

**内部执行序列**：
1. 解析哈希信息：
   ```cpp
   auto hash_info = parseHashInfo(stored_hash);
   ```

2. 检查算法版本：
   ```cpp
   if (hash_info.algorithm != "2b") { // 不是最新的bcrypt版本
       return true;
   }
   ```

3. 检查轮数：
   ```cpp
   if (hash_info.rounds < bcrypt_rounds_) {
       return true; // 需要更多轮数
   }
   ```

4. 检查盐值长度：
   ```cpp
   if (hash_info.salt_length < salt_length_) {
       return true; // 需要更长的盐值
   }
   ```

#### 步骤2：哈希升级执行
```cpp
// 如果需要升级
if (needs_upgrade && password_verified) {
    std::string new_hash = hashPassword(password);
    // 更新数据库中的哈希值
    user_repository_->updatePasswordHash(user_id, new_hash);
}
```

**结果**：哈希升级检查和执行完成

## 密码安全策略流程

### 1. 密码历史检查流程

**业务场景**：防止用户重复使用最近的密码

**执行步骤**：

#### 步骤1：密码历史验证
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::checkPasswordHistory()
bool is_reused = password_manager_->checkPasswordHistory(user_id, new_password);
```

**内部执行序列**：
1. 获取用户密码历史：
   ```cpp
   auto password_history = user_repository_->getPasswordHistory(user_id, config_.password_history_count);
   ```

2. 验证新密码是否重复：
   ```cpp
   for (const auto& old_hash : password_history) {
       if (verifyPassword(new_password, old_hash)) {
           return true; // 密码重复使用
       }
   }
   return false;
   ```

#### 步骤2：密码历史更新
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::updatePasswordHistory()
password_manager_->updatePasswordHistory(user_id, new_password_hash);
```

**内部执行序列**：
1. 添加新密码哈希到历史：
   ```cpp
   user_repository_->addPasswordToHistory(user_id, new_password_hash);
   ```

2. 清理过期的密码历史：
   ```cpp
   user_repository_->cleanupPasswordHistory(user_id, config_.password_history_count);
   ```

**结果**：密码历史检查和更新完成

### 2. 密码过期检查流程

**业务场景**：检查密码是否需要定期更换

**执行步骤**：

#### 步骤1：密码年龄检查
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::isPasswordExpired()
bool expired = password_manager_->isPasswordExpired(user_id);
```

**内部执行序列**：
1. 获取密码最后更改时间：
   ```cpp
   auto last_changed = user_repository_->getPasswordLastChanged(user_id);
   ```

2. 计算密码年龄：
   ```cpp
   auto now = std::chrono::system_clock::now();
   auto password_age = now - last_changed;
   auto max_age = std::chrono::days(config_.password_max_age_days);
   
   return password_age > max_age;
   ```

#### 步骤2：密码过期通知
```cpp
// 如果密码即将过期
if (isPasswordNearExpiry(user_id)) {
    sendPasswordExpiryNotification(user_id);
}
```

**结果**：密码过期检查完成

### 3. 账户锁定策略流程

**业务场景**：实施账户锁定策略防止暴力破解

**执行步骤**：

#### 步骤1：失败尝试记录
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::recordFailedAttempt()
password_manager_->recordFailedAttempt(user_id, client_ip);
```

**内部执行序列**：
1. 记录失败尝试：
   ```cpp
   std::string attempts_key = "failed_attempts:" + std::to_string(user_id);
   redis_pool_->incr(attempts_key);
   redis_pool_->expire(attempts_key, config_.lockout_window_minutes * 60);
   ```

2. 检查是否达到锁定阈值：
   ```cpp
   int attempts = redis_pool_->get(attempts_key);
   if (attempts >= config_.max_failed_attempts) {
       lockAccount(user_id);
   }
   ```

#### 步骤2：账户锁定执行
```cpp
// 内部调用：PasswordManager::lockAccount()
lockAccount(user_id);
```

**内部执行序列**：
1. 设置账户锁定状态：
   ```cpp
   std::string lock_key = "account_locked:" + std::to_string(user_id);
   auto lock_expiry = std::chrono::system_clock::now() + 
                     std::chrono::minutes(config_.lockout_duration_minutes);
   redis_pool_->setex(lock_key, "locked", 
                     std::chrono::duration_cast<std::chrono::seconds>(
                         lock_expiry - std::chrono::system_clock::now()).count());
   ```

2. 记录锁定事件：
   ```cpp
   LOG_WARNING("Account locked due to too many failed attempts: " + std::to_string(user_id));
   ```

3. 发送安全通知：
   ```cpp
   sendAccountLockNotification(user_id);
   ```

**结果**：账户锁定策略执行完成

## 密码历史管理流程

### 1. 密码变更流程

**业务场景**：处理用户密码变更请求

**执行步骤**：

#### 步骤1：密码变更验证
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::changePassword()
auto result = password_manager_->changePassword(user_id, old_password, new_password);
```

**内部执行序列**：
1. 验证旧密码：
   ```cpp
   auto user_info = user_repository_->findById(user_id);
   if (!verifyPassword(old_password, user_info->password_hash)) {
       return PasswordChangeResult::invalid("Current password is incorrect");
   }
   ```

2. 验证新密码强度：
   ```cpp
   auto validation_result = validatePassword(new_password);
   if (!validation_result.is_valid) {
       return PasswordChangeResult::invalid(validation_result.error_message);
   }
   ```

3. 检查密码历史：
   ```cpp
   if (checkPasswordHistory(user_id, new_password)) {
       return PasswordChangeResult::invalid("Cannot reuse recent passwords");
   }
   ```

#### 步骤2：密码更新执行
**执行序列**：
1. 生成新密码哈希：
   ```cpp
   std::string new_hash = hashPassword(new_password);
   ```

2. 更新数据库：
   ```cpp
   user_repository_->updatePassword(user_id, new_hash);
   ```

3. 更新密码历史：
   ```cpp
   updatePasswordHistory(user_id, new_hash);
   ```

4. 撤销所有用户会话：
   ```cpp
   session_manager_->destroyAllUserSessions(user_id);
   ```

5. 记录密码变更事件：
   ```cpp
   LOG_INFO("Password changed for user: " + std::to_string(user_id));
   ```

**结果**：密码变更完成

### 2. 密码重置流程

**业务场景**：处理忘记密码的重置请求

**执行步骤**：

#### 步骤1：重置令牌生成
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::generateResetToken()
std::string reset_token = password_manager_->generateResetToken(email);
```

**内部执行序列**：
1. 验证邮箱存在：
   ```cpp
   auto user_opt = user_repository_->findByEmail(email);
   if (!user_opt.has_value()) {
       // 为了安全，不透露邮箱是否存在
       return "Reset instructions sent if email exists";
   }
   ```

2. 生成安全令牌：
   ```cpp
   std::string reset_token = generateSecureToken(32);
   ```

3. 存储重置令牌：
   ```cpp
   std::string token_key = "password_reset:" + reset_token;
   redis_pool_->setex(token_key, std::to_string(user_opt->user_id), 3600); // 1小时过期
   ```

#### 步骤2：密码重置执行
```cpp
// 调用类：AuthService
// 使用函数：PasswordManager::resetPassword()
auto result = password_manager_->resetPassword(reset_token, new_password);
```

**内部执行序列**：
1. 验证重置令牌：
   ```cpp
   std::string token_key = "password_reset:" + reset_token;
   auto user_id_str = redis_pool_->get(token_key);
   if (user_id_str.empty()) {
       return PasswordResetResult::invalid("Invalid or expired reset token");
   }
   ```

2. 验证新密码：
   ```cpp
   auto validation_result = validatePassword(new_password);
   if (!validation_result.is_valid) {
       return PasswordResetResult::invalid(validation_result.error_message);
   }
   ```

3. 更新密码：
   ```cpp
   uint64_t user_id = std::stoull(user_id_str);
   std::string new_hash = hashPassword(new_password);
   user_repository_->updatePassword(user_id, new_hash);
   ```

4. 清理重置令牌：
   ```cpp
   redis_pool_->del(token_key);
   ```

5. 撤销所有会话：
   ```cpp
   session_manager_->destroyAllUserSessions(user_id);
   ```

**结果**：密码重置完成

## 总结

PasswordManager系统的业务流程体现了现代密码管理的核心特性：

1. **强度验证**：全面的密码强度检查和评分机制
2. **安全哈希**：使用bcrypt算法的安全密码哈希
3. **历史管理**：防止密码重复使用的历史记录
4. **安全策略**：账户锁定、密码过期等安全策略
5. **升级支持**：自动检测和升级旧的密码哈希
6. **重置机制**：安全的密码重置和找回流程
7. **审计日志**：完整的密码操作审计记录
8. **性能优化**：高效的密码验证和哈希算法

每个业务流程都经过精心设计，确保密码管理的安全性、可靠性和用户友好性。
