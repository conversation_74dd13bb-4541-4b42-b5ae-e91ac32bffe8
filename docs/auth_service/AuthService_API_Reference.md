# AuthService API 接口文档

## 目录
1. [API概览](#API概览)
2. [认证接口](#认证接口)
3. [用户管理接口](#用户管理接口)
4. [游戏相关接口](#游戏相关接口)
5. [系统接口](#系统接口)
6. [错误码说明](#错误码说明)

## API概览

### 基础信息

- **Base URL**: `http://localhost:8008/api/v1`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer <PERSON>ken (JWT)

### 通用请求头

```http
Content-Type: application/json
Authorization: Bearer <access_token>  # 需要认证的接口
User-Agent: <client_info>
```

### 通用响应格式

**成功响应**:
```json
{
  "success": true,
  "data": {
    // 具体数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**错误响应**:
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "请求参数错误",
    "details": "具体错误信息"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 认证接口

### 1. 用户注册

**接口**: `POST /auth/register`

**描述**: 注册新用户账户

**请求参数**:
```json
{
  "username": "testuser",      // 可选，用户名 (3-50字符)
  "email": "<EMAIL>", // 可选，邮箱地址
  "password": "SecurePass123!", // 必需，密码 (8-128字符)
  "nickname": "测试用户",       // 可选，昵称 (1-100字符)
  "phone": "+86-13800138000",  // 可选，手机号
  "birth_date": "1990-01-01",  // 可选，生日 (YYYY-MM-DD)
  "gender": "male"             // 可选，性别 (male/female/other)
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user_id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "created_at": "2024-01-01T12:00:00Z",
    "is_verified": false
  },
  "message": "注册成功"
}
```

### 2. 用户登录

**接口**: `POST /auth/login`

**描述**: 用户登录认证

**请求参数**:
```json
{
  "username": "testuser",        // 用户名或邮箱
  "password": "SecurePass123!",  // 密码
  "remember_me": true           // 可选，是否记住登录状态
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user_info": {
      "user_id": 12345,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "last_login_at": "2024-01-01T12:00:00Z"
    }
  },
  "message": "登录成功"
}
```

### 3. 令牌刷新

**接口**: `POST /auth/refresh`

**描述**: 使用刷新令牌获取新的访问令牌

**请求参数**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  },
  "message": "令牌刷新成功"
}
```

### 4. 令牌验证

**接口**: `POST /auth/verify`

**描述**: 验证访问令牌的有效性

**请求头**:
```http
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user_id": 12345,
    "username": "testuser",
    "expires_at": "2024-01-01T13:00:00Z"
  },
  "message": "令牌有效"
}
```

### 5. 用户登出

**接口**: `POST /auth/logout`

**描述**: 用户登出，撤销当前令牌

**请求头**:
```http
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {},
  "message": "登出成功"
}
```

### 6. 忘记密码

**接口**: `POST /auth/forgot-password`

**描述**: 发送密码重置邮件

**请求参数**:
```json
{
  "email": "<EMAIL>"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {},
  "message": "密码重置邮件已发送"
}
```

### 7. 重置密码

**接口**: `POST /auth/reset-password`

**描述**: 使用重置令牌重置密码

**请求参数**:
```json
{
  "reset_token": "abc123def456",
  "new_password": "NewSecurePass123!"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {},
  "message": "密码重置成功"
}
```

## 用户管理接口

### 1. 获取用户信息

**接口**: `GET /user/profile`

**描述**: 获取当前用户的详细信息

**请求头**:
```http
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user_id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "avatar_url": "https://example.com/avatar.jpg",
    "phone": "+86-13800138000",
    "birth_date": "1990-01-01",
    "gender": "male",
    "country": "CN",
    "timezone": "Asia/Shanghai",
    "language": "zh-CN",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "last_login_at": "2024-01-01T12:00:00Z",
    "is_verified": true
  },
  "message": "获取成功"
}
```

### 2. 更新用户信息

**接口**: `PUT /user/profile`

**描述**: 更新当前用户的信息

**请求头**:
```http
Authorization: Bearer <access_token>
```

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "phone": "+86-13900139000",
  "birth_date": "1990-01-01",
  "gender": "female",
  "country": "CN",
  "timezone": "Asia/Shanghai",
  "language": "en-US"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user_id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "新昵称",
    "updated_at": "2024-01-01T13:00:00Z"
  },
  "message": "更新成功"
}
```

### 3. 修改密码

**接口**: `PUT /user/password`

**描述**: 修改当前用户的密码

**请求头**:
```http
Authorization: Bearer <access_token>
```

**请求参数**:
```json
{
  "current_password": "OldPassword123!",
  "new_password": "NewPassword123!"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {},
  "message": "密码修改成功"
}
```

### 4. 获取游戏数据

**接口**: `GET /user/game-data`

**描述**: 获取用户的游戏数据

**请求头**:
```http
Authorization: Bearer <access_token>
```

**查询参数**:
- `game_type`: 游戏类型 (可选，默认返回所有游戏数据)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "snake": {
      "level": 15,
      "experience": 12500,
      "coins": 5000,
      "gems": 50,
      "total_games": 100,
      "wins": 75,
      "losses": 20,
      "draws": 5,
      "best_score": 98765,
      "achievements": ["first_win", "level_10", "score_10000"],
      "updated_at": "2024-01-01T12:00:00Z"
    }
  },
  "message": "获取成功"
}
```

### 5. 更新游戏数据

**接口**: `PUT /user/game-data`

**描述**: 更新用户的游戏数据

**请求头**:
```http
Authorization: Bearer <access_token>
```

**请求参数**:
```json
{
  "game_type": "snake",
  "level": 16,
  "experience": 13000,
  "coins": 5100,
  "gems": 52,
  "total_games": 101,
  "wins": 76,
  "best_score": 99000,
  "achievements": ["first_win", "level_10", "score_10000", "level_15"]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "level": 16,
    "experience": 13000,
    "coins": 5100,
    "gems": 52,
    "updated_at": "2024-01-01T13:00:00Z"
  },
  "message": "更新成功"
}
```

## 游戏相关接口

### 1. 游戏登录

**接口**: `POST /game/login`

**描述**: 游戏登录，获取游戏服务器信息

**请求头**:
```http
Authorization: Bearer <access_token>
```

**请求参数**:
```json
{
  "game_type": "snake",           // 可选，游戏类型
  "preferred_server_region": "us-west"  // 可选，首选服务器区域
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "game_type": "snake",
    "game_server": {
      "server_id": "snake-server-001",
      "host": "game.example.com",
      "port": 9001,
      "region": "us-west",
      "status": "online"
    },
    "game_session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_game_data": {
      "level": 15,
      "experience": 12500,
      "coins": 5000,
      "gems": 50
    }
  },
  "message": "游戏登录成功"
}
```

### 2. 获取排行榜

**接口**: `GET /game/leaderboard`

**描述**: 获取游戏排行榜

**查询参数**:
- `game_type`: 游戏类型 (必需)
- `type`: 排行榜类型 (level/score/wins) (可选，默认score)
- `limit`: 返回数量 (可选，默认10，最大100)
- `offset`: 偏移量 (可选，默认0)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "user_id": 12345,
        "username": "player1",
        "nickname": "顶级玩家",
        "score": 999999,
        "level": 50,
        "updated_at": "2024-01-01T12:00:00Z"
      },
      {
        "rank": 2,
        "user_id": 12346,
        "username": "player2",
        "nickname": "高手",
        "score": 888888,
        "level": 45,
        "updated_at": "2024-01-01T11:00:00Z"
      }
    ],
    "total_count": 1000,
    "user_rank": 15
  },
  "message": "获取成功"
}
```

## 系统接口

### 1. 健康检查

**接口**: `GET /health`

**描述**: 系统健康检查

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "version": "1.0.0",
    "uptime": 86400,
    "components": {
      "database": "healthy",
      "redis": "healthy",
      "auth_service": "healthy"
    }
  },
  "message": "系统正常"
}
```

### 2. 系统统计

**接口**: `GET /stats`

**描述**: 获取系统统计信息 (需要管理员权限)

**请求头**:
```http
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_users": 10000,
    "active_users_today": 1500,
    "total_requests_today": 50000,
    "average_response_time": 120,
    "error_rate": 0.01,
    "active_sessions": 500
  },
  "message": "获取成功"
}
```

## 错误码说明

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/令牌无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

### 业务错误码

| 错误码 | 说明 |
|--------|------|
| 1001 | 用户名已存在 |
| 1002 | 邮箱已存在 |
| 1003 | 密码强度不足 |
| 1004 | 用户不存在 |
| 1005 | 密码错误 |
| 1006 | 账户已锁定 |
| 1007 | 账户未激活 |
| 2001 | 令牌已过期 |
| 2002 | 令牌格式错误 |
| 2003 | 令牌已撤销 |
| 2004 | 刷新令牌无效 |
| 3001 | 游戏数据不存在 |
| 3002 | 游戏服务器不可用 |
| 3003 | 游戏会话已过期 |
| 9001 | 系统维护中 |
| 9002 | 数据库连接失败 |
| 9003 | 缓存服务不可用 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": 1001,
    "message": "用户名已存在",
    "details": "用户名 'testuser' 已被其他用户使用，请选择其他用户名"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 使用示例

### JavaScript/Node.js

```javascript
// 用户登录
const response = await fetch('http://localhost:8008/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    password: 'SecurePass123!'
  })
});

const data = await response.json();
if (data.success) {
  const accessToken = data.data.access_token;
  
  // 获取用户信息
  const userResponse = await fetch('http://localhost:8008/api/v1/user/profile', {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  
  const userData = await userResponse.json();
  console.log(userData.data);
}
```

### Python

```python
import requests

# 用户登录
response = requests.post('http://localhost:8008/api/v1/auth/login', json={
    'username': 'testuser',
    'password': 'SecurePass123!'
})

data = response.json()
if data['success']:
    access_token = data['data']['access_token']
    
    # 获取用户信息
    user_response = requests.get(
        'http://localhost:8008/api/v1/user/profile',
        headers={'Authorization': f'Bearer {access_token}'}
    )
    
    user_data = user_response.json()
    print(user_data['data'])
```

### cURL

```bash
# 用户登录
curl -X POST http://localhost:8008/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"SecurePass123!"}'

# 获取用户信息 (使用返回的access_token)
curl -X GET http://localhost:8008/api/v1/user/profile \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 注意事项

1. **令牌安全**: 访问令牌应安全存储，避免在URL中传递
2. **HTTPS**: 生产环境必须使用HTTPS协议
3. **频率限制**: 部分接口有频率限制，请合理控制请求频率
4. **时区**: 所有时间戳均为UTC时间
5. **版本控制**: API版本通过URL路径控制，当前版本为v1
6. **向后兼容**: 新版本API将保持向后兼容性
