# Config模块集成指南

## 概述

Config模块是一个统一的配置管理系统，为整个微服务项目提供集中化的配置管理功能。本文档详细说明如何将Config模块集成到现有的database、kafka、logger、network、scheduler和thread_pool模块中。

## 目录

1. [Config模块架构](#config模块架构)
2. [模块集成策略](#模块集成策略)
3. [各模块集成详解](#各模块集成详解)
4. [使用示例](#使用示例)
5. [最佳实践](#最佳实践)
6. [故障排除](#故障排除)

## Config模块架构

### 核心架构图

```mermaid
graph TB
    subgraph "Config Module Core"
        CM[ConfigManager<br/>单例模式]
        CV[ConfigValidator<br/>配置验证器]
        CL[ConfigLoader<br/>配置加载器]
        CH[ConfigHotReload<br/>热重载管理器]
    end
    
    subgraph "Configuration Sources"
        YF[YAML Files]
        JF[JSON Files]
        ENV[Environment Variables]
        CMD[Command Line Args]
    end
    
    subgraph "Target Modules"
        DB[Database Module]
        KF[Kafka Module]
        LOG[Logger Module]
        NET[Network Module]
        SCH[Scheduler Module]
        TP[ThreadPool Module]
    end
    
    subgraph "Configuration Structures"
        DBC[DatabaseConfig]
        RC[RedisConfig]
        KC[KafkaConfig]
        LC[LogConfig]
        SC[ServerConfig]
    end
    
    YF --> CL
    JF --> CL
    ENV --> CL
    CMD --> CL
    
    CL --> CM
    CV --> CM
    CH --> CM
    
    CM --> DBC
    CM --> RC
    CM --> KC
    CM --> LC
    CM --> SC
    
    DBC --> DB
    RC --> DB
    KC --> KF
    LC --> LOG
    SC --> NET
    CM --> SCH
    CM --> TP
```

### 详细组件结构

```mermaid
classDiagram
    class ConfigManager {
        -configs_ : map<string, string>
        -config_mutex_ : shared_mutex
        -change_listeners_ : map<string, vector<callback>>
        -hot_reload_enabled_ : atomic<bool>
        +getInstance() : ConfigManager&
        +loadFromFile(path) : bool
        +get<T>(key, default) : T
        +set<T>(key, value) : void
        +addChangeListener(key, callback) : void
        +enableHotReload(path, interval) : void
        +validate() : bool
    }
    
    class DatabaseConfig {
        +mysql_host : string
        +mysql_port : int
        +mysql_user : string
        +mysql_password : string
        +mysql_database : string
        +mysql_pool_size : size_t
        +fromConfigManager() : DatabaseConfig
        +validate() : void
    }
    
    class RedisConfig {
        +redis_host : string
        +redis_port : int
        +redis_password : string
        +redis_database : int
        +redis_pool_size : size_t
        +fromConfigManager() : RedisConfig
        +validate() : void
    }
    
    class KafkaConfig {
        +brokers : string
        +group_id : string
        +client_id : string
        +session_timeout_ms : int
        +fromConfigManager() : KafkaConfig
        +validate() : void
    }
    
    class LogConfig {
        +level : string
        +enable_console : bool
        +enable_file : bool
        +file_path : string
        +max_file_size : size_t
        +fromConfigManager() : LogConfig
        +validate() : void
    }
    
    class ServerConfig {
        +host : string
        +port : int
        +thread_pool_size : int
        +max_connections : int
        +enable_ssl : bool
        +fromConfigManager() : ServerConfig
        +validate() : void
    }
    
    ConfigManager --> DatabaseConfig
    ConfigManager --> RedisConfig
    ConfigManager --> KafkaConfig
    ConfigManager --> LogConfig
    ConfigManager --> ServerConfig
```

## 模块集成策略

### 1. 渐进式集成方案

```mermaid
graph LR
    A[Phase 1<br/>Logger集成] --> B[Phase 2<br/>Database集成]
    B --> C[Phase 3<br/>Network集成]
    C --> D[Phase 4<br/>Kafka集成]
    D --> E[Phase 5<br/>Scheduler集成]
    E --> F[Phase 6<br/>ThreadPool集成]
```

### 2. 配置优先级

```
1. 命令行参数 (最高优先级)
2. 环境变量
3. 配置文件 (YAML/JSON)
4. 默认值 (最低优先级)
```

### 3. 配置文件结构

```yaml
# config/application.yml
system:
  version: "1.0.0"
  environment: "development"

database:
  mysql:
    host: "dev-mysql"
    port: 3306
    user: "app_user"
    password: "${MYSQL_PASSWORD}"
    database: "game_db"
    pool_size: 10
    max_pool_size: 50
  
  redis:
    host: "redis"
    port: 6379
    password: "${REDIS_PASSWORD}"
    database: 0
    pool_size: 5
    max_pool_size: 20

server:
  host: "0.0.0.0"
  port: 8080
  thread_pool_size: 8
  max_connections: 1000
  keep_alive_timeout: 60
  ssl:
    enabled: false
    cert_file: ""
    key_file: ""

kafka:
  brokers: "kafka:9092"
  group_id: "game-service-group"
  client_id: "game-service"
  session_timeout_ms: 30000
  max_poll_interval_ms: 300000
  offset_reset: "earliest"
  enable_auto_commit: false

logging:
  level: "INFO"
  console:
    enabled: true
  file:
    enabled: true
    path: "logs/app.log"
    max_size: 104857600  # 100MB
    max_files: 10
  async:
    enabled: true
```

## 各模块集成详解

### 1. Logger模块集成

#### 集成步骤

```cpp
// 1. 在logger初始化时加载配置
#include "common/config/config_manager.h"

void Logger::initialize() {
    auto& config_manager = ConfigManager::getInstance();
    
    // 加载配置文件
    if (!config_manager.loadFromFile("config/application.yml")) {
        // 使用默认配置
        LOG_WARNING("Failed to load config file, using defaults");
    }
    
    // 获取日志配置
    auto log_config = LogConfig::fromConfigManager();
    log_config.validate();
    
    // 应用配置
    setLogLevel(log_config.level);
    setConsoleOutput(log_config.enable_console);
    setFileOutput(log_config.enable_file, log_config.file_path);
    setAsyncMode(log_config.enable_async);
    
    // 注册配置变更监听
    config_manager.addChangeListener("log_level", 
        [this](const std::string& key, const std::string& old_val, const std::string& new_val) {
            this->setLogLevel(new_val);
            LOG_INFO("Log level changed from " + old_val + " to " + new_val);
        });
}
```

#### 配置热重载支持

```cpp
void Logger::enableConfigHotReload() {
    auto& config_manager = ConfigManager::getInstance();
    
    // 启用热重载，每5秒检查一次
    config_manager.enableHotReload("config/application.yml", std::chrono::seconds(5));
    
    // 注册所有日志相关配置的监听器
    std::vector<std::string> log_keys = {
        "log_level", "enable_console", "enable_file", 
        "log_file_path", "enable_async"
    };
    
    for (const auto& key : log_keys) {
        config_manager.addChangeListener(key, 
            [this](const std::string& k, const std::string& old_v, const std::string& new_v) {
                this->handleConfigChange(k, old_v, new_v);
            });
    }
}
```

### 2. Database模块集成

#### MySQL连接池集成

```cpp
// include/common/database/mysql_pool.h
class MySQLPool {
private:
    DatabaseConfig config_;
    
public:
    // 使用配置管理器初始化
    bool initializeFromConfig() {
        auto& config_manager = ConfigManager::getInstance();
        
        try {
            config_ = DatabaseConfig::fromConfigManager();
            config_.validate();
            
            // 应用配置
            return initialize(config_.mysql_host, config_.mysql_port, 
                            config_.mysql_user, config_.mysql_password,
                            config_.mysql_database, config_.mysql_pool_size);
        } catch (const std::exception& e) {
            LOG_ERROR("Failed to initialize MySQL pool from config: " + std::string(e.what()));
            return false;
        }
    }
    
    // 配置变更处理
    void handleConfigChange(const std::string& key, const std::string& new_value) {
        if (key == "mysql_pool_size") {
            resizePool(std::stoi(new_value));
        } else if (key == "mysql_max_pool_size") {
            setMaxPoolSize(std::stoi(new_value));
        }
        // 其他配置变更需要重新连接
    }
};
```

#### Redis连接池集成

```cpp
// src/common/database/redis_pool.cpp
bool RedisPool::initializeFromConfig() {
    auto& config_manager = ConfigManager::getInstance();
    
    try {
        auto redis_config = RedisConfig::fromConfigManager();
        redis_config.validate();
        
        // 创建Redis连接池配置
        Config pool_config;
        pool_config.host = redis_config.redis_host;
        pool_config.port = redis_config.redis_port;
        pool_config.password = redis_config.redis_password;
        pool_config.initial_size = redis_config.redis_pool_size;
        pool_config.max_size = redis_config.redis_max_pool_size;
        
        return initialize(pool_config);
        
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to initialize Redis pool from config: " + std::string(e.what()));
        return false;
    }
}
```

### 3. Network模块集成

#### 服务器配置集成

```cpp
// include/common/network/server.h
class Server {
private:
    ServerConfig config_;
    
public:
    bool startFromConfig() {
        auto& config_manager = ConfigManager::getInstance();
        
        try {
            config_ = ServerConfig::fromConfigManager();
            config_.validate();
            
            // 配置服务器参数
            setBindAddress(config_.host, config_.port);
            setThreadPoolSize(config_.thread_pool_size);
            setMaxConnections(config_.max_connections);
            setKeepAliveTimeout(config_.keep_alive_timeout);
            
            if (config_.enable_ssl) {
                enableSSL(config_.ssl_cert_file, config_.ssl_key_file);
            }
            
            return start();
            
        } catch (const std::exception& e) {
            LOG_ERROR("Failed to start server from config: " + std::string(e.what()));
            return false;
        }
    }
};
```

### 4. Kafka模块集成

```cpp
// include/common/kafka/kafka_client.h
class KafkaClient {
private:
    KafkaConfig config_;
    
public:
    bool initializeFromConfig() {
        auto& config_manager = ConfigManager::getInstance();
        
        try {
            config_ = KafkaConfig::fromConfigManager();
            config_.validate();
            
            // 配置Kafka客户端
            setBootstrapServers(config_.brokers);
            setGroupId(config_.group_id);
            setClientId(config_.client_id);
            setSessionTimeout(config_.session_timeout_ms);
            setMaxPollInterval(config_.max_poll_interval_ms);
            setOffsetReset(config_.offset_reset);
            setAutoCommit(config_.enable_auto_commit);
            
            return connect();
            
        } catch (const std::exception& e) {
            LOG_ERROR("Failed to initialize Kafka client from config: " + std::string(e.what()));
            return false;
        }
    }
};
```

### 5. Scheduler模块集成

```cpp
// src/common/scheduler/task_scheduler.cpp
bool TaskScheduler::initializeFromConfig() {
    auto& config_manager = ConfigManager::getInstance();
    
    // 获取调度器相关配置
    int worker_threads = config_manager.get<int>("scheduler.worker_threads", 4);
    int max_pending_tasks = config_manager.get<int>("scheduler.max_pending_tasks", 1000);
    bool enable_metrics = config_manager.get<bool>("scheduler.enable_metrics", true);
    
    // 应用配置
    setWorkerThreads(worker_threads);
    setMaxPendingTasks(max_pending_tasks);
    setMetricsEnabled(enable_metrics);
    
    LOG_INFO("TaskScheduler initialized with " + std::to_string(worker_threads) + " worker threads");
    return true;
}
```

### 6. ThreadPool模块集成

```cpp
// src/common/thread_pool/thread_pool.cpp
bool ThreadPool::initializeFromConfig() {
    auto& config_manager = ConfigManager::getInstance();
    
    // 获取线程池配置
    int core_threads = config_manager.get<int>("thread_pool.core_threads", 
                                              std::thread::hardware_concurrency());
    int max_threads = config_manager.get<int>("thread_pool.max_threads", 
                                             core_threads * 2);
    int queue_size = config_manager.get<int>("thread_pool.queue_size", 1000);
    int keep_alive_ms = config_manager.get<int>("thread_pool.keep_alive_ms", 60000);
    
    // 应用配置
    setCorePoolSize(core_threads);
    setMaximumPoolSize(max_threads);
    setQueueCapacity(queue_size);
    setKeepAliveTime(std::chrono::milliseconds(keep_alive_ms));
    
    LOG_INFO("ThreadPool initialized: core=" + std::to_string(core_threads) + 
             ", max=" + std::to_string(max_threads));
    return true;
}
```

## 使用示例

### 1. 应用程序启动流程

```cpp
// main.cpp
int main(int argc, char* argv[]) {
    try {
        // 1. 初始化配置管理器
        auto& config_manager = ConfigManager::getInstance();
        
        // 2. 加载配置文件
        if (!config_manager.loadFromFile("config/application.yml")) {
            std::cerr << "Failed to load configuration file" << std::endl;
            return -1;
        }
        
        // 3. 加载环境变量覆盖
        config_manager.loadFromEnv();
        
        // 4. 验证配置
        if (!config_manager.validate()) {
            auto errors = config_manager.getValidationErrors();
            for (const auto& error : errors) {
                std::cerr << "Config error: " << error << std::endl;
            }
            return -1;
        }
        
        // 5. 启用热重载
        config_manager.enableHotReload("config/application.yml");
        
        // 6. 按顺序初始化各模块
        Logger::getInstance().initializeFromConfig();
        
        MySQLPool mysql_pool;
        mysql_pool.initializeFromConfig();
        
        RedisPool redis_pool;
        redis_pool.initializeFromConfig();
        
        Server server;
        server.startFromConfig();
        
        KafkaClient kafka_client;
        kafka_client.initializeFromConfig();
        
        TaskScheduler scheduler;
        scheduler.initializeFromConfig();
        
        ThreadPool thread_pool;
        thread_pool.initializeFromConfig();
        
        LOG_INFO("All modules initialized successfully");
        
        // 7. 运行应用程序
        server.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Application startup failed: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
```

### 2. 配置变更监听示例

```cpp
// 监听数据库连接池大小变更
config_manager.addChangeListener("mysql_pool_size", 
    [&mysql_pool](const std::string& key, const std::string& old_val, const std::string& new_val) {
        try {
            int new_size = std::stoi(new_val);
            mysql_pool.resizePool(new_size);
            LOG_INFO("MySQL pool resized from " + old_val + " to " + new_val);
        } catch (const std::exception& e) {
            LOG_ERROR("Failed to resize MySQL pool: " + std::string(e.what()));
        }
    });

// 监听日志级别变更
config_manager.addChangeListener("log_level",
    [](const std::string& key, const std::string& old_val, const std::string& new_val) {
        Logger::getInstance().setLogLevel(new_val);
        LOG_INFO("Log level changed from " + old_val + " to " + new_val);
    });
```

### 3. 环境特定配置

```cpp
// 根据环境加载不同配置
std::string env = config_manager.get<std::string>("system.environment", "development");
std::string config_file = "config/application-" + env + ".yml";

if (!config_manager.loadFromFile(config_file)) {
    LOG_WARNING("Environment-specific config not found, using default");
    config_manager.loadFromFile("config/application.yml");
}
```

## 最佳实践

### 1. 配置文件组织

```
config/
├── application.yml          # 主配置文件
├── application-dev.yml      # 开发环境配置
├── application-test.yml     # 测试环境配置
├── application-prod.yml     # 生产环境配置
└── secrets/
    ├── database.yml         # 数据库密码等敏感信息
    └── certificates/        # SSL证书文件
```

### 2. 配置验证策略

```cpp
// 自定义配置验证
class CustomConfigValidator {
public:
    static void validateDatabaseConfig(const DatabaseConfig& config) {
        if (config.mysql_pool_size > config.mysql_max_pool_size) {
            throw std::invalid_argument("Pool size cannot exceed max pool size");
        }
        
        if (config.mysql_port < 1 || config.mysql_port > 65535) {
            throw std::invalid_argument("Invalid MySQL port: " + std::to_string(config.mysql_port));
        }
    }
};
```

### 3. 配置缓存策略

```cpp
// 配置缓存以提高性能
class ConfigCache {
private:
    mutable std::shared_mutex cache_mutex_;
    mutable std::map<std::string, std::any> cache_;
    
public:
    template<typename T>
    T getCached(const std::string& key, const T& default_value) const {
        std::shared_lock<std::shared_mutex> lock(cache_mutex_);
        auto it = cache_.find(key);
        if (it != cache_.end()) {
            return std::any_cast<T>(it->second);
        }
        
        lock.unlock();
        std::unique_lock<std::shared_mutex> write_lock(cache_mutex_);
        
        T value = ConfigManager::getInstance().get<T>(key, default_value);
        cache_[key] = value;
        return value;
    }
};
```

### 4. 错误处理和回退机制

```cpp
// 配置加载失败时的回退策略
bool loadConfigWithFallback() {
    auto& config_manager = ConfigManager::getInstance();
    
    // 尝试加载主配置文件
    if (config_manager.loadFromFile("config/application.yml")) {
        return true;
    }
    
    LOG_WARNING("Primary config file not found, trying backup");
    
    // 尝试加载备份配置
    if (config_manager.loadFromFile("config/application.backup.yml")) {
        return true;
    }
    
    LOG_WARNING("Backup config not found, using embedded defaults");
    
    // 使用内嵌的默认配置
    config_manager.set("mysql_host", "localhost");
    config_manager.set("mysql_port", "3306");
    config_manager.set("redis_host", "localhost");
    config_manager.set("redis_port", "6379");
    // ... 其他默认配置
    
    return true;
}
```

## 故障排除

### 常见问题和解决方案

1. **配置文件加载失败**
   - 检查文件路径和权限
   - 验证YAML/JSON格式
   - 查看详细错误日志

2. **配置验证失败**
   - 使用`getValidationErrors()`获取详细错误
   - 检查必需配置是否缺失
   - 验证数值范围和格式

3. **热重载不工作**
   - 确认文件监控权限
   - 检查文件系统事件支持
   - 验证热重载间隔设置

4. **性能问题**
   - 使用配置缓存
   - 减少配置访问频率
   - 优化配置变更监听器

## 配置模块数据流图

```mermaid
sequenceDiagram
    participant App as Application
    participant CM as ConfigManager
    participant CF as Config File
    participant ENV as Environment
    participant MOD as Module
    participant CB as Callback

    App->>CM: getInstance()
    App->>CM: loadFromFile("config.yml")
    CM->>CF: Read YAML/JSON
    CF-->>CM: Configuration Data
    App->>CM: loadFromEnv()
    CM->>ENV: Read Environment Variables
    ENV-->>CM: Environment Overrides
    App->>CM: validate()
    CM-->>App: Validation Result

    App->>MOD: initializeFromConfig()
    MOD->>CM: get<T>(key, default)
    CM-->>MOD: Configuration Value
    MOD->>CM: addChangeListener(key, callback)
    CM-->>MOD: Listener Registered

    Note over CM: Configuration Change Detected
    CM->>CB: notify(key, old_val, new_val)
    CB->>MOD: handleConfigChange()
    MOD-->>CB: Configuration Applied
```

## 配置优先级和覆盖机制

```mermaid
graph TD
    A[命令行参数<br/>--mysql-host=localhost] --> B{配置合并器}
    C[环境变量<br/>MYSQL_HOST=dev-db] --> B
    D[配置文件<br/>mysql_host: prod-db] --> B
    E[默认值<br/>localhost] --> B

    B --> F[最终配置值<br/>localhost]

    style A fill:#ff9999
    style C fill:#ffcc99
    style D fill:#99ccff
    style E fill:#cccccc
    style F fill:#99ff99
```

## 性能优化和监控

### 1. 配置访问性能监控

```cpp
// include/common/config/config_metrics.h
class ConfigMetrics {
private:
    std::atomic<uint64_t> get_count_{0};
    std::atomic<uint64_t> set_count_{0};
    std::atomic<uint64_t> cache_hits_{0};
    std::atomic<uint64_t> cache_misses_{0};

public:
    void recordGet() { get_count_++; }
    void recordSet() { set_count_++; }
    void recordCacheHit() { cache_hits_++; }
    void recordCacheMiss() { cache_misses_++; }

    double getCacheHitRatio() const {
        uint64_t total = cache_hits_ + cache_misses_;
        return total > 0 ? static_cast<double>(cache_hits_) / total : 0.0;
    }

    void printMetrics() const {
        LOG_INFO("Config Metrics - Gets: " + std::to_string(get_count_) +
                ", Sets: " + std::to_string(set_count_) +
                ", Cache Hit Ratio: " + std::to_string(getCacheHitRatio() * 100) + "%");
    }
};
```

### 2. 配置变更影响分析

```cpp
// 配置变更影响分析器
class ConfigImpactAnalyzer {
public:
    struct ImpactInfo {
        std::string module_name;
        std::string impact_level;  // "low", "medium", "high", "critical"
        std::string description;
        bool requires_restart;
    };

    static std::vector<ImpactInfo> analyzeConfigChange(const std::string& key) {
        std::vector<ImpactInfo> impacts;

        if (key.find("mysql_") == 0) {
            impacts.push_back({
                "Database Module",
                "high",
                "Database connection parameters changed",
                key == "mysql_host" || key == "mysql_port"
            });
        }

        if (key.find("redis_") == 0) {
            impacts.push_back({
                "Redis Module",
                "medium",
                "Redis connection parameters changed",
                key == "redis_host" || key == "redis_port"
            });
        }

        if (key == "log_level") {
            impacts.push_back({
                "Logger Module",
                "low",
                "Log level changed - takes effect immediately",
                false
            });
        }

        return impacts;
    }
};
```

## 安全性考虑

### 1. 敏感信息处理

```cpp
// 敏感配置加密/解密
class SecureConfigHandler {
private:
    std::string encryption_key_;

public:
    // 加密敏感配置值
    std::string encryptValue(const std::string& plaintext) {
        // 使用AES加密实现
        return "ENC(" + aes_encrypt(plaintext, encryption_key_) + ")";
    }

    // 解密配置值
    std::string decryptValue(const std::string& encrypted) {
        if (encrypted.substr(0, 4) == "ENC(") {
            std::string cipher = encrypted.substr(4, encrypted.length() - 5);
            return aes_decrypt(cipher, encryption_key_);
        }
        return encrypted;  // 未加密的值直接返回
    }

    // 检查是否为敏感配置
    bool isSensitiveKey(const std::string& key) {
        static const std::set<std::string> sensitive_keys = {
            "mysql_password", "redis_password", "ssl_key_file",
            "kafka_sasl_password", "jwt_secret"
        };
        return sensitive_keys.find(key) != sensitive_keys.end();
    }
};
```

### 2. 配置访问权限控制

```cpp
// 配置访问权限管理
class ConfigAccessControl {
private:
    std::map<std::string, std::set<std::string>> module_permissions_;

public:
    void grantPermission(const std::string& module, const std::string& config_key) {
        module_permissions_[module].insert(config_key);
    }

    bool hasPermission(const std::string& module, const std::string& config_key) {
        auto it = module_permissions_.find(module);
        if (it != module_permissions_.end()) {
            return it->second.find(config_key) != it->second.end();
        }
        return false;
    }

    void initializeDefaultPermissions() {
        // Database模块权限
        grantPermission("database", "mysql_host");
        grantPermission("database", "mysql_port");
        grantPermission("database", "mysql_user");
        grantPermission("database", "mysql_password");

        // Logger模块权限
        grantPermission("logger", "log_level");
        grantPermission("logger", "enable_console");
        grantPermission("logger", "enable_file");

        // 其他模块权限...
    }
};
```

## 测试策略

### 1. 配置模块单元测试

```cpp
// tests/config_manager_test.cpp
class ConfigManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试配置文件
        createTestConfigFile();
        config_manager_ = &ConfigManager::getInstance();
    }

    void TearDown() override {
        config_manager_->reset();
        removeTestConfigFile();
    }

    ConfigManager* config_manager_;
};

TEST_F(ConfigManagerTest, LoadFromYamlFile) {
    ASSERT_TRUE(config_manager_->loadFromFile("test_config.yml"));
    EXPECT_EQ(config_manager_->get<std::string>("test_key"), "test_value");
}

TEST_F(ConfigManagerTest, ConfigValidation) {
    config_manager_->set("mysql_port", "invalid_port");
    EXPECT_FALSE(config_manager_->validate());

    auto errors = config_manager_->getValidationErrors();
    EXPECT_GT(errors.size(), 0);
}

TEST_F(ConfigManagerTest, HotReload) {
    config_manager_->loadFromFile("test_config.yml");
    config_manager_->enableHotReload("test_config.yml", std::chrono::seconds(1));

    // 修改配置文件
    modifyTestConfigFile();

    // 等待热重载
    std::this_thread::sleep_for(std::chrono::seconds(2));

    EXPECT_EQ(config_manager_->get<std::string>("test_key"), "new_test_value");
}
```

### 2. 集成测试

```cpp
// tests/config_integration_test.cpp
TEST(ConfigIntegrationTest, DatabaseModuleIntegration) {
    auto& config_manager = ConfigManager::getInstance();
    config_manager.loadFromFile("test_database_config.yml");

    MySQLPool mysql_pool;
    ASSERT_TRUE(mysql_pool.initializeFromConfig());

    // 测试配置变更
    config_manager.set("mysql_pool_size", "20");

    // 验证配置变更是否生效
    EXPECT_EQ(mysql_pool.getCurrentPoolSize(), 20);
}
```

## 部署和运维

### 1. Docker环境配置

```dockerfile
# Dockerfile
FROM ubuntu:20.04

# 复制配置文件
COPY config/ /app/config/
COPY config/docker/ /app/config/

# 设置环境变量
ENV CONFIG_FILE=/app/config/application-docker.yml
ENV LOG_LEVEL=INFO
ENV MYSQL_HOST=mysql-service
ENV REDIS_HOST=redis-service

# 启动应用
CMD ["./game_service", "--config", "${CONFIG_FILE}"]
```

### 2. Kubernetes ConfigMap集成

```yaml
# k8s/configmap.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-service-config
data:
  application.yml: |
    database:
      mysql:
        host: mysql-service
        port: 3306
        user: app_user
        database: game_db

    redis:
      host: redis-service
      port: 6379

    server:
      host: 0.0.0.0
      port: 8080

---
apiVersion: v1
kind: Secret
metadata:
  name: game-service-secrets
type: Opaque
data:
  mysql-password: <base64-encoded-password>
  redis-password: <base64-encoded-password>
```

### 3. 配置管理最佳实践总结

1. **分层配置**: 基础配置 → 环境配置 → 运行时配置
2. **敏感信息**: 使用加密存储，避免明文配置
3. **版本控制**: 配置文件纳入版本管理，但排除敏感信息
4. **监控告警**: 配置变更监控和异常告警
5. **回滚机制**: 配置变更失败时的快速回滚
6. **文档维护**: 配置项说明和变更记录

通过以上完整的集成方案，Config模块可以为整个微服务项目提供企业级的配置管理能力，确保系统的可维护性、安全性和可扩展性。
