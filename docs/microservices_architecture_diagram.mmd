graph TB
    %% 游戏微服务系统架构图
    
    subgraph "客户端层"
        WebClient[Web客户端]
        MobileClient[移动客户端]
        GameClient[游戏客户端]
    end
    
    subgraph "API网关层"
        APIGateway[API网关<br/>- 路由转发<br/>- 负载均衡<br/>- 限流熔断<br/>- 认证鉴权]
    end
    
    subgraph "核心服务层 (Core Services)"
        AuthService[认证服务<br/>- JWT管理<br/>- 用户认证<br/>- 权限控制]
        UserService[用户服务<br/>- 用户管理<br/>- 用户关系<br/>- 用户状态]
        ServiceRegistry[服务注册中心<br/>- 服务注册<br/>- 服务发现<br/>- 健康检查]
        ConfigService[配置服务<br/>- 配置管理<br/>- 配置分发<br/>- 版本控制]
    end
    
    subgraph "游戏服务层 (Game Services)"
        GameBaseService[游戏基础服务<br/>- 游戏数据<br/>- 游戏规则<br/>- 游戏状态]
        RoomService[房间服务<br/>- 房间管理<br/>- 玩家进出<br/>- 房间状态]
        MatchmakingService[匹配服务<br/>- 玩家匹配<br/>- 技能评级<br/>- 匹配算法]
    end
    
    subgraph "公共基础设施层 (Common Infrastructure)"
        subgraph "网络模块"
            EventLoop[事件循环]
            Channel[事件通道]
            Epoll[Epoll机制]
            Socket[Socket通信]
            InetAddress[网络地址]
            HTTPFramework[HTTP框架]
        end
        
        subgraph "数据存储"
            MySQLPool[MySQL连接池]
            RedisPool[Redis连接池]
        end
        
        subgraph "消息队列"
            KafkaProducer[Kafka生产者]
            KafkaConsumer[Kafka消费者]
        end
        
        subgraph "系统组件"
            ThreadPool[线程池]
            TaskScheduler[任务调度器]
            Logger[日志系统]
            ConfigManager[配置管理器]
        end
    end
    
    subgraph "外部依赖"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        Kafka[(Kafka消息队列)]
    end
    
    %% 客户端到网关的连接
    WebClient --> APIGateway
    MobileClient --> APIGateway
    GameClient --> APIGateway
    
    %% 网关到核心服务的连接
    APIGateway --> AuthService
    APIGateway --> UserService
    APIGateway --> GameBaseService
    APIGateway --> RoomService
    APIGateway --> MatchmakingService
    
    %% 服务间的依赖关系
    AuthService --> UserService
    RoomService --> GameBaseService
    MatchmakingService --> RoomService
    MatchmakingService --> UserService
    
    %% 服务注册发现
    AuthService -.-> ServiceRegistry
    UserService -.-> ServiceRegistry
    GameBaseService -.-> ServiceRegistry
    RoomService -.-> ServiceRegistry
    MatchmakingService -.-> ServiceRegistry
    APIGateway -.-> ServiceRegistry
    
    %% 配置服务连接
    AuthService -.-> ConfigService
    UserService -.-> ConfigService
    GameBaseService -.-> ConfigService
    RoomService -.-> ConfigService
    MatchmakingService -.-> ConfigService
    APIGateway -.-> ConfigService
    
    %% 核心服务到基础设施的连接
    AuthService --> HTTPFramework
    UserService --> HTTPFramework
    GameBaseService --> HTTPFramework
    RoomService --> HTTPFramework
    MatchmakingService --> HTTPFramework
    ServiceRegistry --> HTTPFramework
    ConfigService --> HTTPFramework
    APIGateway --> HTTPFramework
    
    %% HTTP框架到网络模块的连接
    HTTPFramework --> EventLoop
    HTTPFramework --> Socket
    HTTPFramework --> InetAddress
    
    %% 网络模块内部连接
    EventLoop --> Channel
    EventLoop --> Epoll
    Channel --> Socket
    
    %% 服务到数据存储的连接
    AuthService --> MySQLPool
    AuthService --> RedisPool
    UserService --> MySQLPool
    UserService --> RedisPool
    GameBaseService --> MySQLPool
    RoomService --> RedisPool
    MatchmakingService --> RedisPool
    ServiceRegistry --> RedisPool
    ConfigService --> MySQLPool
    
    %% 服务到消息队列的连接
    RoomService --> KafkaProducer
    MatchmakingService --> KafkaProducer
    GameBaseService --> KafkaConsumer
    
    %% 基础组件的使用关系
    AuthService --> ThreadPool
    UserService --> ThreadPool
    GameBaseService --> ThreadPool
    RoomService --> ThreadPool
    MatchmakingService --> ThreadPool
    APIGateway --> ThreadPool
    
    MatchmakingService --> TaskScheduler
    ConfigService --> TaskScheduler
    
    %% 所有服务都使用日志和配置
    AuthService --> Logger
    UserService --> Logger
    GameBaseService --> Logger
    RoomService --> Logger
    MatchmakingService --> Logger
    APIGateway --> Logger
    ServiceRegistry --> Logger
    ConfigService --> Logger
    
    AuthService --> ConfigManager
    UserService --> ConfigManager
    GameBaseService --> ConfigManager
    RoomService --> ConfigManager
    MatchmakingService --> ConfigManager
    APIGateway --> ConfigManager
    ServiceRegistry --> ConfigManager
    
    %% 基础设施到外部依赖的连接
    MySQLPool --> MySQL
    RedisPool --> Redis
    KafkaProducer --> Kafka
    KafkaConsumer --> Kafka
    
    %% 样式定义
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gatewayStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef coreServiceStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef gameServiceStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef infraStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dbStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class WebClient,MobileClient,GameClient clientStyle
    class APIGateway gatewayStyle
    class AuthService,UserService,ServiceRegistry,ConfigService coreServiceStyle
    class GameBaseService,RoomService,MatchmakingService gameServiceStyle
    class EventLoop,Channel,Epoll,Socket,InetAddress,HTTPFramework,MySQLPool,RedisPool,KafkaProducer,KafkaConsumer,ThreadPool,TaskScheduler,Logger,ConfigManager infraStyle
    class MySQL,Redis,Kafka dbStyle
