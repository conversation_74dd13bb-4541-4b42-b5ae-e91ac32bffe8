graph TB
    %% 网络模块完整工作流程图
    
    subgraph "配置管理层"
        CM[ConfigManager<br/>配置管理器]
        NC[NetworkConfig<br/>网络配置]
        CF[Config File<br/>配置文件]
        
        CF -->|文件监控| CM
        CM -->|加载配置| NC
    end
    
    subgraph "应用层"
        NS[NetworkServer<br/>网络服务器]
        TPM[ThreadPoolManager<br/>线程池管理器]
        
        NS -->|管理| TPM
    end
    
    subgraph "业务协调层"
        EL[EventLoop<br/>事件循环]
        HR[HotReload<br/>热更新机制]
        CB[Callbacks<br/>回调管理]
        
        EL -->|集成| HR
        EL -->|管理| CB
    end
    
    subgraph "系统层"
        EP[Epoll<br/>IO多路复用]
        SK[Socket<br/>网络套接字]
        CH[Channel<br/>事件通道]
        IA[InetAddress<br/>网络地址]
        
        EP -->|管理| CH
        SK -->|绑定| IA
        CH -->|封装| SK
    end
    
    subgraph "线程池层"
        TP[ThreadPool<br/>线程池]
        TW[Worker Threads<br/>工作线程]
        
        TP -->|管理| TW
    end
    
    %% 主要数据流
    NC -->|配置注入| EL
    EL -->|参数传递| EP
    EL -->|参数传递| SK
    EL -->|管理| CH
    EL -->|集成| TP
    
    %% 热更新流程
    CM -->|配置变更通知| HR
    HR -->|触发更新| EL
    EL -->|应用配置| EP
    EL -->|应用配置| SK
    EL -->|通知变更| CB
    
    %% 网络连接流程
    SK -->|监听| EP
    EP -->|事件通知| CH
    CH -->|回调执行| EL
    EL -->|异步处理| TP
    
    %% 样式定义
    classDef configLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef appLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef businessLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef systemLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef threadLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class CM,NC,CF configLayer
    class NS,TPM appLayer
    class EL,HR,CB businessLayer
    class EP,SK,CH,IA systemLayer
    class TP,TW threadLayer
