# 游戏微服务项目详细开发计划

## 📋 文档概述

本文档将开发任务细化到每个模块、每个类、每个方法的具体实现，为开发团队提供精确的开发指导。

## 🎯 第一阶段：核心基础设施 (3-4周)

### 第1周：基础组件开发

#### Day 1-2: 日志系统模块 (🔥 P0)

**模块路径**: `include/common/logger/` 和 `src/common/logger/`

##### 1.1 核心类设计

```cpp
// include/common/logger/log_level.h
enum class LogLevel : uint8_t {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warn = 3,
    Error = 4,
    Critical = 5
};

// include/common/logger/log_message.h
struct LogMessage {
    LogLevel level;
    std::string logger_name;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    std::string file;
    int line;
    std::string function;
    std::thread::id thread_id;
};

// include/common/logger/log_sink.h
class LogSink {
public:
    virtual ~LogSink() = default;
    virtual void log(const LogMessage& message) = 0;
    virtual void flush() = 0;
    virtual void set_level(LogLevel level) = 0;
};

// include/common/logger/console_sink.h
class ConsoleSink : public LogSink {
public:
    explicit ConsoleSink(bool colored = true);
    void log(const LogMessage& message) override;
    void flush() override;
    void set_level(LogLevel level) override;
    
private:
    bool colored_;
    LogLevel level_ = LogLevel::Info;
    std::mutex mutex_;
    
    std::string format_message(const LogMessage& message);
    std::string get_color_code(LogLevel level);
};

// include/common/logger/file_sink.h
class FileSink : public LogSink {
public:
    struct Config {
        std::string filename;
        size_t max_file_size = 10 * 1024 * 1024;  // 10MB
        size_t max_files = 5;
        bool auto_flush = true;
    };
    
    explicit FileSink(Config config);
    ~FileSink();
    
    void log(const LogMessage& message) override;
    void flush() override;
    void set_level(LogLevel level) override;
    
private:
    Config config_;
    std::ofstream file_;
    size_t current_file_size_ = 0;
    LogLevel level_ = LogLevel::Info;
    std::mutex mutex_;
    
    void rotate_file();
    std::string get_rotated_filename(size_t index);
};

// include/common/logger/async_logger.h
template<typename SinkType>
class AsyncLogger {
public:
    explicit AsyncLogger(std::string name, std::shared_ptr<SinkType> sink);
    ~AsyncLogger();
    
    template<typename... Args>
    void trace(std::format_string<Args...> fmt, Args&&... args);
    
    template<typename... Args>
    void debug(std::format_string<Args...> fmt, Args&&... args);
    
    template<typename... Args>
    void info(std::format_string<Args...> fmt, Args&&... args);
    
    template<typename... Args>
    void warn(std::format_string<Args...> fmt, Args&&... args);
    
    template<typename... Args>
    void error(std::format_string<Args...> fmt, Args&&... args);
    
    template<typename... Args>
    void critical(std::format_string<Args...> fmt, Args&&... args);
    
    void set_level(LogLevel level);
    void flush();
    
private:
    std::string name_;
    std::shared_ptr<SinkType> sink_;
    LogLevel level_ = LogLevel::Info;
    
    // 异步处理
    std::queue<LogMessage> message_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::jthread worker_thread_;
    std::atomic<bool> running_{true};
    
    void log_impl(LogLevel level, std::string message, 
                 std::source_location location = std::source_location::current());
    void worker_loop(std::stop_token stop_token);
};

// include/common/logger/logger_manager.h
class LoggerManager {
public:
    static LoggerManager& instance();
    
    template<typename SinkType, typename... Args>
    std::shared_ptr<AsyncLogger<SinkType>> create_logger(const std::string& name, Args&&... args);
    
    std::shared_ptr<LogSink> get_logger(const std::string& name);
    void remove_logger(const std::string& name);
    void flush_all();
    void set_global_level(LogLevel level);
    
private:
    std::unordered_map<std::string, std::shared_ptr<LogSink>> loggers_;
    std::shared_mutex loggers_mutex_;
    LogLevel global_level_ = LogLevel::Info;
    
    LoggerManager() = default;
};
```

##### 1.2 具体开发任务

**Day 1 任务清单**:
```cpp
✅ 实现 LogLevel 枚举和相关工具函数
✅ 实现 LogMessage 结构体
✅ 实现 LogSink 基类接口
✅ 实现 ConsoleSink 类
   - 构造函数和析构函数
   - log() 方法实现
   - flush() 方法实现
   - format_message() 私有方法
   - get_color_code() 私有方法
✅ 编写 ConsoleSink 单元测试
```

**Day 2 任务清单**:
```cpp
✅ 实现 FileSink 类
   - 构造函数和析构函数
   - log() 方法实现
   - flush() 方法实现
   - rotate_file() 私有方法
   - get_rotated_filename() 私有方法
✅ 实现 AsyncLogger 模板类
   - 构造函数和析构函数
   - 各级别日志方法 (trace, debug, info, warn, error, critical)
   - log_impl() 私有方法
   - worker_loop() 私有方法
✅ 实现 LoggerManager 单例类
   - create_logger() 模板方法
   - get_logger() 方法
   - remove_logger() 方法
   - flush_all() 方法
✅ 编写完整的单元测试套件
✅ 性能测试 (目标: 100万条日志/秒)
```

#### Day 3-4: 配置管理系统 (🔥 P0)

**模块路径**: `include/common/config/` 和 `src/common/config/`

##### 2.1 核心类设计

```cpp
// include/common/config/config_value.h
class ConfigValue {
public:
    enum class Type {
        Null,
        Bool,
        Int,
        Double,
        String,
        Array,
        Object
    };
    
    ConfigValue() = default;
    ConfigValue(bool value);
    ConfigValue(int64_t value);
    ConfigValue(double value);
    ConfigValue(std::string value);
    ConfigValue(std::vector<ConfigValue> value);
    ConfigValue(std::unordered_map<std::string, ConfigValue> value);
    
    template<typename T>
    T as() const;
    
    template<typename T>
    T get_or(T default_value) const;
    
    bool is_null() const;
    bool is_bool() const;
    bool is_int() const;
    bool is_double() const;
    bool is_string() const;
    bool is_array() const;
    bool is_object() const;
    
    ConfigValue& operator[](const std::string& key);
    const ConfigValue& operator[](const std::string& key) const;
    ConfigValue& operator[](size_t index);
    const ConfigValue& operator[](size_t index) const;
    
private:
    Type type_ = Type::Null;
    std::variant<std::monostate, bool, int64_t, double, std::string,
                std::vector<ConfigValue>, std::unordered_map<std::string, ConfigValue>> value_;
};

// include/common/config/config_parser.h
class ConfigParser {
public:
    virtual ~ConfigParser() = default;
    virtual std::expected<ConfigValue, std::string> parse(std::string_view content) = 0;
    virtual std::expected<std::string, std::string> serialize(const ConfigValue& value) = 0;
};

// include/common/config/yaml_parser.h
class YamlParser : public ConfigParser {
public:
    std::expected<ConfigValue, std::string> parse(std::string_view content) override;
    std::expected<std::string, std::string> serialize(const ConfigValue& value) override;
    
private:
    ConfigValue parse_yaml_node(const void* node);  // YAML::Node*
    void* serialize_to_yaml_node(const ConfigValue& value);  // YAML::Node
};

// include/common/config/json_parser.h
class JsonParser : public ConfigParser {
public:
    std::expected<ConfigValue, std::string> parse(std::string_view content) override;
    std::expected<std::string, std::string> serialize(const ConfigValue& value) override;
    
private:
    ConfigValue parse_json_value(const nlohmann::json& json);
    nlohmann::json serialize_to_json(const ConfigValue& value);
};

// include/common/config/config_manager.h
class ConfigManager {
public:
    static ConfigManager& instance();
    
    // 加载配置
    std::expected<void, std::string> load_from_file(const std::filesystem::path& file_path);
    std::expected<void, std::string> load_from_string(std::string_view content, const std::string& format);
    std::expected<void, std::string> load_from_env();
    
    // 获取配置值
    template<typename T>
    T get(const std::string& key) const;
    
    template<typename T>
    T get_or(const std::string& key, T default_value) const;
    
    bool has(const std::string& key) const;
    
    // 设置配置值
    void set(const std::string& key, const ConfigValue& value);
    
    // 保存配置
    std::expected<void, std::string> save_to_file(const std::filesystem::path& file_path);
    
    // 热重载
    void enable_hot_reload(const std::filesystem::path& file_path);
    void disable_hot_reload();
    
    // 配置变更通知
    using ChangeCallback = std::function<void(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value)>;
    void add_change_listener(const std::string& key, ChangeCallback callback);
    void remove_change_listener(const std::string& key);
    
private:
    ConfigValue root_;
    std::filesystem::path config_file_;
    std::filesystem::file_time_type last_write_time_;
    
    // 热重载
    std::jthread hot_reload_thread_;
    std::atomic<bool> hot_reload_enabled_{false};
    
    // 变更监听
    std::unordered_map<std::string, std::vector<ChangeCallback>> change_listeners_;
    mutable std::shared_mutex config_mutex_;
    
    // 解析器
    std::unordered_map<std::string, std::unique_ptr<ConfigParser>> parsers_;
    
    ConfigManager();
    void init_parsers();
    void hot_reload_worker(std::stop_token stop_token);
    void notify_change(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value);
    std::vector<std::string> split_key(const std::string& key) const;
    ConfigValue* find_value(const std::string& key);
    const ConfigValue* find_value(const std::string& key) const;
};
```

##### 2.2 具体开发任务

**Day 3 任务清单**:
```cpp
✅ 实现 ConfigValue 类
   - 所有构造函数
   - as<T>() 模板方法
   - get_or<T>() 模板方法
   - 类型检查方法 (is_null, is_bool, etc.)
   - operator[] 重载
✅ 实现 ConfigParser 基类
✅ 实现 YamlParser 类
   - parse() 方法 (使用yaml-cpp库)
   - serialize() 方法
   - parse_yaml_node() 私有方法
   - serialize_to_yaml_node() 私有方法
✅ 编写 ConfigValue 和 YamlParser 单元测试
```

**Day 4 任务清单**:
```cpp
✅ 实现 JsonParser 类
   - parse() 方法 (使用nlohmann/json库)
   - serialize() 方法
   - parse_json_value() 私有方法
   - serialize_to_json() 私有方法
✅ 实现 ConfigManager 单例类
   - load_from_file() 方法
   - load_from_string() 方法
   - load_from_env() 方法
   - get<T>() 和 get_or<T>() 模板方法
   - set() 方法
   - save_to_file() 方法
   - 热重载功能 (enable_hot_reload, hot_reload_worker)
   - 变更监听功能 (add_change_listener, notify_change)
✅ 编写完整的单元测试和集成测试
✅ 性能测试 (目标: 配置读取 < 1μs)
```

#### Day 5-7: 现代化线程池 (🔥 P0)

**模块路径**: `include/common/thread_pool/` 和 `src/common/thread_pool/`

##### 3.1 核心类设计

```cpp
// include/common/thread_pool/task.h
template<typename T>
class Task {
public:
    struct promise_type {
        Task get_return_object() { return Task{std::coroutine_handle<promise_type>::from_promise(*this)}; }
        std::suspend_never initial_suspend() { return {}; }
        std::suspend_never final_suspend() noexcept { return {}; }
        void unhandled_exception() { exception_ = std::current_exception(); }

        template<typename U>
        void return_value(U&& value) { result_ = std::forward<U>(value); }

        std::optional<T> result_;
        std::exception_ptr exception_;
    };

    explicit Task(std::coroutine_handle<promise_type> handle) : handle_(handle) {}
    ~Task() { if (handle_) handle_.destroy(); }

    // 禁止拷贝，允许移动
    Task(const Task&) = delete;
    Task& operator=(const Task&) = delete;
    Task(Task&& other) noexcept : handle_(std::exchange(other.handle_, {})) {}
    Task& operator=(Task&& other) noexcept {
        if (this != &other) {
            if (handle_) handle_.destroy();
            handle_ = std::exchange(other.handle_, {});
        }
        return *this;
    }

    bool is_ready() const { return handle_ && handle_.done(); }
    T get() {
        if (!handle_) throw std::runtime_error("Invalid task");
        if (!handle_.done()) throw std::runtime_error("Task not completed");

        auto& promise = handle_.promise();
        if (promise.exception_) std::rethrow_exception(promise.exception_);
        if (!promise.result_) throw std::runtime_error("No result available");

        return std::move(*promise.result_);
    }

private:
    std::coroutine_handle<promise_type> handle_;
};

// include/common/thread_pool/work_stealing_queue.h
template<typename T>
class WorkStealingQueue {
public:
    explicit WorkStealingQueue(size_t capacity = 1024);
    ~WorkStealingQueue();

    bool push(T item);
    bool pop(T& item);
    bool steal(T& item);

    size_t size() const;
    bool empty() const;

private:
    struct Node {
        std::atomic<T*> data{nullptr};
        std::atomic<Node*> next{nullptr};
    };

    std::atomic<Node*> head_{nullptr};
    std::atomic<Node*> tail_{nullptr};
    std::atomic<size_t> size_{0};
    size_t capacity_;

    Node* allocate_node();
    void deallocate_node(Node* node);
};

// include/common/thread_pool/thread_pool_executor.h
class ThreadPoolExecutor {
public:
    struct Config {
        size_t core_threads = std::thread::hardware_concurrency();
        size_t max_threads = std::thread::hardware_concurrency() * 2;
        std::chrono::seconds keep_alive_time{60};
        size_t queue_capacity = 10000;
        bool enable_work_stealing = true;
        std::string thread_name_prefix = "ThreadPool";
    };

    explicit ThreadPoolExecutor(Config config = {});
    ~ThreadPoolExecutor();

    // 提交任务
    template<typename F, typename... Args>
    auto submit(F&& f, Args&&... args) -> std::future<std::invoke_result_t<F, Args...>>;

    template<typename F, typename... Args>
    auto submit_coroutine(F&& f, Args&&... args) -> Task<std::invoke_result_t<F, Args...>>;

    // 批量提交
    template<typename Iterator>
    void submit_batch(Iterator first, Iterator last);

    // 控制方法
    void shutdown();
    void shutdown_now();
    bool await_termination(std::chrono::milliseconds timeout);

    // 状态查询
    size_t active_thread_count() const;
    size_t pending_task_count() const;
    size_t completed_task_count() const;
    bool is_shutdown() const;

private:
    Config config_;

    // 任务队列
    using TaskFunction = std::function<void()>;
    std::queue<TaskFunction> global_queue_;
    std::vector<std::unique_ptr<WorkStealingQueue<TaskFunction>>> worker_queues_;

    // 线程管理
    std::vector<std::jthread> workers_;
    std::atomic<bool> shutdown_{false};
    std::atomic<bool> shutdown_now_{false};

    // 同步原语
    std::mutex global_queue_mutex_;
    std::condition_variable task_available_;
    std::condition_variable termination_;

    // 统计信息
    std::atomic<size_t> active_threads_{0};
    std::atomic<size_t> pending_tasks_{0};
    std::atomic<size_t> completed_tasks_{0};

    // 内部方法
    void worker_loop(std::stop_token stop_token, size_t worker_id);
    bool try_get_task(TaskFunction& task, size_t worker_id);
    bool try_steal_task(TaskFunction& task, size_t worker_id);
    void adjust_thread_pool_size();
    void set_thread_name(const std::string& name);
};

// include/common/thread_pool/scheduled_executor.h
class ScheduledExecutor {
public:
    struct Config {
        size_t core_threads = 2;
        std::string thread_name_prefix = "ScheduledPool";
    };

    explicit ScheduledExecutor(Config config = {});
    ~ScheduledExecutor();

    // 延迟执行
    template<typename F, typename... Args>
    auto schedule(std::chrono::milliseconds delay, F&& f, Args&&... args)
        -> std::future<std::invoke_result_t<F, Args...>>;

    // 定期执行
    template<typename F, typename... Args>
    auto schedule_at_fixed_rate(std::chrono::milliseconds initial_delay,
                               std::chrono::milliseconds period,
                               F&& f, Args&&... args) -> std::shared_ptr<void>;

    template<typename F, typename... Args>
    auto schedule_with_fixed_delay(std::chrono::milliseconds initial_delay,
                                  std::chrono::milliseconds delay,
                                  F&& f, Args&&... args) -> std::shared_ptr<void>;

    // 取消任务
    void cancel_task(std::shared_ptr<void> task_handle);

    void shutdown();
    bool await_termination(std::chrono::milliseconds timeout);

private:
    Config config_;

    struct ScheduledTask {
        std::function<void()> task;
        std::chrono::steady_clock::time_point next_execution;
        std::chrono::milliseconds period{0};
        bool is_fixed_rate = false;
        bool cancelled = false;
        size_t task_id;
    };

    std::priority_queue<std::shared_ptr<ScheduledTask>,
                       std::vector<std::shared_ptr<ScheduledTask>>,
                       std::function<bool(const std::shared_ptr<ScheduledTask>&,
                                        const std::shared_ptr<ScheduledTask>&)>> task_queue_;

    std::vector<std::jthread> workers_;
    std::atomic<bool> shutdown_{false};
    std::atomic<size_t> next_task_id_{1};

    std::mutex queue_mutex_;
    std::condition_variable task_available_;

    void worker_loop(std::stop_token stop_token);
};

// include/common/thread_pool/thread_pool_manager.h
class ThreadPoolManager {
public:
    static ThreadPoolManager& instance();

    // 获取默认线程池
    ThreadPoolExecutor& default_executor();
    ScheduledExecutor& scheduled_executor();

    // 创建自定义线程池
    std::shared_ptr<ThreadPoolExecutor> create_executor(const std::string& name,
                                                        ThreadPoolExecutor::Config config);
    std::shared_ptr<ScheduledExecutor> create_scheduled_executor(const std::string& name,
                                                                ScheduledExecutor::Config config);

    // 获取已创建的线程池
    std::shared_ptr<ThreadPoolExecutor> get_executor(const std::string& name);
    std::shared_ptr<ScheduledExecutor> get_scheduled_executor(const std::string& name);

    // 关闭所有线程池
    void shutdown_all();
    bool await_termination_all(std::chrono::milliseconds timeout);

    // 统计信息
    struct Statistics {
        size_t total_executors;
        size_t total_threads;
        size_t active_threads;
        size_t pending_tasks;
        size_t completed_tasks;
    };

    Statistics get_statistics() const;

private:
    std::unique_ptr<ThreadPoolExecutor> default_executor_;
    std::unique_ptr<ScheduledExecutor> scheduled_executor_;

    std::unordered_map<std::string, std::shared_ptr<ThreadPoolExecutor>> executors_;
    std::unordered_map<std::string, std::shared_ptr<ScheduledExecutor>> scheduled_executors_;

    mutable std::shared_mutex executors_mutex_;

    ThreadPoolManager();
};
```

##### 3.2 具体开发任务

**Day 5 任务清单**:
```cpp
✅ 实现 Task<T> 协程类
   - promise_type 结构体
   - 构造函数和析构函数
   - 移动语义支持
   - is_ready() 和 get() 方法
✅ 实现 WorkStealingQueue<T> 模板类
   - 构造函数和析构函数
   - push(), pop(), steal() 方法
   - size() 和 empty() 方法
   - Node 结构体和内存管理
✅ 编写 Task 和 WorkStealingQueue 单元测试
```

**Day 6 任务清单**:
```cpp
✅ 实现 ThreadPoolExecutor 类
   - 构造函数和析构函数
   - submit() 模板方法
   - submit_coroutine() 模板方法
   - submit_batch() 模板方法
   - worker_loop() 私有方法
   - try_get_task() 和 try_steal_task() 私有方法
   - shutdown() 和 shutdown_now() 方法
   - 状态查询方法
✅ 编写 ThreadPoolExecutor 单元测试
✅ 性能测试 (目标: 100万任务/秒)
```

**Day 7 任务清单**:
```cpp
✅ 实现 ScheduledExecutor 类
   - 构造函数和析构函数
   - schedule() 模板方法
   - schedule_at_fixed_rate() 模板方法
   - schedule_with_fixed_delay() 模板方法
   - cancel_task() 方法
   - worker_loop() 私有方法
✅ 实现 ThreadPoolManager 单例类
   - default_executor() 和 scheduled_executor() 方法
   - create_executor() 和 create_scheduled_executor() 方法
   - get_executor() 和 get_scheduled_executor() 方法
   - shutdown_all() 和 await_termination_all() 方法
   - get_statistics() 方法
✅ 编写完整的集成测试
✅ 压力测试和内存泄漏检测
```

### 第2周：网络和数据库模块

#### Day 8-10: 网络模块完善 (🔥 P0)

**模块路径**: `include/common/network/` 和 `src/common/network/`

##### 4.1 核心类设计

```cpp
// include/common/network/socket.h
class ModernSocket {
public:
    enum class Type {
        TCP,
        UDP,
        Unix
    };

    enum class State {
        Closed,
        Connecting,
        Connected,
        Listening,
        Error
    };

    explicit ModernSocket(Type type);
    ~ModernSocket();

    // 禁止拷贝，允许移动
    ModernSocket(const ModernSocket&) = delete;
    ModernSocket& operator=(const ModernSocket&) = delete;
    ModernSocket(ModernSocket&& other) noexcept;
    ModernSocket& operator=(ModernSocket&& other) noexcept;

    // 连接操作
    [[nodiscard]] std::expected<void, std::string> connect(const std::string& host, uint16_t port);
    [[nodiscard]] std::expected<void, std::string> bind(const std::string& address, uint16_t port);
    [[nodiscard]] std::expected<void, std::string> listen(int backlog = 128);
    [[nodiscard]] std::expected<std::unique_ptr<ModernSocket>, std::string> accept();

    // 数据传输
    [[nodiscard]] std::expected<size_t, std::string> send(std::span<const std::byte> data);
    [[nodiscard]] std::expected<size_t, std::string> receive(std::span<std::byte> buffer);
    [[nodiscard]] std::expected<std::vector<std::byte>, std::string> receive_all(size_t max_size = 64 * 1024);

    // 异步操作
    [[nodiscard]] Task<std::expected<size_t, std::string>> send_async(std::span<const std::byte> data);
    [[nodiscard]] Task<std::expected<size_t, std::string>> receive_async(std::span<std::byte> buffer);

    // 控制操作
    void close();
    void shutdown(int how = SHUT_RDWR);

    // 状态查询
    [[nodiscard]] State state() const noexcept { return state_; }
    [[nodiscard]] bool is_connected() const noexcept { return state_ == State::Connected; }
    [[nodiscard]] int native_handle() const noexcept { return fd_; }

    // 地址信息
    [[nodiscard]] std::string local_address() const;
    [[nodiscard]] uint16_t local_port() const;
    [[nodiscard]] std::string remote_address() const;
    [[nodiscard]] uint16_t remote_port() const;

    // 选项设置
    void set_non_blocking(bool non_blocking);
    void set_reuse_address(bool reuse);
    void set_keep_alive(bool keep_alive);
    void set_tcp_no_delay(bool no_delay);
    void set_receive_timeout(std::chrono::milliseconds timeout);
    void set_send_timeout(std::chrono::milliseconds timeout);

private:
    Type type_;
    State state_ = State::Closed;
    int fd_ = -1;

    void update_state();
    [[nodiscard]] std::expected<void, std::string> set_socket_option(int level, int option, const void* value, socklen_t len);
};

// include/common/network/event_loop.h
class EventLoop {
public:
    enum class EventType {
        Read = 1 << 0,
        Write = 1 << 1,
        Error = 1 << 2,
        HangUp = 1 << 3
    };

    using EventCallback = std::function<void(int fd, EventType events)>;

    explicit EventLoop(size_t max_events = 1024);
    ~EventLoop();

    // 禁止拷贝和移动
    EventLoop(const EventLoop&) = delete;
    EventLoop& operator=(const EventLoop&) = delete;
    EventLoop(EventLoop&&) = delete;
    EventLoop& operator=(EventLoop&&) = delete;

    // 事件注册
    [[nodiscard]] std::expected<void, std::string> add_fd(int fd, EventType events, EventCallback callback);
    [[nodiscard]] std::expected<void, std::string> modify_fd(int fd, EventType events);
    [[nodiscard]] std::expected<void, std::string> remove_fd(int fd);

    // 定时器
    using TimerCallback = std::function<void()>;
    [[nodiscard]] uint64_t add_timer(std::chrono::milliseconds delay, TimerCallback callback, bool repeat = false);
    void cancel_timer(uint64_t timer_id);

    // 事件循环控制
    void run();
    void run_once(std::chrono::milliseconds timeout = std::chrono::milliseconds{-1});
    void stop();

    // 线程安全的任务提交
    void post(std::function<void()> task);

    // 状态查询
    [[nodiscard]] bool is_running() const noexcept { return running_; }
    [[nodiscard]] size_t fd_count() const noexcept { return fd_callbacks_.size(); }

private:
    struct TimerInfo {
        uint64_t id;
        std::chrono::steady_clock::time_point next_execution;
        std::chrono::milliseconds interval;
        TimerCallback callback;
        bool repeat;
        bool cancelled = false;
    };

    int epoll_fd_ = -1;
    int event_fd_ = -1;  // 用于线程间通信
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};

    // 事件管理
    std::unordered_map<int, EventCallback> fd_callbacks_;
    std::vector<struct epoll_event> events_;

    // 定时器管理
    std::priority_queue<std::shared_ptr<TimerInfo>,
                       std::vector<std::shared_ptr<TimerInfo>>,
                       std::function<bool(const std::shared_ptr<TimerInfo>&,
                                        const std::shared_ptr<TimerInfo>&)>> timers_;
    std::atomic<uint64_t> next_timer_id_{1};

    // 任务队列
    std::queue<std::function<void()>> pending_tasks_;
    std::mutex tasks_mutex_;

    void handle_events(int timeout_ms);
    void handle_timers();
    void handle_pending_tasks();
    void handle_event_fd();

    [[nodiscard]] std::chrono::milliseconds get_next_timer_timeout() const;
};

// include/common/network/tcp_server.h
class TcpServer {
public:
    struct Config {
        std::string bind_address = "0.0.0.0";
        uint16_t port = 8080;
        int backlog = 128;
        size_t worker_threads = std::thread::hardware_concurrency();
        bool reuse_address = true;
        bool tcp_no_delay = true;
        std::chrono::seconds keep_alive_timeout{60};
    };

    using ConnectionHandler = std::function<Task<void>(std::unique_ptr<ModernSocket>)>;

    explicit TcpServer(Config config);
    ~TcpServer();

    void set_connection_handler(ConnectionHandler handler);

    [[nodiscard]] std::expected<void, std::string> start();
    [[nodiscard]] std::expected<void, std::string> stop();

    [[nodiscard]] bool is_running() const noexcept { return running_; }
    [[nodiscard]] size_t active_connections() const noexcept { return active_connections_; }

private:
    Config config_;
    ConnectionHandler connection_handler_;

    std::unique_ptr<ModernSocket> listen_socket_;
    std::unique_ptr<EventLoop> event_loop_;
    std::unique_ptr<thread_pool::ThreadPoolExecutor> thread_pool_;

    std::atomic<bool> running_{false};
    std::atomic<size_t> active_connections_{0};
    std::jthread accept_thread_;

    void accept_loop(std::stop_token stop_token);
    Task<void> handle_connection(std::unique_ptr<ModernSocket> client);
};

// include/common/network/tcp_client.h
class TcpClient {
public:
    struct Config {
        std::chrono::seconds connect_timeout{10};
        std::chrono::seconds read_timeout{30};
        std::chrono::seconds write_timeout{30};
        bool tcp_no_delay = true;
        bool keep_alive = true;
        size_t max_reconnect_attempts = 3;
        std::chrono::seconds reconnect_delay{5};
    };

    explicit TcpClient(Config config = {});
    ~TcpClient();

    [[nodiscard]] std::expected<void, std::string> connect(const std::string& host, uint16_t port);
    void disconnect();

    [[nodiscard]] std::expected<size_t, std::string> send(std::span<const std::byte> data);
    [[nodiscard]] std::expected<std::vector<std::byte>, std::string> receive(size_t max_size = 64 * 1024);

    [[nodiscard]] Task<std::expected<size_t, std::string>> send_async(std::span<const std::byte> data);
    [[nodiscard]] Task<std::expected<std::vector<std::byte>, std::string>> receive_async(size_t max_size = 64 * 1024);

    [[nodiscard]] bool is_connected() const;

    // 自动重连
    void enable_auto_reconnect(bool enable);

private:
    Config config_;
    std::unique_ptr<ModernSocket> socket_;
    std::string host_;
    uint16_t port_ = 0;

    std::atomic<bool> auto_reconnect_{false};
    std::atomic<size_t> reconnect_attempts_{0};

    [[nodiscard]] std::expected<void, std::string> do_reconnect();
};
```

##### 4.2 具体开发任务

**Day 8 任务清单**:
```cpp
✅ 完善 ModernSocket 类
   - 构造函数和析构函数
   - 移动语义支持
   - connect(), bind(), listen(), accept() 方法
   - send(), receive(), receive_all() 方法
   - send_async(), receive_async() 协程方法
   - 地址信息查询方法
   - 套接字选项设置方法
✅ 编写 ModernSocket 单元测试
✅ 跨平台兼容性测试 (Linux/Windows)
```

**Day 9 任务清单**:
```cpp
✅ 实现 EventLoop 类
   - 构造函数和析构函数
   - add_fd(), modify_fd(), remove_fd() 方法
   - add_timer(), cancel_timer() 方法
   - run(), run_once(), stop() 方法
   - post() 线程安全任务提交
   - handle_events(), handle_timers() 私有方法
✅ 编写 EventLoop 单元测试
✅ 性能测试 (目标: 10万连接并发)
```

**Day 10 任务清单**:
```cpp
✅ 实现 TcpServer 类
   - 构造函数和析构函数
   - set_connection_handler() 方法
   - start(), stop() 方法
   - accept_loop() 私有方法
   - handle_connection() 协程方法
✅ 实现 TcpClient 类
   - 构造函数和析构函数
   - connect(), disconnect() 方法
   - send(), receive() 同步方法
   - send_async(), receive_async() 异步方法
   - 自动重连功能
✅ 编写完整的网络模块集成测试
✅ 压力测试和稳定性测试
```

#### Day 11-14: 数据库连接池 (🔥 P0)

**模块路径**: `include/common/database/` 和 `src/common/database/`

##### 5.1 MySQL连接池核心类设计

```cpp
// include/common/database/mysql/mysql_connection.h
class MySQLConnection {
public:
    struct Config {
        std::string host = "localhost";
        uint16_t port = 3306;
        std::string username;
        std::string password;
        std::string database;
        std::string charset = "utf8mb4";
        bool auto_commit = true;
        std::chrono::seconds connect_timeout{10};
        std::chrono::seconds read_timeout{30};
        std::chrono::seconds write_timeout{30};
    };

    explicit MySQLConnection(Config config);
    ~MySQLConnection();

    // 禁止拷贝，允许移动
    MySQLConnection(const MySQLConnection&) = delete;
    MySQLConnection& operator=(const MySQLConnection&) = delete;
    MySQLConnection(MySQLConnection&& other) noexcept;
    MySQLConnection& operator=(MySQLConnection&& other) noexcept;

    // 连接管理
    [[nodiscard]] std::expected<void, std::string> connect();
    void disconnect();
    [[nodiscard]] bool is_connected() const;
    [[nodiscard]] std::expected<void, std::string> ping();

    // 事务管理
    [[nodiscard]] std::expected<void, std::string> begin_transaction();
    [[nodiscard]] std::expected<void, std::string> commit();
    [[nodiscard]] std::expected<void, std::string> rollback();

    // 查询执行
    [[nodiscard]] std::expected<void, std::string> execute(std::string_view sql);

    template<typename... Args>
    [[nodiscard]] std::expected<void, std::string> execute(std::string_view sql, Args&&... args);

    [[nodiscard]] std::expected<std::unique_ptr<MySQLResultSet>, std::string>
    query(std::string_view sql);

    template<typename... Args>
    [[nodiscard]] std::expected<std::unique_ptr<MySQLResultSet>, std::string>
    query(std::string_view sql, Args&&... args);

    // 预处理语句
    [[nodiscard]] std::expected<std::unique_ptr<MySQLPreparedStatement>, std::string>
    prepare(std::string_view sql);

    // 连接信息
    [[nodiscard]] uint64_t last_insert_id() const;
    [[nodiscard]] uint64_t affected_rows() const;
    [[nodiscard]] std::string escape_string(std::string_view str) const;

    // 连接状态
    [[nodiscard]] std::chrono::system_clock::time_point last_used() const { return last_used_; }
    void update_last_used() { last_used_ = std::chrono::system_clock::now(); }

private:
    Config config_;
    MYSQL* mysql_ = nullptr;
    std::chrono::system_clock::time_point last_used_;
    mutable std::mutex mutex_;

    [[nodiscard]] std::expected<void, std::string> set_connection_options();
    [[nodiscard]] std::string get_error_message() const;
};

// include/common/database/mysql/mysql_result_set.h
class MySQLResultSet {
public:
    explicit MySQLResultSet(MYSQL_RES* result);
    ~MySQLResultSet();

    // 禁止拷贝，允许移动
    MySQLResultSet(const MySQLResultSet&) = delete;
    MySQLResultSet& operator=(const MySQLResultSet&) = delete;
    MySQLResultSet(MySQLResultSet&& other) noexcept;
    MySQLResultSet& operator=(MySQLResultSet&& other) noexcept;

    // 结果集导航
    [[nodiscard]] bool next();
    [[nodiscard]] bool has_data() const;
    [[nodiscard]] size_t row_count() const;
    [[nodiscard]] size_t column_count() const;

    // 数据获取
    template<typename T>
    [[nodiscard]] std::optional<T> get(size_t column_index) const;

    template<typename T>
    [[nodiscard]] std::optional<T> get(std::string_view column_name) const;

    template<typename T>
    [[nodiscard]] T get_or(size_t column_index, T default_value) const;

    template<typename T>
    [[nodiscard]] T get_or(std::string_view column_name, T default_value) const;

    // 列信息
    [[nodiscard]] std::vector<std::string> column_names() const;
    [[nodiscard]] std::optional<size_t> column_index(std::string_view column_name) const;
    [[nodiscard]] bool is_null(size_t column_index) const;

    // 便利方法
    [[nodiscard]] std::unordered_map<std::string, std::string> to_map() const;
    [[nodiscard]] std::vector<std::unordered_map<std::string, std::string>> to_vector() const;

private:
    MYSQL_RES* result_ = nullptr;
    MYSQL_ROW current_row_ = nullptr;
    std::vector<std::string> column_names_;
    std::unordered_map<std::string, size_t> column_indices_;

    void initialize_column_info();
    [[nodiscard]] std::string get_raw_value(size_t column_index) const;
};

// include/common/database/mysql/mysql_prepared_statement.h
class MySQLPreparedStatement {
public:
    explicit MySQLPreparedStatement(MYSQL_STMT* stmt);
    ~MySQLPreparedStatement();

    // 禁止拷贝，允许移动
    MySQLPreparedStatement(const MySQLPreparedStatement&) = delete;
    MySQLPreparedStatement& operator=(const MySQLPreparedStatement&) = delete;
    MySQLPreparedStatement(MySQLPreparedStatement&& other) noexcept;
    MySQLPreparedStatement& operator=(MySQLPreparedStatement&& other) noexcept;

    // 参数绑定
    template<typename... Args>
    [[nodiscard]] std::expected<void, std::string> bind(Args&&... args);

    // 执行
    [[nodiscard]] std::expected<void, std::string> execute();
    [[nodiscard]] std::expected<std::unique_ptr<MySQLResultSet>, std::string> execute_query();

    // 批量执行
    template<typename Container>
    [[nodiscard]] std::expected<void, std::string> execute_batch(const Container& params);

    // 状态查询
    [[nodiscard]] uint64_t affected_rows() const;
    [[nodiscard]] uint64_t last_insert_id() const;

private:
    MYSQL_STMT* stmt_ = nullptr;
    std::vector<MYSQL_BIND> bind_params_;
    std::vector<std::unique_ptr<std::byte[]>> param_buffers_;

    template<typename T>
    void bind_parameter(size_t index, T&& value);

    [[nodiscard]] std::string get_error_message() const;
};

// include/common/database/mysql/mysql_connection_pool.h
class MySQLConnectionPool {
public:
    struct Config {
        MySQLConnection::Config connection_config;
        size_t initial_size = 5;
        size_t max_size = 20;
        size_t min_idle = 2;
        std::chrono::seconds max_idle_time{300};  // 5分钟
        std::chrono::seconds validation_interval{30};
        std::chrono::seconds connection_timeout{10};
        bool test_on_borrow = true;
        bool test_on_return = false;
        bool test_while_idle = true;
        std::string validation_query = "SELECT 1";
    };

    explicit MySQLConnectionPool(Config config);
    ~MySQLConnectionPool();

    // 连接管理
    [[nodiscard]] std::expected<std::unique_ptr<MySQLConnection>, std::string> get_connection();
    [[nodiscard]] std::expected<std::unique_ptr<MySQLConnection>, std::string>
    get_connection(std::chrono::milliseconds timeout);

    void return_connection(std::unique_ptr<MySQLConnection> connection);

    // 连接池控制
    [[nodiscard]] std::expected<void, std::string> initialize();
    void shutdown();

    // 状态查询
    [[nodiscard]] size_t active_connections() const;
    [[nodiscard]] size_t idle_connections() const;
    [[nodiscard]] size_t total_connections() const;

    // 统计信息
    struct Statistics {
        std::atomic<uint64_t> connections_created{0};
        std::atomic<uint64_t> connections_destroyed{0};
        std::atomic<uint64_t> connections_borrowed{0};
        std::atomic<uint64_t> connections_returned{0};
        std::atomic<uint64_t> connection_timeouts{0};
        std::atomic<uint64_t> validation_failures{0};
    };

    [[nodiscard]] const Statistics& statistics() const { return stats_; }

private:
    Config config_;

    struct PooledConnection {
        std::unique_ptr<MySQLConnection> connection;
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point last_used;
        bool in_use = false;
    };

    std::vector<std::unique_ptr<PooledConnection>> connections_;
    std::queue<PooledConnection*> available_connections_;

    mutable std::mutex pool_mutex_;
    std::condition_variable connection_available_;

    // 后台维护
    std::jthread maintenance_thread_;
    std::atomic<bool> shutdown_requested_{false};

    mutable Statistics stats_;

    // 内部方法
    [[nodiscard]] std::expected<std::unique_ptr<PooledConnection>, std::string> create_connection();
    [[nodiscard]] bool validate_connection(MySQLConnection* connection);
    void maintenance_loop(std::stop_token stop_token);
    void cleanup_idle_connections();
    void ensure_minimum_connections();
};
```

##### 5.2 Redis连接池核心类设计

```cpp
// include/common/database/redis/redis_connection.h
class RedisConnection {
public:
    struct Config {
        std::string host = "localhost";
        uint16_t port = 6379;
        std::string password;
        int database = 0;
        std::chrono::seconds connect_timeout{10};
        std::chrono::seconds command_timeout{30};
        bool keep_alive = true;
    };

    explicit RedisConnection(Config config);
    ~RedisConnection();

    // 禁止拷贝，允许移动
    RedisConnection(const RedisConnection&) = delete;
    RedisConnection& operator=(const RedisConnection&) = delete;
    RedisConnection(RedisConnection&& other) noexcept;
    RedisConnection& operator=(RedisConnection&& other) noexcept;

    // 连接管理
    [[nodiscard]] std::expected<void, std::string> connect();
    void disconnect();
    [[nodiscard]] bool is_connected() const;
    [[nodiscard]] std::expected<void, std::string> ping();

    // 基础命令
    [[nodiscard]] std::expected<std::string, std::string> get(std::string_view key);
    [[nodiscard]] std::expected<void, std::string> set(std::string_view key, std::string_view value);
    [[nodiscard]] std::expected<void, std::string> set(std::string_view key, std::string_view value, std::chrono::seconds ttl);
    [[nodiscard]] std::expected<bool, std::string> exists(std::string_view key);
    [[nodiscard]] std::expected<bool, std::string> del(std::string_view key);

    // 哈希操作
    [[nodiscard]] std::expected<std::string, std::string> hget(std::string_view key, std::string_view field);
    [[nodiscard]] std::expected<void, std::string> hset(std::string_view key, std::string_view field, std::string_view value);
    [[nodiscard]] std::expected<std::unordered_map<std::string, std::string>, std::string> hgetall(std::string_view key);

    // 列表操作
    [[nodiscard]] std::expected<void, std::string> lpush(std::string_view key, std::string_view value);
    [[nodiscard]] std::expected<void, std::string> rpush(std::string_view key, std::string_view value);
    [[nodiscard]] std::expected<std::optional<std::string>, std::string> lpop(std::string_view key);
    [[nodiscard]] std::expected<std::optional<std::string>, std::string> rpop(std::string_view key);

    // 集合操作
    [[nodiscard]] std::expected<void, std::string> sadd(std::string_view key, std::string_view member);
    [[nodiscard]] std::expected<bool, std::string> sismember(std::string_view key, std::string_view member);
    [[nodiscard]] std::expected<std::vector<std::string>, std::string> smembers(std::string_view key);

    // 发布订阅
    [[nodiscard]] std::expected<void, std::string> publish(std::string_view channel, std::string_view message);
    [[nodiscard]] std::expected<void, std::string> subscribe(std::string_view channel);
    [[nodiscard]] std::expected<void, std::string> unsubscribe(std::string_view channel);

    // 通用命令
    [[nodiscard]] std::expected<void, std::string> expire(std::string_view key, std::chrono::seconds ttl);
    [[nodiscard]] std::expected<std::chrono::seconds, std::string> ttl(std::string_view key);

    // 原始命令执行
    template<typename... Args>
    [[nodiscard]] std::expected<RedisReply, std::string> execute(std::string_view command, Args&&... args);

    // 连接状态
    [[nodiscard]] std::chrono::system_clock::time_point last_used() const { return last_used_; }
    void update_last_used() { last_used_ = std::chrono::system_clock::now(); }

private:
    Config config_;
    redisContext* context_ = nullptr;
    std::chrono::system_clock::time_point last_used_;
    mutable std::mutex mutex_;

    [[nodiscard]] std::expected<void, std::string> authenticate();
    [[nodiscard]] std::expected<void, std::string> select_database();
    [[nodiscard]] std::string get_error_message() const;
};

// include/common/database/redis/redis_connection_pool.h
class RedisConnectionPool {
public:
    struct Config {
        RedisConnection::Config connection_config;
        size_t initial_size = 5;
        size_t max_size = 20;
        size_t min_idle = 2;
        std::chrono::seconds max_idle_time{300};
        std::chrono::seconds validation_interval{30};
        std::chrono::seconds connection_timeout{10};
        bool test_on_borrow = true;
        bool test_while_idle = true;
    };

    explicit RedisConnectionPool(Config config);
    ~RedisConnectionPool();

    // 连接管理
    [[nodiscard]] std::expected<std::unique_ptr<RedisConnection>, std::string> get_connection();
    [[nodiscard]] std::expected<std::unique_ptr<RedisConnection>, std::string>
    get_connection(std::chrono::milliseconds timeout);

    void return_connection(std::unique_ptr<RedisConnection> connection);

    // 连接池控制
    [[nodiscard]] std::expected<void, std::string> initialize();
    void shutdown();

    // 状态查询
    [[nodiscard]] size_t active_connections() const;
    [[nodiscard]] size_t idle_connections() const;
    [[nodiscard]] size_t total_connections() const;

    // 统计信息
    struct Statistics {
        std::atomic<uint64_t> connections_created{0};
        std::atomic<uint64_t> connections_destroyed{0};
        std::atomic<uint64_t> connections_borrowed{0};
        std::atomic<uint64_t> connections_returned{0};
        std::atomic<uint64_t> connection_timeouts{0};
        std::atomic<uint64_t> validation_failures{0};
    };

    [[nodiscard]] const Statistics& statistics() const { return stats_; }

private:
    Config config_;

    struct PooledConnection {
        std::unique_ptr<RedisConnection> connection;
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point last_used;
        bool in_use = false;
    };

    std::vector<std::unique_ptr<PooledConnection>> connections_;
    std::queue<PooledConnection*> available_connections_;

    mutable std::mutex pool_mutex_;
    std::condition_variable connection_available_;

    // 后台维护
    std::jthread maintenance_thread_;
    std::atomic<bool> shutdown_requested_{false};

    mutable Statistics stats_;

    // 内部方法
    [[nodiscard]] std::expected<std::unique_ptr<PooledConnection>, std::string> create_connection();
    [[nodiscard]] bool validate_connection(RedisConnection* connection);
    void maintenance_loop(std::stop_token stop_token);
    void cleanup_idle_connections();
    void ensure_minimum_connections();
};
```

##### 5.3 具体开发任务

**Day 11 任务清单**:
```cpp
✅ 实现 MySQLConnection 类
   - 构造函数和析构函数
   - 移动语义支持
   - connect(), disconnect(), ping() 方法
   - begin_transaction(), commit(), rollback() 方法
   - execute() 和 query() 模板方法
   - prepare() 预处理语句方法
   - 连接信息查询方法
✅ 实现 MySQLResultSet 类
   - 构造函数和析构函数
   - 移动语义支持
   - next(), has_data() 导航方法
   - get<T>() 和 get_or<T>() 模板方法
   - 列信息查询方法
   - to_map() 和 to_vector() 便利方法
✅ 编写 MySQL 连接和结果集单元测试
```

**Day 12 任务清单**:
```cpp
✅ 实现 MySQLPreparedStatement 类
   - 构造函数和析构函数
   - 移动语义支持
   - bind<Args...>() 模板方法
   - execute() 和 execute_query() 方法
   - execute_batch() 批量执行方法
   - bind_parameter<T>() 私有模板方法
✅ 实现 MySQLConnectionPool 类
   - 构造函数和析构函数
   - get_connection() 方法 (带超时版本)
   - return_connection() 方法
   - initialize() 和 shutdown() 方法
   - maintenance_loop() 后台维护
   - 连接验证和清理逻辑
✅ 编写 MySQL 连接池单元测试和压力测试
```

**Day 13 任务清单**:
```cpp
✅ 实现 RedisConnection 类
   - 构造函数和析构函数
   - 移动语义支持
   - connect(), disconnect(), ping() 方法
   - 基础命令 (get, set, exists, del)
   - 哈希操作 (hget, hset, hgetall)
   - 列表操作 (lpush, rpush, lpop, rpop)
   - 集合操作 (sadd, sismember, smembers)
   - 发布订阅 (publish, subscribe, unsubscribe)
   - execute<Args...>() 原始命令执行
✅ 编写 Redis 连接单元测试
```

**Day 14 任务清单**:
```cpp
✅ 实现 RedisConnectionPool 类
   - 构造函数和析构函数
   - get_connection() 方法 (带超时版本)
   - return_connection() 方法
   - initialize() 和 shutdown() 方法
   - maintenance_loop() 后台维护
   - 连接验证和清理逻辑
✅ 编写 Redis 连接池单元测试和压力测试
✅ 数据库模块集成测试
✅ 性能基准测试 (目标: MySQL 10000 QPS, Redis 50000 QPS)
✅ 内存泄漏检测和长时间稳定性测试
```

### 第3-4周：HTTP服务器和认证系统

#### Day 15-21: HTTP服务器实现 (🚀 P1)

**模块路径**: `include/common/http/` 和 `src/common/http/`

##### 6.1 HTTP基础组件

```cpp
// include/common/http/http_method.h
enum class HttpMethod {
    GET,
    POST,
    PUT,
    DELETE,
    PATCH,
    HEAD,
    OPTIONS,
    TRACE,
    CONNECT
};

[[nodiscard]] std::string_view to_string(HttpMethod method);
[[nodiscard]] std::optional<HttpMethod> from_string(std::string_view method_str);

// include/common/http/http_status.h
enum class HttpStatus : uint16_t {
    // 1xx Informational
    Continue = 100,
    SwitchingProtocols = 101,

    // 2xx Success
    OK = 200,
    Created = 201,
    Accepted = 202,
    NoContent = 204,

    // 3xx Redirection
    MovedPermanently = 301,
    Found = 302,
    NotModified = 304,

    // 4xx Client Error
    BadRequest = 400,
    Unauthorized = 401,
    Forbidden = 403,
    NotFound = 404,
    MethodNotAllowed = 405,
    Conflict = 409,
    UnprocessableEntity = 422,
    TooManyRequests = 429,

    // 5xx Server Error
    InternalServerError = 500,
    NotImplemented = 501,
    BadGateway = 502,
    ServiceUnavailable = 503,
    GatewayTimeout = 504
};

[[nodiscard]] std::string_view to_string(HttpStatus status);
[[nodiscard]] std::string get_reason_phrase(HttpStatus status);

// include/common/http/http_headers.h
class HttpHeaders {
public:
    HttpHeaders() = default;

    // 设置头部
    void set(std::string name, std::string value);
    void add(std::string name, std::string value);
    void remove(std::string_view name);

    // 获取头部
    [[nodiscard]] std::optional<std::string> get(std::string_view name) const;
    [[nodiscard]] std::vector<std::string> get_all(std::string_view name) const;
    [[nodiscard]] bool has(std::string_view name) const;

    // 便利方法
    void set_content_type(std::string_view content_type);
    void set_content_length(size_t length);
    void set_connection(std::string_view connection);
    void set_cache_control(std::string_view cache_control);

    [[nodiscard]] std::optional<std::string> content_type() const;
    [[nodiscard]] std::optional<size_t> content_length() const;
    [[nodiscard]] std::optional<std::string> connection() const;

    // 迭代器支持
    using HeaderMap = std::multimap<std::string, std::string, std::less<>>;
    [[nodiscard]] const HeaderMap& all() const { return headers_; }

    // 序列化
    [[nodiscard]] std::string to_string() const;

private:
    HeaderMap headers_;

    [[nodiscard]] std::string normalize_name(std::string_view name) const;
};

// include/common/http/http_request.h
class HttpRequest {
public:
    HttpRequest() = default;

    // 基础属性
    [[nodiscard]] HttpMethod method() const { return method_; }
    [[nodiscard]] const std::string& path() const { return path_; }
    [[nodiscard]] const std::string& query_string() const { return query_string_; }
    [[nodiscard]] const std::string& version() const { return version_; }
    [[nodiscard]] const HttpHeaders& headers() const { return headers_; }
    [[nodiscard]] const std::vector<std::byte>& body() const { return body_; }

    // 设置方法
    void set_method(HttpMethod method) { method_ = method; }
    void set_path(std::string path) { path_ = std::move(path); }
    void set_query_string(std::string query_string) { query_string_ = std::move(query_string); }
    void set_version(std::string version) { version_ = std::move(version); }
    void set_body(std::vector<std::byte> body) { body_ = std::move(body); }

    // 头部操作
    HttpHeaders& headers() { return headers_; }

    // 查询参数
    [[nodiscard]] std::unordered_map<std::string, std::string> query_params() const;
    [[nodiscard]] std::optional<std::string> query_param(std::string_view name) const;

    // 路径参数 (由路由器设置)
    [[nodiscard]] std::optional<std::string> path_param(std::string_view name) const;
    void set_path_param(std::string name, std::string value);

    // 请求体处理
    [[nodiscard]] std::string body_as_string() const;
    [[nodiscard]] std::expected<nlohmann::json, std::string> body_as_json() const;

    // 客户端信息
    [[nodiscard]] std::string client_address() const { return client_address_; }
    void set_client_address(std::string address) { client_address_ = std::move(address); }

    // 请求时间
    [[nodiscard]] std::chrono::system_clock::time_point timestamp() const { return timestamp_; }
    void set_timestamp(std::chrono::system_clock::time_point timestamp) { timestamp_ = timestamp; }

private:
    HttpMethod method_ = HttpMethod::GET;
    std::string path_;
    std::string query_string_;
    std::string version_ = "HTTP/1.1";
    HttpHeaders headers_;
    std::vector<std::byte> body_;
    std::string client_address_;
    std::chrono::system_clock::time_point timestamp_;

    std::unordered_map<std::string, std::string> path_params_;
};

// include/common/http/http_response.h
class HttpResponse {
public:
    HttpResponse() = default;

    // 状态设置
    [[nodiscard]] HttpStatus status() const { return status_; }
    void set_status(HttpStatus status) { status_ = status; }

    // 头部操作
    [[nodiscard]] const HttpHeaders& headers() const { return headers_; }
    HttpHeaders& headers() { return headers_; }

    // 响应体
    [[nodiscard]] const std::vector<std::byte>& body() const { return body_; }
    void set_body(std::vector<std::byte> body) { body_ = std::move(body); }

    // 便利方法
    void set_text(std::string_view text);
    void set_html(std::string_view html);
    void set_json(const nlohmann::json& json);
    void set_file(const std::filesystem::path& file_path);
    void set_error(HttpStatus status, std::string_view message);

    // 重定向
    void redirect(std::string_view location, HttpStatus status = HttpStatus::Found);

    // Cookie操作
    void set_cookie(std::string name, std::string value,
                   std::optional<std::chrono::seconds> max_age = std::nullopt,
                   std::optional<std::string> domain = std::nullopt,
                   std::optional<std::string> path = std::nullopt,
                   bool secure = false, bool http_only = false);

    // 序列化
    [[nodiscard]] std::vector<std::byte> serialize() const;

private:
    HttpStatus status_ = HttpStatus::OK;
    HttpHeaders headers_;
    std::vector<std::byte> body_;
};
```

##### 6.2 具体开发任务

**Day 15-16 任务清单**:
```cpp
✅ 实现 HTTP 基础组件
   - HttpMethod 枚举和转换函数
   - HttpStatus 枚举和状态码处理
   - HttpHeaders 类 (设置、获取、便利方法)
   - HttpRequest 类 (所有属性和方法)
   - HttpResponse 类 (状态、头部、响应体、便利方法)
✅ 编写 HTTP 基础组件单元测试
✅ HTTP 协议解析和序列化测试
```

**Day 17-18 任务清单**:
```cpp
✅ 实现 HTTP 解析器
   - HttpRequestParser 类 (解析HTTP请求)
   - HttpResponseSerializer 类 (序列化HTTP响应)
   - 分块传输编码支持
   - Keep-Alive 连接处理
✅ 实现路由系统
   - Route 类 (路径模式匹配)
   - Router 类 (路由注册和查找)
   - 路径参数提取
   - 中间件支持
✅ 编写解析器和路由系统单元测试
```

**Day 19-21 任务清单**:
```cpp
✅ 实现 HTTP 服务器核心
   - HttpServer 类 (服务器主体)
   - HttpConnection 类 (连接处理)
   - 中间件管道
   - 静态文件服务
   - WebSocket 升级支持
✅ 实现错误处理和日志记录
✅ 编写完整的 HTTP 服务器集成测试
✅ 性能测试 (目标: 10000 QPS)
✅ 与 Qt 客户端对接测试
```

#### Day 22-28: 认证授权系统 (🚀 P1)

**模块路径**: `include/common/auth/` 和 `src/common/auth/`

##### 7.1 JWT管理核心类

```cpp
// include/common/auth/jwt_manager.h
class JwtManager {
public:
    struct Config {
        std::string secret_key;
        std::string issuer = "game-server";
        std::chrono::seconds access_token_lifetime{3600};    // 1小时
        std::chrono::seconds refresh_token_lifetime{604800}; // 7天
        std::string algorithm = "HS256";
    };

    explicit JwtManager(Config config);

    // 令牌生成
    [[nodiscard]] std::expected<std::string, std::string>
    generate_access_token(const UserClaims& claims);

    [[nodiscard]] std::expected<std::string, std::string>
    generate_refresh_token(const UserClaims& claims);

    // 令牌验证
    [[nodiscard]] std::expected<UserClaims, std::string>
    verify_access_token(std::string_view token);

    [[nodiscard]] std::expected<UserClaims, std::string>
    verify_refresh_token(std::string_view token);

    // 令牌刷新
    [[nodiscard]] std::expected<std::pair<std::string, std::string>, std::string>
    refresh_tokens(std::string_view refresh_token);

    // 令牌撤销
    void revoke_token(std::string_view token);
    [[nodiscard]] bool is_token_revoked(std::string_view token) const;

private:
    Config config_;
    std::unordered_set<std::string> revoked_tokens_;
    mutable std::shared_mutex revoked_tokens_mutex_;

    [[nodiscard]] std::string create_token(const UserClaims& claims, std::chrono::seconds lifetime);
    [[nodiscard]] std::expected<UserClaims, std::string> verify_token(std::string_view token);
};

// include/common/auth/user_claims.h
struct UserClaims {
    std::string user_id;
    std::string username;
    std::string email;
    std::vector<std::string> roles;
    std::vector<std::string> permissions;
    std::unordered_map<std::string, std::string> custom_claims;

    // JWT标准声明
    std::string issuer;
    std::string subject;
    std::string audience;
    std::chrono::system_clock::time_point issued_at;
    std::chrono::system_clock::time_point expires_at;
    std::chrono::system_clock::time_point not_before;
    std::string jti;  // JWT ID

    // 序列化
    [[nodiscard]] nlohmann::json to_json() const;
    [[nodiscard]] static std::expected<UserClaims, std::string> from_json(const nlohmann::json& json);

    // 权限检查
    [[nodiscard]] bool has_role(std::string_view role) const;
    [[nodiscard]] bool has_permission(std::string_view permission) const;
    [[nodiscard]] bool has_any_role(const std::vector<std::string>& roles) const;
    [[nodiscard]] bool has_all_permissions(const std::vector<std::string>& permissions) const;
};

// include/common/auth/password_manager.h
class PasswordManager {
public:
    struct Config {
        std::string algorithm = "bcrypt";
        int cost_factor = 12;  // bcrypt cost
        size_t salt_length = 16;
        size_t min_password_length = 8;
        bool require_uppercase = true;
        bool require_lowercase = true;
        bool require_digits = true;
        bool require_special_chars = true;
    };

    explicit PasswordManager(Config config = {});

    // 密码哈希
    [[nodiscard]] std::expected<std::string, std::string> hash_password(std::string_view password);

    // 密码验证
    [[nodiscard]] std::expected<bool, std::string> verify_password(std::string_view password, std::string_view hash);

    // 密码强度检查
    [[nodiscard]] std::expected<void, std::string> validate_password_strength(std::string_view password);

    // 密码生成
    [[nodiscard]] std::string generate_random_password(size_t length = 16);

private:
    Config config_;

    [[nodiscard]] std::string generate_salt();
    [[nodiscard]] std::expected<std::string, std::string> bcrypt_hash(std::string_view password, std::string_view salt);
    [[nodiscard]] std::expected<bool, std::string> bcrypt_verify(std::string_view password, std::string_view hash);
};

// include/common/auth/auth_service.h
class AuthService {
public:
    struct Config {
        JwtManager::Config jwt_config;
        PasswordManager::Config password_config;
        std::chrono::seconds session_timeout{3600};
        size_t max_login_attempts = 5;
        std::chrono::minutes lockout_duration{15};
        bool enable_two_factor = false;
    };

    explicit AuthService(Config config,
                        std::shared_ptr<UserRepository> user_repo,
                        std::shared_ptr<SessionRepository> session_repo);

    // 用户注册
    [[nodiscard]] std::expected<std::string, std::string>
    register_user(const UserRegistrationRequest& request);

    // 用户登录
    [[nodiscard]] std::expected<AuthenticationResult, std::string>
    authenticate(const LoginRequest& request);

    // 令牌刷新
    [[nodiscard]] std::expected<AuthenticationResult, std::string>
    refresh_authentication(std::string_view refresh_token);

    // 用户登出
    [[nodiscard]] std::expected<void, std::string> logout(std::string_view access_token);

    // 密码管理
    [[nodiscard]] std::expected<void, std::string>
    change_password(std::string_view user_id, std::string_view old_password, std::string_view new_password);

    [[nodiscard]] std::expected<void, std::string>
    reset_password(std::string_view email);

    // 用户验证
    [[nodiscard]] std::expected<UserClaims, std::string>
    validate_token(std::string_view token);

    // 权限检查
    [[nodiscard]] std::expected<bool, std::string>
    check_permission(std::string_view user_id, std::string_view permission);

    [[nodiscard]] std::expected<bool, std::string>
    check_role(std::string_view user_id, std::string_view role);

private:
    Config config_;
    std::unique_ptr<JwtManager> jwt_manager_;
    std::unique_ptr<PasswordManager> password_manager_;
    std::shared_ptr<UserRepository> user_repository_;
    std::shared_ptr<SessionRepository> session_repository_;

    // 登录尝试跟踪
    struct LoginAttempt {
        size_t attempts = 0;
        std::chrono::system_clock::time_point last_attempt;
        std::chrono::system_clock::time_point locked_until;
    };

    std::unordered_map<std::string, LoginAttempt> login_attempts_;
    mutable std::shared_mutex login_attempts_mutex_;

    [[nodiscard]] bool is_account_locked(std::string_view identifier);
    void record_login_attempt(std::string_view identifier, bool success);
    void clear_login_attempts(std::string_view identifier);
};

// include/common/auth/rbac_manager.h
class RbacManager {
public:
    // 角色管理
    [[nodiscard]] std::expected<void, std::string> create_role(const Role& role);
    [[nodiscard]] std::expected<void, std::string> update_role(const Role& role);
    [[nodiscard]] std::expected<void, std::string> delete_role(std::string_view role_name);
    [[nodiscard]] std::expected<Role, std::string> get_role(std::string_view role_name);
    [[nodiscard]] std::vector<Role> list_roles();

    // 权限管理
    [[nodiscard]] std::expected<void, std::string> create_permission(const Permission& permission);
    [[nodiscard]] std::expected<void, std::string> delete_permission(std::string_view permission_name);
    [[nodiscard]] std::expected<Permission, std::string> get_permission(std::string_view permission_name);
    [[nodiscard]] std::vector<Permission> list_permissions();

    // 角色-权限关联
    [[nodiscard]] std::expected<void, std::string>
    assign_permission_to_role(std::string_view role_name, std::string_view permission_name);

    [[nodiscard]] std::expected<void, std::string>
    revoke_permission_from_role(std::string_view role_name, std::string_view permission_name);

    // 用户-角色关联
    [[nodiscard]] std::expected<void, std::string>
    assign_role_to_user(std::string_view user_id, std::string_view role_name);

    [[nodiscard]] std::expected<void, std::string>
    revoke_role_from_user(std::string_view user_id, std::string_view role_name);

    // 权限检查
    [[nodiscard]] std::expected<bool, std::string>
    user_has_permission(std::string_view user_id, std::string_view permission);

    [[nodiscard]] std::expected<bool, std::string>
    user_has_role(std::string_view user_id, std::string_view role);

    [[nodiscard]] std::expected<std::vector<std::string>, std::string>
    get_user_permissions(std::string_view user_id);

    [[nodiscard]] std::expected<std::vector<std::string>, std::string>
    get_user_roles(std::string_view user_id);

private:
    std::shared_ptr<RoleRepository> role_repository_;
    std::shared_ptr<PermissionRepository> permission_repository_;
    std::shared_ptr<UserRoleRepository> user_role_repository_;

    // 缓存
    mutable std::unordered_map<std::string, std::vector<std::string>> user_permissions_cache_;
    mutable std::unordered_map<std::string, std::vector<std::string>> user_roles_cache_;
    mutable std::shared_mutex cache_mutex_;

    void invalidate_user_cache(std::string_view user_id);
};
```

##### 7.2 具体开发任务

**Day 22-23 任务清单**:
```cpp
✅ 实现 JWT 管理组件
   - UserClaims 结构体 (所有字段和方法)
   - JwtManager 类 (令牌生成、验证、刷新、撤销)
   - JWT 编码解码实现 (使用jwt-cpp库)
   - 令牌撤销黑名单管理
✅ 实现 PasswordManager 类
   - 密码哈希 (bcrypt算法)
   - 密码验证
   - 密码强度检查
   - 随机密码生成
✅ 编写 JWT 和密码管理单元测试
```

**Day 24-25 任务清单**:
```cpp
✅ 实现 AuthService 类
   - 用户注册逻辑
   - 用户登录认证
   - 令牌刷新机制
   - 用户登出处理
   - 密码修改和重置
   - 登录尝试跟踪和账户锁定
✅ 实现用户和会话数据仓库
   - UserRepository 接口和实现
   - SessionRepository 接口和实现
   - 数据库表设计和迁移脚本
✅ 编写认证服务单元测试
```

**Day 26-28 任务清单**:
```cpp
✅ 实现 RBAC 权限管理
   - Role 和 Permission 实体类
   - RbacManager 类 (角色权限管理)
   - RoleRepository 和 PermissionRepository
   - UserRoleRepository 用户角色关联
   - 权限缓存机制
✅ 实现认证中间件
   - AuthenticationMiddleware (令牌验证)
   - AuthorizationMiddleware (权限检查)
   - CORS 中间件
✅ 编写完整的认证授权集成测试
✅ 安全性测试 (令牌伪造、权限绕过等)
✅ 与 HTTP 服务器集成测试
```

## 🎮 第二阶段：核心业务功能 (3-4周)

### 第5周：API网关和游戏服务

#### Day 29-35: API网关实现 (🚀 P1)

**模块路径**: `include/common/gateway/` 和 `src/common/gateway/`

##### 8.1 API网关核心类

```cpp
// include/common/gateway/load_balancer.h
class LoadBalancer {
public:
    enum class Algorithm {
        RoundRobin,
        WeightedRoundRobin,
        LeastConnections,
        WeightedLeastConnections,
        Random,
        IPHash,
        ConsistentHash
    };

    struct ServerInfo {
        std::string id;
        std::string host;
        uint16_t port;
        uint32_t weight = 1;
        std::atomic<uint32_t> active_connections{0};
        std::atomic<bool> healthy{true};
        std::chrono::system_clock::time_point last_health_check;
    };

    explicit LoadBalancer(Algorithm algorithm = Algorithm::RoundRobin);

    // 服务器管理
    void add_server(ServerInfo server);
    void remove_server(std::string_view server_id);
    void update_server_weight(std::string_view server_id, uint32_t weight);
    void mark_server_healthy(std::string_view server_id, bool healthy);

    // 负载均衡
    [[nodiscard]] std::optional<ServerInfo> select_server(std::string_view client_key = "");

    // 连接跟踪
    void on_connection_start(std::string_view server_id);
    void on_connection_end(std::string_view server_id);

    // 统计信息
    [[nodiscard]] std::vector<ServerInfo> get_servers() const;
    [[nodiscard]] size_t healthy_server_count() const;

private:
    Algorithm algorithm_;
    std::vector<ServerInfo> servers_;
    mutable std::shared_mutex servers_mutex_;

    // 算法状态
    std::atomic<size_t> round_robin_index_{0};
    std::unordered_map<std::string, size_t> consistent_hash_ring_;

    [[nodiscard]] std::optional<ServerInfo> round_robin_select();
    [[nodiscard]] std::optional<ServerInfo> weighted_round_robin_select();
    [[nodiscard]] std::optional<ServerInfo> least_connections_select();
    [[nodiscard]] std::optional<ServerInfo> random_select();
    [[nodiscard]] std::optional<ServerInfo> ip_hash_select(std::string_view client_key);
    [[nodiscard]] std::optional<ServerInfo> consistent_hash_select(std::string_view client_key);
};

// include/common/gateway/rate_limiter.h
class RateLimiter {
public:
    enum class Algorithm {
        TokenBucket,
        LeakyBucket,
        FixedWindow,
        SlidingWindow
    };

    struct Config {
        Algorithm algorithm = Algorithm::TokenBucket;
        uint32_t max_requests = 100;
        std::chrono::seconds window_size{60};
        uint32_t burst_size = 10;
        std::chrono::milliseconds refill_interval{100};
    };

    explicit RateLimiter(Config config);

    // 限流检查
    [[nodiscard]] bool is_allowed(std::string_view key);
    [[nodiscard]] bool is_allowed(std::string_view key, uint32_t tokens);

    // 获取限流信息
    struct LimitInfo {
        uint32_t remaining;
        std::chrono::system_clock::time_point reset_time;
        std::chrono::milliseconds retry_after;
    };

    [[nodiscard]] LimitInfo get_limit_info(std::string_view key);

    // 配置更新
    void update_config(const Config& config);

private:
    Config config_;

    struct BucketState {
        std::atomic<uint32_t> tokens;
        std::atomic<std::chrono::steady_clock::time_point> last_refill;
        std::queue<std::chrono::steady_clock::time_point> request_times;
        std::mutex mutex;
    };

    std::unordered_map<std::string, std::unique_ptr<BucketState>> buckets_;
    mutable std::shared_mutex buckets_mutex_;

    [[nodiscard]] bool token_bucket_check(BucketState& bucket, uint32_t tokens);
    [[nodiscard]] bool sliding_window_check(BucketState& bucket, uint32_t tokens);
    void refill_bucket(BucketState& bucket);
    void cleanup_expired_buckets();
};

// include/common/gateway/circuit_breaker.h
class CircuitBreaker {
public:
    enum class State {
        Closed,    // 正常状态
        Open,      // 熔断状态
        HalfOpen   // 半开状态
    };

    struct Config {
        uint32_t failure_threshold = 5;
        uint32_t success_threshold = 3;
        std::chrono::seconds timeout{60};
        std::chrono::seconds window_size{60};
        double failure_rate_threshold = 0.5;  // 50%
        uint32_t min_request_threshold = 10;
    };

    explicit CircuitBreaker(std::string name, Config config = {});

    // 执行保护的调用
    template<typename F>
    [[nodiscard]] std::expected<std::invoke_result_t<F>, std::string> execute(F&& func);

    // 手动状态控制
    void force_open();
    void force_close();
    void reset();

    // 状态查询
    [[nodiscard]] State state() const { return state_.load(); }
    [[nodiscard]] std::string name() const { return name_; }

    // 统计信息
    struct Statistics {
        uint32_t total_requests;
        uint32_t successful_requests;
        uint32_t failed_requests;
        double failure_rate;
        std::chrono::system_clock::time_point last_failure_time;
        std::chrono::system_clock::time_point state_changed_time;
    };

    [[nodiscard]] Statistics get_statistics() const;

private:
    std::string name_;
    Config config_;
    std::atomic<State> state_{State::Closed};

    // 统计数据
    std::atomic<uint32_t> failure_count_{0};
    std::atomic<uint32_t> success_count_{0};
    std::atomic<uint32_t> request_count_{0};
    std::atomic<std::chrono::system_clock::time_point> last_failure_time_;
    std::atomic<std::chrono::system_clock::time_point> state_changed_time_;

    // 滑动窗口
    std::queue<std::pair<std::chrono::steady_clock::time_point, bool>> request_history_;
    mutable std::mutex history_mutex_;

    void record_success();
    void record_failure();
    void update_state();
    [[nodiscard]] bool should_attempt_reset() const;
    [[nodiscard]] double calculate_failure_rate() const;
    void cleanup_old_requests();
};
```

## 📊 开发任务总结

### 完整的类和模块清单

#### 第一阶段：基础设施 (28个核心类)

| 模块 | 核心类 | 开发天数 | 优先级 |
|------|--------|----------|--------|
| **日志系统** | LogMessage, LogSink, ConsoleSink, FileSink, AsyncLogger, LoggerManager | 2天 | 🔥 P0 |
| **配置管理** | ConfigValue, ConfigParser, YamlParser, JsonParser, ConfigManager | 2天 | 🔥 P0 |
| **线程池** | Task, WorkStealingQueue, ThreadPoolExecutor, ScheduledExecutor, ThreadPoolManager | 3天 | 🔥 P0 |
| **网络模块** | ModernSocket, EventLoop, TcpServer, TcpClient | 3天 | 🔥 P0 |
| **数据库** | MySQLConnection, MySQLResultSet, MySQLPreparedStatement, MySQLConnectionPool, RedisConnection, RedisConnectionPool | 4天 | 🔥 P0 |

#### 第二阶段：HTTP和认证 (25个核心类)

| 模块 | 核心类 | 开发天数 | 优先级 |
|------|--------|----------|--------|
| **HTTP基础** | HttpMethod, HttpStatus, HttpHeaders, HttpRequest, HttpResponse | 2天 | 🚀 P1 |
| **HTTP服务器** | HttpRequestParser, HttpResponseSerializer, Route, Router, HttpServer, HttpConnection | 5天 | 🚀 P1 |
| **认证授权** | UserClaims, JwtManager, PasswordManager, AuthService, RbacManager | 7天 | 🚀 P1 |

#### 第三阶段：网关和业务 (15个核心类)

| 模块 | 核心类 | 开发天数 | 优先级 |
|------|--------|----------|--------|
| **API网关** | LoadBalancer, RateLimiter, CircuitBreaker, ApiGateway | 4天 | 🚀 P1 |
| **游戏服务** | GameRoom, GamePlayer, GameSession, GameService, UserService | 3天 | 🚀 P1 |

### 开发里程碑检查点

#### 第1周末检查点
```cpp
// 必须通过的测试
void week1_milestone_test() {
    // 日志系统测试
    auto logger = logger::LoggerManager::instance().create_logger<logger::ConsoleSink>("test", true);
    logger->info("Week 1 milestone test");

    // 配置系统测试
    auto& config = config::ConfigManager::instance();
    assert(config.load_from_string("test: value", "yaml").has_value());
    assert(config.get<std::string>("test") == "value");

    // 线程池测试
    auto pool = std::make_unique<thread_pool::ThreadPoolExecutor>();
    auto future = pool->submit([]() { return 42; });
    assert(future.get() == 42);

    std::cout << "✅ Week 1 milestone passed!" << std::endl;
}
```

#### 第2周末检查点
```cpp
// 必须通过的测试
void week2_milestone_test() {
    // 网络模块测试
    auto socket = std::make_unique<network::ModernSocket>(network::ModernSocket::Type::TCP);
    assert(socket->bind("127.0.0.1", 0).has_value());

    // 数据库连接池测试
    MySQLConnectionPool::Config mysql_config;
    mysql_config.connection_config.host = "localhost";
    mysql_config.connection_config.username = "test";
    mysql_config.connection_config.password = "test";
    mysql_config.connection_config.database = "test_db";

    auto mysql_pool = std::make_unique<MySQLConnectionPool>(mysql_config);
    assert(mysql_pool->initialize().has_value());

    auto conn = mysql_pool->get_connection();
    assert(conn.has_value());

    std::cout << "✅ Week 2 milestone passed!" << std::endl;
}
```

#### 第4周末检查点
```cpp
// 必须通过的测试
void week4_milestone_test() {
    // HTTP服务器测试
    http::HttpServer::Config server_config;
    server_config.port = 8080;

    auto server = std::make_unique<http::HttpServer>(server_config);
    server->get("/test", [](const auto& req, auto& resp) {
        resp.set_json({{"message", "Hello World"}});
    });

    assert(server->start().has_value());

    // 认证系统测试
    auth::JwtManager::Config jwt_config;
    jwt_config.secret_key = "test_secret";

    auto jwt_manager = std::make_unique<auth::JwtManager>(jwt_config);

    auth::UserClaims claims;
    claims.user_id = "test_user";
    claims.username = "testuser";

    auto token = jwt_manager->generate_access_token(claims);
    assert(token.has_value());

    auto verified_claims = jwt_manager->verify_access_token(*token);
    assert(verified_claims.has_value());
    assert(verified_claims->user_id == "test_user");

    std::cout << "✅ Week 4 milestone passed!" << std::endl;
}
```

### 关键开发注意事项

#### 1. 依赖关系管理
```cpp
// 严格按照依赖顺序开发
日志系统 → 配置管理 → 线程池 → 网络模块 → 数据库连接池 → HTTP服务器 → 认证系统 → API网关 → 游戏服务
```

#### 2. 测试驱动开发
```cpp
// 每个类都必须有对应的单元测试
class MyClass {
    // 实现
};

// 对应的测试文件: tests/test_my_class.cpp
TEST_CASE("MyClass basic functionality") {
    MyClass obj;
    // 测试用例
}
```

#### 3. 性能基准要求
```cpp
// 每个模块都有明确的性能目标
struct PerformanceTargets {
    // 日志系统: 100万条日志/秒
    static constexpr uint32_t LOG_THROUGHPUT = 1000000;

    // HTTP服务器: 1万QPS
    static constexpr uint32_t HTTP_QPS = 10000;

    // 数据库连接池: 连接获取 < 1ms
    static constexpr auto DB_CONNECTION_LATENCY = std::chrono::milliseconds{1};

    // 线程池: 100万任务/秒
    static constexpr uint32_t THREAD_POOL_THROUGHPUT = 1000000;
};
```

#### 4. 内存安全要求
```cpp
// 所有类都必须遵循现代C++最佳实践
class ModernClass {
public:
    // 使用智能指针
    std::unique_ptr<Resource> resource_;

    // 使用RAII
    explicit ModernClass(Config config) : config_(std::move(config)) {}

    // 禁止拷贝，允许移动
    ModernClass(const ModernClass&) = delete;
    ModernClass& operator=(const ModernClass&) = delete;
    ModernClass(ModernClass&&) noexcept = default;
    ModernClass& operator=(ModernClass&&) noexcept = default;

    // 使用[[nodiscard]]
    [[nodiscard]] std::expected<Result, Error> operation();

private:
    Config config_;
};
```

## 📞 开发支持

### 技术栈版本要求
- **C++标准**: C++20 (最低) / C++23 (推荐)
- **编译器**: GCC 11+ / Clang 13+ / MSVC 2022+
- **构建系统**: CMake 3.20+
- **测试框架**: Catch2 v3
- **JSON库**: nlohmann/json v3.11+
- **YAML库**: yaml-cpp v0.7+
- **JWT库**: jwt-cpp v0.6+
- **MySQL库**: libmysqlclient 8.0+
- **Redis库**: hiredis v1.0+

### 开发环境配置
```bash
# Ubuntu/Debian
sudo apt-get install build-essential cmake ninja-build
sudo apt-get install libmysqlclient-dev libhiredis-dev libyaml-cpp-dev
sudo apt-get install libssl-dev libcurl4-openssl-dev

# 克隆项目
git clone <project-repo>
cd game-microservice

# 构建
mkdir build && cd build
cmake -GNinja -DCMAKE_BUILD_TYPE=Release ..
ninja

# 运行测试
ctest --output-on-failure
```

---

**© 2024 游戏微服务项目团队 - 详细开发计划 v1.0**

*本文档提供了精确到类级别的开发指导，总计68个核心类，预计13周完成开发。*
```
```
```
